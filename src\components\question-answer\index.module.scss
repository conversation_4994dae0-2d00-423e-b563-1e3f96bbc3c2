/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-12 10:58:46
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-12 12:05:37
 * @FilePath: src/components/question-answer/index.module.scss
 * @Version: 1.0.0
 * @Description: 组件描述
 */
.question-answer-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 140px;
  width: 100%;
  z-index: 1;
  background-color: #FCFCFC;

  flex-grow: 1;
  overflow-y: auto;
  // scroll-behavior: smooth;
  &::-webkit-scrollbar {
    width: 0; /* 隐藏垂直滚动条 */
    height: 0; /* 隐藏水平滚动条 */
  }

  &::-webkit-scrollbar-thumb {
    background-color: transparent;
  }

  .qa-item {
    max-width: 50rem;
    margin: 0 auto;
    padding: 0;
    position: relative;
    line-height: 1.5;

    .question-avatar {
      width: 43px;
      height: 43px;
      position: absolute;
      top: 0;
      left: -4.375rem;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .question-content {
      font-size: 16px;
      line-height: 1.6;
      display: inline-block;
      color: #FFFFFF;
      background-color: #4873FF;
      margin-bottom: 0.5rem;
      padding: 1rem;
      border-radius: 10px;
      white-space: pre-wrap;
      word-break: break-all;
    }

    &.answer {
      color: #333333;
      background: linear-gradient(180deg, rgba(233, 241, 249, 0.8) 0%, rgba(231, 235, 248, 0.8) 100%);
      margin-bottom: 2.5rem;
      border-radius: 10px;
    }

    .answer-avatar {
      width: 43px;
      height: 43px;
      border-radius: 100%;
      overflow: hidden;
      position: absolute;
      top: 0;
      left: -4.375rem;

      img {
        width: 100%;
        height: 100%;
      }
    }

    .answer-content {
      max-width: 100%;
      font-size: 16px;
      line-height: 1.6;
      color: #FFF;
      display: inline-block;
      padding: 1rem;
      word-break: break-all;
      white-space: word-break;

      video {
        width: 100%;
        max-height: 30rem;
      }

    }

    .cursor-effect {
      &::after {
        content: "";
        width: 5px;
        height: 16px;
        background-color: #000000;
        animation: cursor-blink 1s infinite;
        display: inline-block;
      }
    }
  }

  .recomment-list {
    max-width: 50rem;
    margin: 0 auto;

    .recomment-list-title {
      font-size: 18px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.8);
      margin-bottom: 1rem;
    }

    .list-container {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      .recomment-item {
        width: fit-content;
        font-size: 16px;
        color: #4873FF;
        cursor: pointer;
      }
    }
  }
}