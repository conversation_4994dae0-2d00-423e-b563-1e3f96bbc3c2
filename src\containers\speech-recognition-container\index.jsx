/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 11:54:00
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 21:56:31
 * @FilePath: src/containers/speech-recognition-container/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述 语音识别数据容器组件，包含微信端语音识别和h5端语音识别
 */
import SpeechRecognition from '../../components/speech-recognition';
import { configWeChat, startRecordWeChat, stopRecordWeChat } from '@/tools/speech-recognition-wechat';
import {
	connectWebSocket,
	createRecorderManager,
	destroy,
	onClear,
	stopSpeech,
} from '../../tools/speech-recognition-xfyun'; 
import { useEffect, useRef, useState } from 'react';

const STATUS = {
	NORMAL: 'normal',   // 默认状态，当前文本框内为空
	SPEECH: 'speech',   // 语音状态，当前正在语音讲话中
	PAUSED: 'paused',   // 文本框内有已经输入的内容。
};

const SpeechRecognitionContainer = ({ onSend, onClose, loading = false }) => {
	const [currentStatus, setCurrentStatus] = useState(STATUS.NORMAL);
	const [speechText, setSpeechText] = useState('');
	const speechTextRef = useRef(null);
	// const isWeChat = localStorage.getItem('isWeChat') === 'true';
	const isWeChat = typeof window.WeixinJSBridge === 'object' && typeof window.WeixinJSBridge.invoke === 'function';

	useEffect(() => {
		createRecorderManager({ resolve: onFrameRecordedResolve, stop: onStop });

		return () => {
			destroy();
		};
	}, []);

	useEffect(() => {
		speechTextRef.current = speechText;
	}, [speechText]);


	/**
	 * 实时更新语音转换的内容
	 * @param value
	 */
	const onFrameRecordedResolve = (value) => {
		// setSpeechText(value);
	};

	const onStop = (value) => {
		setCurrentStatus(STATUS.PAUSED);
		setSpeechText(value);
	};
	/**
	 * 手动更新文本输入内容
	 * @param e
	 */
	const onTextChangeHandler = (e) => {
		setSpeechText(e.target.value);
	};

	/**
	 * 开始语音转文字
	 */
	const onSpeechHandler = () => {
		if (isWeChat) {
			startRecordWeChat();
			// 更新状态为语音输入状态
			setCurrentStatus(STATUS.SPEECH);
		} else if (window.android) {    // 安卓环境下
			window.android.startAsr();  // 开启语音识别，开启成功后会触发startAsrSuccessCallback回调
		} else {
			connectWebSocket(speechTextRef.current);
			// 更新状态为语音输入状态
			setCurrentStatus(STATUS.SPEECH);
		}
	};
	/**
	 * 取消语音转文字
	 */
	const onCancelSpeechHandler = () => {
		if (isWeChat) {
			// 更新状态为暂停输入状态
			setCurrentStatus(STATUS.PAUSED);
			stopRecordWeChat((text) => {
				setSpeechText(text);
			}, () => {

			});
		} else if (window.android) {
			window.android.stopAsr();
		} else {
			stopSpeech();
		}
	};

	/**
	 * 清空文本
	 */
	const onClearHandler = () => {
		setSpeechText('');
		setCurrentStatus(STATUS.NORMAL);
		onClear();
	};
	/**
	 * 发送文本
	 */
	const onSendHandler = () => {
		onSend(speechText);
		onClose();
	};

	window.startAsrSuccessCallback = () => {    // 安卓回调方法，语音识别开启成功
		// 更新状态为语音输入状态
		setCurrentStatus(STATUS.SPEECH);
	};

	window.getAsrResultCallback = (result) => { // 安卓端语音转换结果   
		setSpeechText(result);
	};

	window.stopAsrSuccessCallback = () => { // 安卓端停止语音输入
		setCurrentStatus(STATUS.PAUSED);
	};

	return (
		<SpeechRecognition
			speechText={speechText}
			currentStatus={currentStatus}
			onTextChange={onTextChangeHandler}
			onSpeech={onSpeechHandler}
			onCancelSpeech={onCancelSpeechHandler}
			onClear={onClearHandler}
			onSend={onSendHandler}
			onClose={onClose}
			loading={loading}
		/>
	);
};

export default SpeechRecognitionContainer;