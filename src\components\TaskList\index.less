.task-list-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;

  .task-list-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background-color: #fff;
    position: relative;
    flex-shrink: 0;

    // 返回按钮（左侧）
    .back-arrow {
      position: absolute;
      left: 16px;
      font-size: 20px;
      color: #333;
      cursor: pointer;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .header-icon {
      position: absolute;
      left: 16px;
      font-size: 20px;
      color: #333;
      cursor: pointer;
      width: 17px;
      height: 17px;
      display: flex;
      align-items: center;
      justify-content: center;
  }

    // 下拉菜单样式
    .header-dropdown {
      position: relative;

      .dropdown-trigger {
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.2s ease;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        .dropdown-title {
          font-size: 18px;
          font-weight: 600;
          color: #333;
          margin-right: 8px;
        }

        .dropdown-arrow {
          font-size: 12px;
          color: #999;
          transition: transform 0.2s ease;

          &.open {
            transform: rotate(180deg);
          }
        }
      }

      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        min-width: 120px;
        overflow: hidden;
        margin-top: 4px;

        .dropdown-item {
          padding: 12px 16px;
          font-size: 16px;
          color: #333;
          cursor: pointer;
          transition: background-color 0.2s ease;
          text-align: center;

          &:hover {
            background-color: #f5f5f5;
          }

          &.active {
            background-color: #e6f7ff;
            color: #1890ff;
            font-weight: 500;
          }

          &:not(:last-child) {
            border-bottom: 1px solid #f0f0f0;
          }
        }
      }
    }
  }



  // 新增任务按钮
  .new-task-section {
    padding: 15px;
    background-color: #f0f2f5;
    

    .new-task-btn {
      width: 100%;
      background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
      border-radius: 22px 22px 22px 22px;
      border: none;
      color: white;
      font-size: 16px;
      font-weight: 500;
      padding: 14px 0;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 4px 12px rgba(72, 115, 255, 0.3);

      &:hover {
        background: linear-gradient(135deg, #3566e0 0%, #4873FF 100%);
        box-shadow: 0 6px 16px rgba(72, 115, 255, 0.4);
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  .task-list-content {
    flex: 1;
    padding: 0 16px 16px 16px;
    overflow-y: auto;

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #999;
      font-size: 16px;
    }

    .empty-state {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 200px;
      color: #999;
      font-size: 16px;
    }

    .task-item {
      background-color: #fff;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      border-left: 4px solid transparent;
      position: relative;

      // 根据状态设置左边框颜色
      &.status-in-progress {
        border-left-color: rgba(0, 181, 120, 1);
        background: linear-gradient( 180deg, #FFFFFF 0%, #EBF6FF 99%);
        box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
      }

      &.status-completed {
        border-left-color: #D9D9D9;
      }

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .task-header {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        margin-bottom: 12px;
        
        .task-title {
          font-weight: 500;
          color: #333;
          flex: 1;
          margin-right: 12px;
          line-height: 1.4;
          max-width: 85%;
          font-family: AlibabaPuHuiTi, AlibabaPuHuiTi;
          font-size: 17px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(0,0,0,0.8);
          line-height: 26px;
          text-align: left;
          font-style: normal;
          text-transform: none;
          font-weight: 800;
        }

        .task-status {
          padding: 4px 12px;
          border-radius: 0px 7px 0px 7px;
          font-size: 12px;
          font-weight: 400;
          white-space: nowrap;
          text-align: center;
          position: absolute;
          right: 0;
          top: 0;

          &.status-not-started {
            background: url('../../assets/taskList/bg_yjs.png') no-repeat center center;
            color: #fff;
          }

          &.status-in-progress {
            background: url('../../assets/taskList/bg_jxz.png') no-repeat center center;
            color: #fff;
            
          }

          &.status-completed {
            background: url('../../assets/taskList/bg_yjs.png') no-repeat center center;
            color: #fff;
          }
        }
      }
      .line{
        width: 100%;
        height: 1px;
        background-color: #CAD9FA;
        margin: 12px 0;
      }
      .task-progress {
        .progress-text {
          font-size: 14px;
          color: #666;
          line-height: 1.4;

          

          // 完成度百分比样式
          .progress-percentage {
            // font-weight: 600;

            &.progress-low {
              color: #FF4D4F;
            }

            &.progress-medium {
              color: #FA8C16;
            }

            &.progress-high {
              color: #52C41A;
            }
          }
          .bolb{
            font-weight: 800;
          }

          // 数字高亮
          .highlight-number {
            color: #FF4D4F;
            font-weight: 500;
          }
        }
      }
    }
    // 最后一个任务项\
    .task-item:last-child{
      margin-bottom: 120px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .task-list-container {
    .task-list-header {
      padding: 12px;

      .type-selector {
        margin-right: 8px;
      }

      .new-task-btn {
        font-size: 12px;
        padding: 6px 12px;
        height: 32px;
      }
    }

    .task-list-content {
      padding: 0 12px 12px 12px;

      .task-item {
        padding: 12px;

        .task-header {
          .task-title {
            font-size: 16px;
          }

          .task-status {
            font-size: 11px;
            padding: 3px 8px;
          }
        }

        .task-progress {
          .progress-text {
            font-size: 13px;
          }
        }
      }
    }
  }
}
