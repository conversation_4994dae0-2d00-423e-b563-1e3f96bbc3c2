.inline-chat {
  display: flex;
  flex-direction: column;
  // 使用动态视口高度，支持键盘适配
  height: calc(var(--viewport-height, 100vh) - 200px);
  background: #FFFFFF;
  overflow: hidden;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  transition: height 0.3s ease, padding-bottom 0.3s ease;

  // 键盘可见时的样式
  &.keyboard-visible {
    .messages-container {
      transition: margin-bottom 0.3s ease;
      // 确保消息不被键盘覆盖
      max-height: calc(100% - 120px);
    }
  }

  .chat-header {
    background: linear-gradient(135deg, #4873FF 0%, #6C5CE7 100%);
    color: white;
    padding: 16px;

    .chat-title {
      font-size: 18px;
      font-weight: 600;
      margin: 0 0 12px 0;
      text-align: center;
    }

    .progress-section {
      .progress-steps {
        display: flex;
        justify-content: space-between;
        margin-bottom: 12px;

        .step-item {
          display: flex;
          flex-direction: column;
          align-items: center;
          flex: 1;
          position: relative;

          &:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 12px;
            right: -50%;
            width: 100%;
            height: 2px;
            background: rgba(255, 255, 255, 0.3);
            z-index: 1;
          }

          &.active:not(:last-child)::after {
            background: rgba(255, 255, 255, 0.8);
          }

          .step-circle {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            position: relative;
            z-index: 2;

            &.active {
              background: rgba(255, 255, 255, 0.9);
              color: #4873FF;
            }

            &.current {
              background: white;
              color: #4873FF;
              box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.5);
            }
          }

          .step-label {
            font-size: 10px;
            text-align: center;
            opacity: 0.8;
            line-height: 1.2;
          }

          &.active .step-label {
            opacity: 1;
            font-weight: 500;
          }
        }
      }

      .progress-bar {
        margin-top: 8px;
      }
    }
  }

  .messages-container {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    // 为软键盘预留空间
    padding-bottom: calc(16px + env(keyboard-inset-height, 0px));

    .message-item {
      display: flex;
      
      &.user {
        justify-content: flex-end;
      }

      &.assistant {
        justify-content: flex-start;
      }

      .message-bubble {
        max-width: 70%;
        padding: 12px 16px;
        border-radius: 18px;
        font-size: 16px;
        line-height: 1.4;
        word-break: break-word;

        &.user-bubble {
          background: rgba(227, 239, 255, 1);
          color: rgba(0, 0, 0, 0.80);
          border-radius: 18px 18px 4px 18px;
          .markdown-content{
            color: rgba(0, 0, 0, 0.80);
          }

          // 用户消息中的图片样式
          .message-images {
            margin-bottom: 8px;

            .message-image-item {
              margin-bottom: 8px;

              &:last-child {
                margin-bottom: 0;
              }

              .message-image {
                width: 100%;
                max-width: 200px;
                height: auto;
                border-radius: 8px;
                display: block;
              }

              .image-info {
                margin-top: 4px;
                font-size: 12px;
                opacity: 0.8;

                .check-item-name {
                  background: rgba(0, 0, 0, 0.2);
                  padding: 2px 6px;
                  border-radius: 4px;
                  font-size: 11px;
                }
              }
            }
          }
        }

        &.assistant-bubble {
          background:rgba(245, 245, 245, 1);
          color: #333;
          border-radius: 18px 18px 18px 4px;
          box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

          // 为answer-content组件调整样式
          :global(.answer-content) {
            padding: 0;
            background: transparent;

            // 调整插件内容的样式
            .plugins-list {
              margin-top: 8px;
            }

            // 调整视频插件样式
            .video-plugin {
              video {
                max-width: 100%;
                border-radius: 8px;
              }
            }

            // 调整图片预览样式
            .fire-inspection-list {
              .ant-image {
                border-radius: 6px;
              }
            }

            // 调整文件下载样式
            .plugin-item {
              font-size: 13px;
              padding: 6px 10px;
              margin: 4px 0;
            }
          }
        }
      }
    }

    // 补充附件按钮样式
    .supplement-attachment-btn-container {
      margin-top: 8px;
      display: flex;
      justify-content: flex-start;

      .supplement-attachment-btn {
        background: #D9E1F8;
        padding: 8px 16px;
        border-radius: 8px;
        font-size: 15px;
        cursor: pointer;
        border: none;
        color: rgba(72, 115, 255, 1);
        transition: all 0.3s;

        &:hover {
          background: #1890ff;
          color: white;
        }

        &:active {
          transform: scale(0.98);
        }
      }
    }

    .typing-indicator {
      display: flex;
      gap: 4px;
      align-items: center;

      span {
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background: #999;
        animation: typing 1.4s infinite ease-in-out;

        &:nth-child(1) {
          animation-delay: -0.32s;
        }

        &:nth-child(2) {
          animation-delay: -0.16s;
        }
      }
    }
  }

  // 待发送图片预览区域
  .pending-images-preview {
    background: white;
    border-top: 1px solid #f0f0f0;
    padding: 12px 16px;

    .pending-images-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;

      .pending-images-title {
        font-size: 14px;
        font-weight: 500;
        color: #333;
      }

      .clear-all-btn {
        font-size: 12px;
        color: #999;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: #f5f5f5;
          color: #666;
        }
      }
    }

    .pending-images-list {
      display: flex;
      gap: 8px;
      padding-bottom: 4px;

      .pending-image-item {
        position: relative;
        flex-shrink: 0;

        .pending-image {
          width: 60px;
          height: 60px;
          object-fit: cover;
          border-radius: 6px;
          border: 2px solid #4873FF;
        }

        .pending-image-info {
          position: absolute;
          bottom: -2px;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          font-size: 10px;
          padding: 2px 4px;
          border-radius: 0 0 4px 4px;
          text-align: center;

          .check-item-name {
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .remove-image-btn {
          position: absolute;
          top: -5px;
          right: -6px;
          width: 18px;
          height: 18px;
          background: #ff4d4f;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          cursor: pointer;
          line-height: 1;
          transition: all 0.2s ease;

          &:hover {
            background: #ff7875;
            transform: scale(1.1);
          }
        }
      }
    }
  }

  .chat-input {
    background: white;
    padding: 12px 16px;
    border-top: 1px solid #f0f0f0;

    .input-container {
      .input-wrapper {
        display: flex;
        align-items: flex-end;
        gap: 8px;
        background: #f8f9fa;
        border-radius: 20px;
        padding: 8px 12px;

        .voice-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:active {
            transform: scale(0.95);
          }

          svg {
            font-size: 18px;
          }
        }

        .text-input {
          flex: 1;
          border: none;
          background: transparent;
          resize: none;
          outline: none;
          font-size: 14px;
          line-height: 1.4;

          &::placeholder {
            color: #999;
          }
        }

        .voice-input {
          flex: 1;
          padding: 8px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;
          color: #999;
          font-size: 14px;
          cursor: pointer;
          user-select: none;
          border-radius: 16px;
          transition: all 0.3s ease;

          &.recording {
            background: rgba(72, 115, 255, 0.1);
            color: #4873FF;
            
            span {
              animation: pulse 1s infinite;
            }
          }

          &:active {
            transform: scale(0.98);
          }

          svg {
            font-size: 16px;
          }
        }

        .send-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 32px;
          height: 32px;
          border-radius: 50%;
          background: #e0e0e0;
          cursor: pointer;
          transition: all 0.3s ease;

          &.active {
            background: #4873FF;
            color: white;
          }

          &:active {
            transform: scale(0.95);
          }

          svg {
            font-size: 16px;
          }
        }
      }

      .char-count {
        text-align: right;
        font-size: 12px;
        color: #999;
        margin-top: 4px;
        padding-right: 4px;
      }
    }
  }
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.4;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

// 全局样式：键盘可见时的处理
:global(body.keyboard-visible) {
  // 防止页面滚动时的抖动
  .inline-chat {
    .messages-container {
      // 为键盘预留空间，iOS设备减少预留空间
      padding-bottom: calc(var(--keyboard-height, 0px) + 20px);

      // iOS设备特殊处理
      @supports (-webkit-touch-callout: none) {
        padding-bottom: calc(var(--keyboard-height, 0px) * 0.3 + 20px);
      }
    }
  }

  // 输入框容器的调整
  .text-input-wrapper {
    // 确保输入框不被键盘覆盖，iOS设备减少调整幅度
    transform: translateY(calc(-1 * var(--keyboard-height, 0px) / 3));
    transition: transform 0.3s ease;

    // iOS设备更保守的调整
    @supports (-webkit-touch-callout: none) {
      transform: translateY(calc(-1 * var(--keyboard-height, 0px) / 6));
    }
  }
}
