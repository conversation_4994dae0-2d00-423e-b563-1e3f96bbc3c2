/**
 * 安卓相机工具类
 * 用于处理安卓壳程序中的相机功能
 */

export interface AndroidCameraOptions {
  quality?: number; // 图片质量 0-1
  maxWidth?: number; // 最大宽度
  maxHeight?: number; // 最大高度
  outputFormat?: 'base64' | 'file'; // 输出格式
}

export interface AndroidCameraResult {
  success: boolean;
  data?: string; // base64 或文件路径
  error?: string;
}

export class AndroidCameraManager {
  private static instance: AndroidCameraManager;
  private callbacks: Map<string, (result: AndroidCameraResult) => void> = new Map();

  private constructor() {
    this.setupGlobalCallbacks();
  }

  public static getInstance(): AndroidCameraManager {
    if (!AndroidCameraManager.instance) {
      AndroidCameraManager.instance = new AndroidCameraManager();
    }
    return AndroidCameraManager.instance;
  }

  /**
   * 设置全局回调函数
   */
  private setupGlobalCallbacks() {
    // 成功回调
    (window as any).androidCameraSuccess = (data: string) => {
      this.handleCameraResult({ success: true, data });
    };

    // 失败回调
    (window as any).androidCameraError = (error: string) => {
      this.handleCameraResult({ success: false, error });
    };

    // 取消回调
    (window as any).androidCameraCancel = () => {
      this.handleCameraResult({ success: false, error: 'User cancelled' });
    };
  }

  /**
   * 处理相机结果
   */
  private handleCameraResult(result: AndroidCameraResult) {
    this.callbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('Camera callback error:', error);
      }
    });
    this.callbacks.clear();
  }

  /**
   * 检查是否支持安卓相机
   */
  public isSupported(): boolean {
    return !!(window as any).android && 
           typeof (window as any).android.startCamera === 'function';
  }

  /**
   * 启动相机
   */
  public async startCamera(options: AndroidCameraOptions = {}): Promise<AndroidCameraResult> {
    return new Promise((resolve, reject) => {
      if (!this.isSupported()) {
        reject(new Error('Android camera not supported'));
        return;
      }

      // 生成唯一ID
      const callbackId = Date.now().toString();
      
      // 注册回调
      this.callbacks.set(callbackId, resolve);

      // 设置超时
      const timeout = setTimeout(() => {
        this.callbacks.delete(callbackId);
        reject(new Error('Camera timeout'));
      }, 30000); // 30秒超时

      try {
        // 调用安卓相机
        const params = {
          quality: options.quality || 0.8,
          maxWidth: options.maxWidth || 1200,
          maxHeight: options.maxHeight || 1200,
          outputFormat: options.outputFormat || 'base64'
        };

        if ((window as any).android.startCameraWithOptions) {
          // 支持参数的版本
          (window as any).android.startCameraWithOptions(JSON.stringify(params));
        } else {
          // 基础版本
          (window as any).android.startCamera();
        }

        // 清除超时
        clearTimeout(timeout);
      } catch (error) {
        clearTimeout(timeout);
        this.callbacks.delete(callbackId);
        reject(error);
      }
    });
  }

  /**
   * 清理资源
   */
  public cleanup() {
    this.callbacks.clear();
    delete (window as any).androidCameraSuccess;
    delete (window as any).androidCameraError;
    delete (window as any).androidCameraCancel;
  }
}

/**
 * 便捷函数：启动安卓相机
 */
export const startAndroidCamera = async (options?: AndroidCameraOptions): Promise<AndroidCameraResult> => {
  const manager = AndroidCameraManager.getInstance();
  return manager.startCamera(options);
};

/**
 * 便捷函数：检查安卓相机支持
 */
export const isAndroidCameraSupported = (): boolean => {
  const manager = AndroidCameraManager.getInstance();
  return manager.isSupported();
};
