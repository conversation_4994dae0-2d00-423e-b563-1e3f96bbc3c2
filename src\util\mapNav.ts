/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-05 12:08:47
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-11 19:27:12
 * @FilePath: \react-h5-template\src\util\mapNav.Ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 定义接口
interface MapNavOptions {
    type?: 'gd' | 'tx' | 'bd'; // 地图类型
    point: {
        lat: number; // 纬度
        lng: number; // 经度
    };
    key?: string; // 对应开发者平台的key
    pointname?: string; // 地点名称
}

// 定义一个常量来存储默认配置
const defaultOption: MapNavOptions = {
    type: 'gd',
    point: {
        lat: 0,
        lng: 0
    },
    key: '',
    pointname: '终点'
};

/**
 * 每个地图软件配置的map 对象
 */
const getSchemeUrl = (option: MapNavOptions): string => {
    const urlObj = {
        'gd':{
            'android': `http://uri.amap.com/marker?position=${option.point.lat},${option.point.lng}&name=${option.pointname}&coordinate=gaode&callnative=1`,
            'ios': `http://uri.amap.com/marker?position=${option.point.lat},${option.point.lng}&name=${option.pointname}&coordinate=gaode&callnative=1`
        },
        'bd': {
            'android': `http://api.map.baidu.com/marker?location=${option.point.lng},${option.point.lat}}&title=${option.pointname}&content=${option.pointname}&output=html`,
            'ios': `http://api.map.baidu.com/marker?location=${option.point.lng},${option.point.lat}&title=${option.pointname}&content=${option.pointname}&output=html`
        },
        
        'tx': {
            'android': `https://apis.map.qq.com/uri/v1/marker?marker=coord:${option.point.lng},${option.point.lat};title:${option.pointname};addr:手帕口桥北铁路道口&referer=NETBZ-TPMWI-DMFGV-UGF37-IW3Q7-UAFTB`,
            'ios': `https://apis.map.qq.com/uri/v1/marker?marker=coord:${option.point.lng},${option.point.lat};title:${option.pointname};addr:手帕口桥北铁路道口&referer=NETBZ-TPMWI-DMFGV-UGF37-IW3Q7-UAFTB`
        }
    };

    // 使用三元运算符来简化判断
    const platform = (navigator.userAgent.indexOf('Android') > -1 || navigator.userAgent.indexOf('Linux') > -1) ? "android" : "ios";
    
    return urlObj[option.type!][platform];
};

// 打开app的函数
const isError = (): Promise<void> => {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        const t = setInterval(() => {
            const endTime = Date.now();
            // 如果没有超时
            if (endTime - startTime < 4010) {
                resolve();
            } else {
                clearInterval(t);
            }
        }, 4000);
    });
}

/**
 * 外部暴露方法
 * @param {object} option - 配置参数
 * @param {string} option.type - 地图类型 gd 高德，tx 腾讯，bd 百度
 * @param {object} option.point - 经度和纬度 { lat: 104.06, lng:  30.67} 
 * @param {string} option.key - 对应开发者平台的key，没有就不传
 * @param {string} option.pointname - 地点名称
 */
export function mapNav(option: MapNavOptions): void {
    // 使用对象展开运算符和默认值合并参数
    const finalOption = { ...defaultOption, ...option };
    
    // 跳转的schemeUrl
    const schemeUrl = getSchemeUrl(finalOption);
    console.log(schemeUrl);
    
    window.open(schemeUrl);

    // 这里原来的注释掉的逻辑已移除
    // isError().then(() => {
    //     let ifr = document.createElement('iframe')
    //     ifr.src = schemeUrl;
    //     ifr.style.display = 'none';
    //     document.body.appendChild(ifr);
    // });
}
