/**
 * 微信 JS Bridge 全局类型声明
 */

interface WeixinJSBridgeMiniProgramConfig {
  url: string;
  success?: (res: any) => void;
  fail?: (res: any) => void;
  complete?: (res: any) => void;
}

interface WeixinJSBridgeMiniProgram {
  redirectTo(config: WeixinJSBridgeMiniProgramConfig): void;
  navigateTo(config: WeixinJSBridgeMiniProgramConfig): void;
  navigateBack(config?: { delta?: number }): void;
  switchTab(config: WeixinJSBridgeMiniProgramConfig): void;
  reLaunch(config: WeixinJSBridgeMiniProgramConfig): void;
}

interface WeixinJSBridgeInterface {
  invoke(api: string, params: any, callback?: (res: any) => void): void;
  on(event: string, callback: (res: any) => void): void;
  miniProgram: WeixinJSBridgeMiniProgram;
}

declare global {
  interface Window {
    WeixinJSBridge: WeixinJSBridgeInterface;
  }
  
  const <PERSON><PERSON>J<PERSON><PERSON>: WeixinJSBridgeInterface;
}

export {};
