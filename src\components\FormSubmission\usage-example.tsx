/*
 * @Description: FormSubmission 组件使用示例
 * @Author: AI Assistant
 * @Date: 2025-07-22
 */
import React, { useRef } from 'react';
import { Button } from 'antd-mobile';
import FormSubmission, { FormSubmissionRef } from './index';

const FormSubmissionExample: React.FC = () => {
  // 创建 ref
  const formSubmissionRef = useRef<FormSubmissionRef>(null);

  // 模拟表单数据
  const mockFormItems = [
    {
      id: '1',
      config: {
        reqName: '消防设备是否正常',
        outputType: 'SINGLE_CHOICE',
        outputContent: '"是"；"否"'
      }
    },
    {
      id: '2',
      config: {
        reqName: '设备数量',
        outputType: 'TEXT',
        outputContent: ''
      }
    }
  ];

  // 处理表单提交
  const handleFormSubmit = (data: any) => {
    console.log('表单提交数据:', data);
    // 这里可以调用 API 提交数据
  };

  // 外部触发提交
  const handleExternalSubmit = () => {
    if (formSubmissionRef.current) {
      // 调用组件内部的 handleSubmit 方法
      formSubmissionRef.current.handleSubmit();
    }
  };

  return (
    <div>
      <h2>FormSubmission 使用示例</h2>
      
      {/* 使用带 ref 的 FormSubmission 组件 */}
      <FormSubmission
        ref={formSubmissionRef}
        formItems={mockFormItems}
        onSubmit={handleFormSubmit}
      />
      
      {/* 外部提交按钮 */}
      <div style={{ padding: '20px', borderTop: '1px solid #eee' }}>
        <Button 
          color="primary" 
          onClick={handleExternalSubmit}
          block
        >
          外部触发提交
        </Button>
      </div>
    </div>
  );
};

export default FormSubmissionExample;
