/*
 * @Description: 
 * @Author: wangliang
 * @Date: 2025-01-02 15:58:23
 * @LastEditTime: 2025-06-20 18:55:40
 * @LastEditors: wl
 */
import { Spin, Tree, message, Image } from "antd"
import { SyncOutlined } from '@ant-design/icons';
import { isForbidPlay, transformTreeData, getGin, openFilePreview } from "@/util/util"
import axios from 'axios';
import { apis, projectPath } from "@/api/api"
import InfomationSource from "@/components/infomation-source";
import MarkdownContent from "@/components/markdown-content";
import WebsearchContent from "../websearch-content";
import fileDownloadSvg from "@/assets/images/chat/icon_download_file.svg"
import floderDownloadSvg from "@/assets/images/chat/icon_download_floder.svg"
import style from "./AnswerContent.module.scss"
const AnswerContent = ({ answerItem, questionItem, pluginsLoading, loading, sceneData, isLast }) => {

    const openPayModal = () => {

    }

    const onDownloadFile = (file) => {
        openFilePreview(`/file-preview?file_url=${file.remotePath}&file_name=${file.name}`)
    }

    const onPreview = (item, key) => {
        openFilePreview(`/file-preview?file_url=${item[key]}&file_name=${item.name}`)
    }

    const getLedgerTree = () => {
        const item = answerItem.chatPlugins ? answerItem.chatPlugins[0] : {}
        if(item.type === 'list') {
            const list = [JSON.parse(item.extra)]
            return <Tree defaultExpandedKeys={['0-0']} treeData={transformTreeData(list, '0')} titleRender={customTitleRender} showIcon/>
        }else if(item.type === 'doc') {
            const list = JSON.parse(item.extra).details || []
            if(!!list.length) {
                if(list.length === 1) {
                    return <>
                        <div className={style['plugins-list-title']}>为您生成详细文件</div>
                        <Tree defaultExpandAll treeData={transformTreeData(list, '0')} titleRender={customTitleRender} showIcon/>
                    </>
                }else {
                    return <>
                        <div className={style['plugins-list-title']}>为您生成详细文件</div>
                        <Tree defaultExpandAll treeData={transformTreeData(list.slice(0, 1), '0')} titleRender={customTitleRender} showIcon/>
                        <div className={style['plugins-list-title']}>同时为您生成了可能感兴趣的文件</div>
                        <Tree defaultExpandAll treeData={transformTreeData(list.slice(1), '0')} titleRender={customTitleRender} showIcon/>
                    </>
                }
            }else {
                return ;
            }
        }else {
            return item.content
        }
        
    }

    const customTitleRender = (node) => {
        if(node.fileUrl) {
            return <div className={style['ledger-tree-item']} onClick={(e)=>onLedgerSelect(e, node)}>
                <span>{node.title}</span>
                {
                    node.detailId ? <img src={fileDownloadSvg} alt="" title="点击预览"/> : <img src={floderDownloadSvg} alt="" title="点击下载"/>
                }
            </div>
        }else {
            return <span>{node.title}</span>
        }
    }

    const onLedgerSelect = (e, node) => {
        e.stopPropagation()
        e.preventDefault()
        const fileUrl = node?.fileUrl
        const fileName = node?.fileName || node?.name

        if(node.detailId) {
            const origin = window.location.origin
            window.open(`${origin}/file-preview?file_url=${fileUrl}&file_name=${fileName}`)
        }else {
            const gin = getGin()
            const _url = `${projectPath}/upload/downLoadByUrl?url=${fileUrl}`
            axios.get(
                _url, 
                {
                    responseType: 'blob',
                    headers: {
                        "Authorization": `Bearer ${gin.accessToken}`
                    },
                })
            .then(response => {
                const fileTypeObj = {
                    'zip': 'application/zip',
                    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                    'pdf': 'application/pdf'
                }
                const fileType = fileUrl.slice((fileUrl.lastIndexOf(".") - 1 >>> 0) + 2)
                const blob = new Blob([response.data], {type: fileTypeObj[fileType] || 'application/octet-stream'});
                const url = URL.createObjectURL(blob);
                const fileName = node?.fileName ||  (node?.name + '.' + fileType)
                const a = document.createElement('a');
                a.href = url;
                a.download = fileName;
                a.style.display = 'none';

                // 触发下载
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);

                // 清理URL
                URL.revokeObjectURL(url);

                message.success('下载完成')
            }).catch(() => {
            })
        }
    }

    const videoPluginContainer = (plugin) => {
        const obj = JSON.parse(plugin.extra) || {}
        return <div className={style['video-plugin']}>
            {
                obj.bestMatch ? <>
                    {/* <div>为您推荐最匹配的视频：</div>
                    <ul>
                        <li className={style['video-plugin-info']}>标题：{obj.bestMatch.title}</li>
                        <li className={style['video-plugin-info']}>内容简介：{obj.bestMatch.content}</li>
                        <li className={style['video-plugin-info']}>视频播放：</li>
                    </ul> */}
                    <video controls controlsList="nodownload" style={{marginTop: '0.5rem'}}>
                        <source src={obj.bestMatch.remotePath} />
                    </video>
                </> : ''
                // <div className={style['video-best-empty']}>很抱歉，经过仔细查找，我没有找到您需求相关的视频资料。</div>
            }
            {
                obj.recommendations && !!obj.recommendations.length && <div className={style['video-recommend']}>
                    <div className={style['video-recommend-title']}>
                        搜索到相近主题视频，您可能感兴趣：
                    </div>
                    <div className={style['video-recommend-list']}>
                        {
                            obj.recommendations.map((item, index) => {
                                return <div className={style['video-recommend-item']}>
                                    <div className={style['video-recommend-item-title']} title={`${index+1}、${item.title}`}>{index+1}、{item.title}</div>
                                    <video controls controlsList="nodownload">
                                        <source src={item.remotePath} />
                                    </video>
                                </div>
                            })
                        }
                    </div>
                    <div className={style['video-recommend-btn']}>
                        {obj.recommendations.length}篇资料
                    </div>
            </div>
            }
        </div>
    }

    const checkListPluginContainer = (plugin) => {
        let obj = {}
        try {
            if(plugin.content.includes('|')) {
                // 兼容旧数据
                obj.best_match = [
                    {
                        name: plugin.name.split('|').slice(0, 1),
                        remotePath: plugin.content.split('|').slice(0, 1)
                    }
                ]
                let recommendations = []
                plugin.name.split('|').slice(1).map((l, i) => {
                    recommendations.push({
                        name: l,
                        remotePath: plugin.content.split('|')[i+1]
    
                    })
                })
                obj.recommendations = recommendations
            }else {
                obj = JSON.parse(plugin.content) || {}
            }
        }catch {
            obj = {
                best_match: [],
                recommendations: []
            }
        }
        
        return <div className={style['check-list-plugin']}>
            {
                obj.best_match && !!obj.best_match.length && <>
                    <div className={style['plugins-list-title']}>
                    已为您生成详细的表格模版，可点击预览
                    </div>
                    <div className={style['list-container']}>
                        {
                            obj.best_match.map(item => {
                                return <div key={item.remotePath} className={style['plugin-item']} onClick={() => onDownloadFile(item)}>
                                    {item.name}
                                </div>
                            })
                        }
                    </div>
                </>
            }
            
            {
                obj?.recommendations && !!obj?.recommendations.length && <>
                    <div className={style['plugins-list-title']}>同时为您生成了可能感兴趣的表格模版</div>
                    <div className={style['list-container']}>
                        {
                            obj?.recommendations.map(item => {
                                return <div key={item.remotePath} className={style['plugin-item']} onClick={() => onDownloadFile(item)}>
                                    {item.name}
                                </div>
                            })
                        }
                    </div>
                </>
            }
        </div>
        
    }

    // const riskAnalysisPlugin = (plugin) => {
    //     try {
    //         let obj = JSON.parse(plugin.content)
    //         let content = obj.content
    //         if(content.includes('$imgs')) {
    //             const parts = content.split("$imgs");
    //             return (
    //               <>
    //                 {parts[0]}
    //                 <div className={style['risk-example-list']}>
    //                     <Image.PreviewGroup>
    //                         <Image src={require("@/assets/images/chat/risk_example_2.jpg")} />
    //                         <Image src={require("@/assets/images/chat/risk_example_3.jpg")} />
    //                         <Image src={require("@/assets/images/chat/risk_example_1.jpg")} />
    //                     </Image.PreviewGroup>
    //                 </div>
    //                 {parts[1]}
    //               </>
    //             );
    //         }
    //         return content
    //     }catch (err){
    //         console.log(err);
            
    //         return
    //     }
        
    // }

    const fireInspectionPlugin = (plugin) => {
        try {
            let list = JSON.parse(plugin.extra) || []
            return <div className={style['fire-inspection']}>
                {/* <div>图片列表</div> */}
                <div className={style['fire-inspection-list']}>
                    <Image.PreviewGroup>
                        {
                            list.map((l, i) => {
                                return <div key={l.id} className={style['list-item']}>
                                    <Image src={`${projectPath}/upload/downLoadByUrl?url=${l.fileUrl}`} />
                                    <div className={style['img-idx']}>- {i+1} -</div>
                                </div>
                            })
                        }
                    </Image.PreviewGroup>
                </div>
                
            </div>

        }catch {}
    }

    return <div className={`${style['answer-content']} ${(isLast && loading && (!(answerItem.chatContents[0]?.content || '')) && sceneData.code !== 'gen_paper')  ? style['cursor-effect'] : ''}`}>
        {
            sceneData.code === 'gen_paper' && isLast && !questionItem.chatPlugins && !answerItem.chatContents[0]?.content && <div className={style['paper-loading']}>
                正在为您生成试卷 <Spin indicator={<SyncOutlined spin style={{ color: '#000' }} />} size="small" />
            </div>
        }
        {/* {
            sceneData.code === 'set_exams' && answerItem.chatContents[0]?.content && <div className={style["exam-desc"]}>
                以下题目由AI生成，请审核确认后再使用
            </div>
        } */}
        
        {
            isForbidPlay(answerItem.chatContents[0].content || '') ? <MarkdownContent content={answerItem.chatContents[0].content || ''} parent={null} loading={loading} sceneData={sceneData} /> :
            <div className={style['forbid-play']}>
                非会员无法播放，请<span onClick={openPayModal}>升级</span>后查看
            </div>
        }
        {
            pluginsLoading && isLast && <div className={style['plugins-loading']}>正在生成文件<Spin indicator={<SyncOutlined spin />} /></div>
        }
        {
            (questionItem?.chatSources && !!questionItem?.chatSources.length) && <InfomationSource sourceDocuments={questionItem.chatSources} />
        }
        {
            (questionItem?.chatWebSearches && !!questionItem?.chatWebSearches.length) && 
            <WebsearchContent chatWebSearches={questionItem?.chatWebSearches || []}/>
        }
        {
            (!(/\[(.*?)\]/g).test(answerItem.chatContents[0].content) && questionItem?.chatPlugins && !!questionItem?.chatPlugins.length) && <div className={style["plugins-list"]}>
                {
                    questionItem.chatPlugins?.map((plugin, i) => {
                        return <div key={i}>
                            {
                                ( plugin.status === 7 || plugin.status === 9 )  ? <>{plugin?.content}</> :
                                plugin.pluginsCode.includes('ScooperSearch') ? <>
                                    {videoPluginContainer(plugin)}
                                </> : plugin.pluginsCode.includes('ScooperPaper') ? <>
                                    <div className={style['genPaper-plugin']}>
                                        {plugin.extra ? `围绕${JSON.parse(plugin.extra)?.matchTag.join('、')}等要点，` : ''}生成了一张试卷，点击预览下载：
                                        <div className={style['genPaper-plugin-file']} onClick={() => onPreview(plugin, 'content')}>{plugin.name}</div>
                                    </div>
                                </> : plugin.pluginsCode.includes('ScooperCheckList') ? <>
                                    {checkListPluginContainer(plugin)}
                                </> : plugin.pluginsCode.includes('ScooperLedger') ? <div className={style['ledger-container']}>
                                    {getLedgerTree()}
                                </div> : plugin.pluginsCode.includes('ScooperDangerAnalysis') ? <>
                                    {plugin?.content}
                                {/* </> : plugin.pluginsCode.includes('ScooperRiskAnalysis') ? <>
                                    {riskAnalysisPlugin(plugin)} */}
                                </>: plugin.pluginsCode.includes('ScooperFireInspection') ? <>
                                    {fireInspectionPlugin(plugin)}
                                </> :
                                <></> 
                            }
                        </div>
                    })
                }
            </div>
        }
    </div>
}

export default AnswerContent