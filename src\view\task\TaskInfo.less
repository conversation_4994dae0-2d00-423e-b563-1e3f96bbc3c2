.task-info-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;

  .task-info-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;

    .header-icon {
      width: 17px;
      height: 17px;
      position: absolute;
      left: 16px;
      cursor: pointer;
    }

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  // 企业信息卡片
  .card-header {
    background: rgba(205, 225, 253, 1);
    padding: 16px;
    display: flex;
    align-items: center;

    .building-icon {
      width: 18px;
      height: 18px;
      margin-right: 12px;
      background: url('../../assets/taskList/icon_company.png') no-repeat center center;
      background-size: 100% 100%;
    }

    .company-name {
      font-size: 17px;
      font-weight: 600;
      color: rgba(0, 0, 0, 0.80);
    }
  }
  .task-info-content {
    flex: 1;
    padding: 16px 16px 80px 16px; // 底部留出按钮空间
    overflow-y: auto;
    .card-header {
      background: #f0f2f5;
      padding: 12px 0px;
      display: flex;
      align-items: center;

      .task-icon {
        width: 11px;
        height: 11px;
        margin-right: 8px;
        background: url('../../assets/taskList/icon_zs.png') no-repeat center center;
        background-size: 100% 100%;
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: #666;
      }
    }
    
    .info-card {
      background-color: #fff;
      border-radius: 8px;
      margin-bottom: 12px;
      overflow: hidden;

      &:last-child {
        margin-bottom: 0;
      }


      .info-item {
        display: flex;
        align-items: flex-start;
        justify-content: space-between;
        padding: 11px;
        border-bottom: 1px solid #f0f0f0;
        flex-direction: column;

        &:last-child {
          border-bottom: none;
        }

        .label {
          font-size: 14px;
          color: #666;
          margin-right: 16px;
          min-width: 120px;
          flex-shrink: 0;
          text-align: left;
          margin-bottom: 10px;
        }

        .value {
          font-size: 14px;
          color: #333;
          text-align: left;
          flex: 1;
          word-break: break-all;
        }
      }
    }

    .action-section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #fff;
      padding: 16px;
      box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);

      .start-inspection-btn {
        background: linear-gradient(135deg, #4873FF 0%, #5B8CFF 100%);
        border: none;
        border-radius: 24px;
        color: white;
        font-size: 16px;
        font-weight: 500;
        height: 48px;
        width: 100%;
        box-shadow: 0 4px 12px rgba(72, 115, 255, 0.3);

        &:hover {
          background: linear-gradient(135deg, #3566e0 0%, #4873FF 100%);
          box-shadow: 0 6px 16px rgba(72, 115, 255, 0.4);
        }

        &:active {
          transform: translateY(1px);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .task-info-container {
    .task-info-content {
      padding: 12px;

      .info-card {
        .info-item {
          padding: 12px;

          .label {
            min-width: 100px;
            font-size: 13px;
          }

          .value {
            font-size: 13px;
          }
        }
      }
    }
  }
}
