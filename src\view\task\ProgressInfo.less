.progress-info-container {
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #f0f0f0;
    flex-shrink: 0;
    z-index: 100;
    
    .header-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .antd-mobile-icon {
        font-size: 20px;
        color: #333;
        cursor: pointer;
      }
      
      .title {
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }
    }
    
    .header-right {
      display: flex;
      align-items: center;
      gap: 8px;

      .ant-select {
        .ant-select-selector {
          border: 1px solid #d9d9d9;
          border-radius: 6px;
        }
      }
    }
  }
  
  .content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    padding: 16px;
    -webkit-overflow-scrolling: touch; // iOS 平滑滚动

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(0, 0, 0, 0.2);
      border-radius: 2px;

      &:hover {
        background: rgba(0, 0, 0, 0.3);
      }
    }
    
    .loading {
      text-align: center;
      padding: 40px 0;
      color: #666;
    }
    
    .task-summary {
      background: white;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .task-name {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
      }
      
      .task-stats {
        display: flex;
        gap: 16px;
        font-size: 14px;
        color: #666;
        
        span {
          &:not(:last-child)::after {
            content: '|';
            margin-left: 16px;
            color: #d9d9d9;
          }
        }
      }
    }
    
    .place-group {
      background: white;
      border-radius: 8px;
      margin-bottom: 16px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      
      .group-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        background: #f8f9fa;
        border-bottom: 1px solid #f0f0f0;
        
        .group-name {
          font-size: 15px;
          font-weight: 600;
          color: #333;
        }
        
        .group-stats {
          font-size: 13px;
          color: #666;
          background: #e8f4fd;
          padding: 2px 8px;
          border-radius: 12px;
          display: flex;
          align-items: center;
          gap: 4px;

          .stats-label {
            font-size: 11px;
            color: #999;
            font-weight: normal;
          }
        }
      }
      
      .group-items {
        .check-item-category {
          border-bottom: 1px solid #f0f0f0;

          &:last-child {
            border-bottom: none;
          }

          .category-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px 16px;
            background: #fafafa;
            border-bottom: 1px solid #f5f5f5;

            .category-name {
              font-size: 14px;
              font-weight: 600;
              color: #333;
            }

            .category-stats {
              font-size: 12px;
              color: #666;
              background: #e8f4fd;
              padding: 2px 6px;
              border-radius: 10px;
              display: flex;
              align-items: center;
              gap: 4px;

              .stats-label {
                font-size: 10px;
                color: #999;
                font-weight: normal;
              }
            }
          }

          .category-versions {
            .version-item {
              border-bottom: 1px solid #f8f8f8;

              &:last-child {
                border-bottom: none;
              }

              .version-header {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 8px 16px 8px 24px;
                background: #f9f9f9;
                border-bottom: 1px solid #f0f0f0;

                .version-name {
                  font-size: 13px;
                  font-weight: 500;
                  color: #555;
                }

                .version-stats {
                  font-size: 11px;
                  color: #666;
                  background: #e1f3d8;
                  padding: 2px 6px;
                  border-radius: 8px;
                  display: flex;
                  align-items: center;
                  gap: 4px;

                  .stats-label {
                    font-size: 9px;
                    color: #999;
                    font-weight: normal;
                  }
                }
              }

              .version-requirements {
                .requirement-item {
                  padding: 8px 16px 8px 40px;
                  border-bottom: 1px solid #fafafa;

                  &:last-child {
                    border-bottom: none;
                  }

                  .requirement-content {
                    .requirement-header {
                      display: flex;
                      align-items: flex-start;
                      gap: 6px;
                      margin-bottom: 4px;

                      .requirement-number {
                        // font-weight: 600;
                        color: #333;
                        min-width: 16px;
                        font-size: 12px;
                      }

                      .requirement-description {
                        flex: 1;
                        font-size: 12px;
                        color: #333;
                        line-height: 1.4;
                      }

                      .attachment-count {
                        font-size: 10px;
                        color: #1890ff;
                        background: #e6f7ff;
                        padding: 1px 4px;
                        border-radius: 2px;
                        white-space: nowrap;
                      }
                    }

                    .requirement-result {
                      margin-left: 24px;
                      font-size: 12px;
                      color: #52c41a;
                      font-weight: 500;
                    }

                    .hazard-desc {
                      margin-left: 24px;
                      font-size: 11px;
                      color: #ff4d4f;
                      background: #fff2f0;
                      padding: 3px 5px;
                      border-radius: 2px;
                      border: 1px solid #ffccc7;
                      margin-top: 2px;
                    }
                  }

                  // 状态样式
                  &.status-completed {
                    .requirement-result {
                      color: #52c41a;
                    }
                  }

                  &.status-hazard {
                    background: #fff7e6;
                    border-left: 2px solid #faad14;

                    .requirement-result {
                      color: #faad14;
                      font-weight: 600;
                    }
                  }

                  &.status-pending {
                    .requirement-description {
                      color: #999;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 0;
      color: #999;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .progress-info-container {
    .header {
      padding: 10px 12px;

      .header-left {
        gap: 8px;

        .title {
          font-size: 16px;
        }
      }
    }

    .content {
      padding: 12px;

      // 移动端滚动优化
      &::-webkit-scrollbar {
        width: 2px;
      }
      
      .task-summary {
        padding: 12px;
        
        .task-stats {
          flex-wrap: wrap;
          gap: 8px;
          
          span {
            &:not(:last-child)::after {
              display: none;
            }
          }
        }
      }
      
      .place-group {
        .group-header {
          padding: 10px 12px;
        }

        .group-items {
          .check-item-category {
            .category-header {
              padding: 8px 12px;

              .category-name {
                font-size: 13px;
              }

              .category-stats {
                font-size: 11px;
              }
            }

            .category-versions {
              .version-item {
                .version-header {
                  // padding: 6px 12px 6px 20px;

                  .version-name {
                    font-size: 12px;
                  }

                  .version-stats {
                    font-size: 10px;
                  }
                }

                .version-requirements {
                  .requirement-item {
                    // padding: 6px 12px 6px 32px;

                    .requirement-content {
                      .requirement-header {
                        .requirement-description {
                          font-size: 11px;
                        }

                        .attachment-count {
                          font-size: 9px;
                        }
                      }

                      .requirement-result {
                        margin-left: 18px;
                        font-size: 11px;
                      }

                      .hazard-desc {
                        margin-left: 18px;
                        font-size: 10px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
