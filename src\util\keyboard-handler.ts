/*
 * @Description: 移动端软键盘处理工具
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */

interface KeyboardHandlerOptions {
  onKeyboardShow?: (height: number) => void;
  onKeyboardHide?: () => void;
  adjustViewport?: boolean; // 是否自动调整视口
  inputSelector?: string; // 输入框选择器
}

class KeyboardHandler {
  private originalHeight: number;
  private currentHeight: number;
  private isKeyboardVisible: boolean = false;
  private options: KeyboardHandlerOptions;
  private resizeHandler: () => void;
  private focusHandler: (e: Event) => void;
  private blurHandler: (e: Event) => void;

  constructor(options: KeyboardHandlerOptions = {}) {
    this.originalHeight = window.innerHeight;
    this.currentHeight = window.innerHeight;
    this.options = {
      adjustViewport: true,
      inputSelector: 'input, textarea, [contenteditable]',
      ...options
    };

    this.resizeHandler = this.handleResize.bind(this);
    this.focusHandler = this.handleFocus.bind(this);
    this.blurHandler = this.handleBlur.bind(this);

    this.init();
  }

  private init() {
    // 监听窗口大小变化（主要用于Android）
    window.addEventListener('resize', this.resizeHandler);
    
    // 监听输入框焦点事件（主要用于iOS）
    document.addEventListener('focusin', this.focusHandler);
    document.addEventListener('focusout', this.blurHandler);

    // 设置初始视口高度
    if (this.options.adjustViewport) {
      this.setViewportHeight(this.originalHeight);
    }
  }

  private handleResize() {
    const newHeight = window.innerHeight;
    const heightDiff = this.originalHeight - newHeight;
    
    // 高度差超过100px认为是键盘弹起
    if (heightDiff > 100 && !this.isKeyboardVisible) {
      this.isKeyboardVisible = true;
      this.currentHeight = newHeight;
      
      console.log('键盘弹起 - 高度差:', heightDiff);
      
      if (this.options.adjustViewport) {
        this.setViewportHeight(newHeight);
      }
      
      this.options.onKeyboardShow?.(heightDiff);
    } 
    // 高度差小于50px认为是键盘收起
    else if (heightDiff < 50 && this.isKeyboardVisible) {
      this.isKeyboardVisible = false;
      this.currentHeight = newHeight;
      
      console.log('键盘收起');
      
      if (this.options.adjustViewport) {
        this.setViewportHeight(this.originalHeight);
      }
      
      this.options.onKeyboardHide?.();
    }
  }

  private handleFocus(e: Event) {
    const target = e.target as HTMLElement;
    
    // 检查是否是输入元素
    if (this.isInputElement(target)) {
      console.log('输入框获得焦点');
      
      // iOS上延迟检查，因为键盘弹起有延迟
      setTimeout(() => {
        const newHeight = window.innerHeight;
        const heightDiff = this.originalHeight - newHeight;
        
        if (heightDiff > 50 && !this.isKeyboardVisible) {
          this.isKeyboardVisible = true;
          this.currentHeight = newHeight;
          
          if (this.options.adjustViewport) {
            this.setViewportHeight(newHeight);
          }
          
          this.options.onKeyboardShow?.(heightDiff);
        }
      }, 300);
    }
  }

  private handleBlur(e: Event) {
    const target = e.target as HTMLElement;
    
    if (this.isInputElement(target)) {
      console.log('输入框失去焦点');
      
      // 延迟检查，避免在输入框之间切换时误判
      setTimeout(() => {
        // 检查是否还有其他输入框处于焦点状态
        const activeElement = document.activeElement as HTMLElement;
        
        if (!this.isInputElement(activeElement)) {
          this.isKeyboardVisible = false;
          this.currentHeight = this.originalHeight;
          
          if (this.options.adjustViewport) {
            this.setViewportHeight(this.originalHeight);
          }
          
          this.options.onKeyboardHide?.();
        }
      }, 100);
    }
  }

  private isInputElement(element: HTMLElement): boolean {
    if (!element) return false;
    
    const tagName = element.tagName.toLowerCase();
    const isInput = tagName === 'input' || tagName === 'textarea';
    const isContentEditable = element.contentEditable === 'true';
    
    return isInput || isContentEditable;
  }

  private setViewportHeight(height: number) {
    // 设置CSS自定义属性
    document.documentElement.style.setProperty('--viewport-height', `${height}px`);
    
    // 同时设置body的高度
    document.body.style.height = `${height}px`;
  }

  // 手动调整输入框位置，确保不被键盘覆盖
  public scrollInputIntoView(inputElement?: HTMLElement) {
    const target = inputElement || document.activeElement as HTMLElement;
    
    if (this.isInputElement(target) && this.isKeyboardVisible) {
      setTimeout(() => {
        target.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }, 100);
    }
  }

  // 获取当前键盘状态
  public getKeyboardInfo() {
    return {
      isVisible: this.isKeyboardVisible,
      height: this.isKeyboardVisible ? this.originalHeight - this.currentHeight : 0,
      viewportHeight: this.currentHeight
    };
  }

  // 销毁监听器
  public destroy() {
    window.removeEventListener('resize', this.resizeHandler);
    document.removeEventListener('focusin', this.focusHandler);
    document.removeEventListener('focusout', this.blurHandler);
    
    // 重置视口高度
    if (this.options.adjustViewport) {
      document.documentElement.style.removeProperty('--viewport-height');
      document.body.style.height = '';
    }
  }
}

// 创建全局实例
let globalKeyboardHandler: KeyboardHandler | null = null;

// 初始化键盘处理器
export function initKeyboardHandler(options?: KeyboardHandlerOptions): KeyboardHandler {
  if (globalKeyboardHandler) {
    globalKeyboardHandler.destroy();
  }
  
  globalKeyboardHandler = new KeyboardHandler(options);
  return globalKeyboardHandler;
}

// 获取全局键盘处理器
export function getKeyboardHandler(): KeyboardHandler | null {
  return globalKeyboardHandler;
}

// 销毁全局键盘处理器
export function destroyKeyboardHandler() {
  if (globalKeyboardHandler) {
    globalKeyboardHandler.destroy();
    globalKeyboardHandler = null;
  }
}

export default KeyboardHandler;
