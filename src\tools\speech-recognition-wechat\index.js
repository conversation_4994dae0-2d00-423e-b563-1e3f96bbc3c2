/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-22 11:51:01
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 22:02:41
 * @FilePath: src/tools/speech-recognition-wechat/index.js
 * @Version: 1.0.0
 * @Description: 微信语音识别工具封装
 */
import { Toast } from 'antd-mobile';
import wx from 'weixin-js-sdk';

// 录音状态常量
const RECORD_STATUS = {
	IDLE: 'idle',
	RECORDING: 'recording',
	PROCESSING: 'processing'
};

let localId = '';
let recordStatus = RECORD_STATUS.IDLE;
let voiceRecordEndCallback = null;

/**
 * 配置微信JSSDK
 * @param {Object} config - 微信配置对象
 * @param {string} config.appId - 微信公众号appId
 * @param {string} config.timestamp - 时间戳
 * @param {string} config.nonceStr - 随机字符串
 * @param {string} config.signature - 签名
 * @param {Function} onSuccess - 配置成功回调
 * @param {Function} onError - 配置失败回调
 */
const configWeChat = (config, onSuccess, onError) => {
	// 验证配置参数
	if (!config || !config.appId || !config.timestamp || !config.nonceStr || !config.signature) {
		const errorMsg = '微信配置参数不完整';
		Toast.show(errorMsg);
		onError && onError(errorMsg);
		return;
	}

	wx.config({
		debug: false,
		appId: config.appId,
		timestamp: parseInt(config.timestamp),
		nonceStr: config.nonceStr,
		signature: config.signature,
		jsApiList: ['startRecord', 'stopRecord', 'translateVoice'],
		success: function (res) {
			console.log('微信JSSDK配置成功', res);
			onSuccess && onSuccess(res);
		},
		fail: function (res) {
			const errorMsg = '微信JSSDK配置失败';
			console.error(errorMsg, res);
			Toast.show(errorMsg);
			onError && onError(res);
		}
	});

	wx.ready(function () {
		wx.checkJsApi({
			jsApiList: ['startRecord', 'stopRecord', 'translateVoice'],
			success: function (res) {
				console.log('微信JSSDK支持的API：', res);
			},
			fail: function (res) {
				console.error('API检查失败：', res);
				Toast.show('部分语音功能不可用');
			}
		});

		// 监听录音自动停止
		wx.onVoiceRecordEnd({
			complete: function (res) {
				localId = res.localId;
				recordStatus = RECORD_STATUS.PROCESSING;
				console.log('录音自动结束，localId:', localId);

				// 自动转换语音为文字
				wx.translateVoice({
					localId: localId,
					isShowProgressTips: 1,
					success: function (translateRes) {
						recordStatus = RECORD_STATUS.IDLE;
						console.log('语音转换成功:', translateRes.translateResult);
						voiceRecordEndCallback && voiceRecordEndCallback(translateRes.translateResult);
					},
					fail: function (translateRes) {
						recordStatus = RECORD_STATUS.IDLE;
						const errorMsg = '语音转换失败';
						console.error(errorMsg, translateRes);
						Toast.show(errorMsg);
						voiceRecordEndCallback && voiceRecordEndCallback(null, translateRes);
					}
				});
			}
		});
	});

	wx.error(function (res) {
		const errorMsg = '微信JSSDK初始化失败';
		console.error(errorMsg, res);
		Toast.show(errorMsg);
		onError && onError(res);
	});
};

/**
 * 开始录音
 * @param {Function} onSuccess - 开始录音成功回调
 * @param {Function} onError - 开始录音失败回调
 */
const startRecordWeChat = (onSuccess = null, onError = null) => {
	if (recordStatus !== RECORD_STATUS.IDLE) {
		const errorMsg = '录音正在进行中，请稍后再试';
		Toast.show(errorMsg);
		onError && onError(errorMsg);
		return;
	}

	wx.startRecord({
		success: function (res) {
			recordStatus = RECORD_STATUS.RECORDING;
			localId = res.localId || '';
			console.log('开始录音成功，localId:', localId);
			Toast.show('开始录音');
			onSuccess && onSuccess(res);
		},
		fail: function (res) {
			recordStatus = RECORD_STATUS.IDLE;
			const errorMsg = '开始录音失败';
			console.error(errorMsg, res);
			Toast.show(errorMsg);
			onError && onError(res);
		},
	});
};

/**
 * 停止录音并转换为文字
 * @param {Function} onSuccess - 转换成功回调，参数为转换结果文本
 * @param {Function} onError - 转换失败回调
 */
const stopRecordWeChat = (onSuccess, onError) => {
	if (recordStatus !== RECORD_STATUS.RECORDING) {
		const errorMsg = '当前没有正在进行的录音';
		Toast.show(errorMsg);
		onError && onError(errorMsg);
		return;
	}

	wx.stopRecord({
		success: function (res) {
			localId = res.localId;
			recordStatus = RECORD_STATUS.PROCESSING;
			console.log('停止录音成功，开始转换，localId:', localId);
			Toast.show('正在转换语音...');

			// 将语音转换为文字
			wx.translateVoice({
				localId: localId,
				isShowProgressTips: 1,
				success: function (translateRes) {
					recordStatus = RECORD_STATUS.IDLE;
					console.log('语音转换成功:', translateRes.translateResult);
					Toast.show('转换成功');
					onSuccess && onSuccess(translateRes.translateResult);
				},
				fail: function (translateRes) {
					recordStatus = RECORD_STATUS.IDLE;
					const errorMsg = '语音转换失败';
					console.error(errorMsg, translateRes);
					Toast.show(errorMsg);
					onError && onError(translateRes);
				},
			});
		},
		fail: function (res) {
			recordStatus = RECORD_STATUS.IDLE;
			const errorMsg = '停止录音失败';
			console.error(errorMsg, res);
			Toast.show(errorMsg);
			onError && onError(res);
		},
	});
};

/**
 * 设置录音自动结束的回调函数
 * @param {Function} callback - 回调函数，参数为转换结果文本或错误信息
 */
const onVoiceRecordEnd = (callback) => {
	voiceRecordEndCallback = callback;
};

/**
 * 获取当前录音状态
 * @returns {string} 录音状态
 */
const getRecordStatus = () => {
	return recordStatus;
};

/**
 * 重置录音状态
 */
const resetRecordStatus = () => {
	recordStatus = RECORD_STATUS.IDLE;
	localId = '';
};

export {
	configWeChat,
	startRecordWeChat,
	stopRecordWeChat,
	onVoiceRecordEnd,
	getRecordStatus,
	resetRecordStatus,
	RECORD_STATUS
};