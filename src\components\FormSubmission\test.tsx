/*
 * @Description: FormSubmission 组件测试文件
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
import React, { useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import FormSubmission from './index';

// 模拟测试数据
const mockFormItems = [
  {
    id: '1',
    value: '是',
    isHazard: 1,
    hazardDesc: '发现消防设备损坏',
    material: '',
    config: {
      reqName: '消防设备是否完好',
      outputType: 'SINGLE_CHOICE',
      outputContent: '"是"；"否"'
    }
  },
  {
    id: '2',
    value: '否',
    isHazard: 0,
    hazardDesc: '',
    material: '',
    config: {
      reqName: '是否存在安全隐患',
      outputType: 'SINGLE_CHOICE',
      outputContent: '"是"；"否"'
    }
  },
  {
    id: '3',
    value: ['选项1', '选项2'],
    isHazard: 1,
    hazardDesc: '多个问题需要处理',
    material: '',
    config: {
      reqName: '检查多个项目',
      outputType: 'MULTIPLE_CHOICE',
      outputContent: '"选项1"；"选项2"；"选项3"'
    }
  },
  {
    id: '4',
    value: '5',
    isHazard: 0,
    hazardDesc: '',
    material: '',
    config: {
      reqName: '设备数量统计',
      outputType: 'TEXT',
      outputContent: ''
    }
  }
];

const FormSubmissionTest: React.FC = () => {
  const [testData, setTestData] = useState(mockFormItems);

  const handleSubmit = (data: any) => {
    console.log('测试提交数据:', data);
    
    // 验证隐患描述功能
    data.forEach((item: any, index: number) => {
      console.log(`项目${index + 1}:`, {
        id: item.id,
        value: item.value,
        isHazard: item.isHazard,
        hazardDesc: item.hazardDesc,
        material: item.material
      });
      
      // 验证隐患状态和描述的一致性
      if (item.isHazard === 1 && !item.hazardDesc) {
        console.warn(`项目${index + 1}: 隐患状态为1但隐患描述为空`);
      }
      
      if (item.isHazard === 0 && item.hazardDesc) {
        console.warn(`项目${index + 1}: 隐患状态为0但隐患描述不为空`);
      }
    });
    
    Toast.show({
      content: '测试提交成功！请查看控制台输出',
      duration: 2000
    });
  };

  const resetTestData = () => {
    setTestData([...mockFormItems]);
    Toast.show('测试数据已重置');
  };

  const addHazardItem = () => {
    const newItem = {
      id: `${Date.now()}`,
      value: '',
      isHazard: 1,
      hazardDesc: '新增隐患项目',
      material: '',
      config: {
        reqName: '新增测试项目',
        outputType: 'SINGLE_CHOICE',
        outputContent: '"正常"；"异常"'
      }
    };
    setTestData(prev => [...prev, newItem]);
    Toast.show('已添加新的隐患项目');
  };

  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <div style={{ marginBottom: '20px', backgroundColor: 'white', padding: '16px', borderRadius: '8px' }}>
        <h2 style={{ margin: '0 0 16px 0', fontSize: '18px', fontWeight: 'bold' }}>
          FormSubmission 隐患描述功能测试
        </h2>
        <div style={{ display: 'flex', gap: '12px', flexWrap: 'wrap' }}>
          <Button size="small" color="primary" onClick={resetTestData}>
            重置测试数据
          </Button>
          <Button size="small" color="warning" onClick={addHazardItem}>
            添加隐患项目
          </Button>
        </div>
        <div style={{ marginTop: '12px', fontSize: '14px', color: '#666' }}>
          <p>测试说明：</p>
          <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
            <li>当状态选择为"隐患"时，会显示隐患描述输入框</li>
            <li>当状态切换为"正常"时，隐患描述会自动清空</li>
            <li>提交时会包含隐患描述数据</li>
            <li>查看控制台可以看到详细的提交数据</li>
          </ul>
        </div>
      </div>

      <FormSubmission
        formItems={testData}
        onSubmit={handleSubmit}
        onUpload={(itemId) => {
          console.log('测试上传功能:', itemId);
          Toast.show(`测试上传功能: ${itemId}`);
        }}
      />
    </div>
  );
};

export default FormSubmissionTest;
