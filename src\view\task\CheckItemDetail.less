.check-item-detail-container {
  min-height: 100vh;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 头部样式
  .check-item-header {
    background-color: #fff;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    margin-bottom: 13px;

    .back-icon {
      font-size: 20px;
      color: #333;
      margin-right: 12px;
      cursor: pointer;
    }

    .page-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex: 1;
    }
  }

  // 主要内容区域样式
  .main-content {
    overflow-y: auto;
    overflow-x: hidden;
    height: auto; // 强制flex子元素可滚动
    padding-bottom: 120px; // 底部留白
  }

  // 通用区域样式
  .guidance-section,
  .requirements-section{
    background-color: #fff;
    margin: 8px 16px;
    border-radius: 12px;
    padding: 16px;
    padding-left: 0px;
    padding-right: 0px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(72, 115, 255, 0.2);
  }
  .section-header {
    display: flex;
    align-items: center;
    padding: 0px 16px;

    .section-icon {
      width: 11px;
      height: 11px;
      margin-right: 8px;
      background: url('../../assets/taskList/icon_zs.png') no-repeat center center;
      background-size: 100% 100%;
    }
    .analysis-icon {
      width: 22px;
      height: 22px;
      margin-right: 8px;
      background: url('../../assets/home/<USER>') no-repeat center center;
      background-size: 100% 100%;
    }

    .section-title {
      font-size: 17px;
      font-weight: 600;
      color: #333;
      flex: 1;
      
    }

    .titleColor {
      // 字体过渡色
      background: linear-gradient( 90deg, #64CCF1 0%, #4873FF 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent; 
      // color: #64CCF1;
    }
    .action-buttons {
      display: flex;
      border-radius: 16px;
      overflow: hidden;
      // box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .check-btn {
      width: 78px;
      height: 32px;
      font-size: 12px;
      font-weight: 400;
      color: #666;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
      background: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      color: rgba(0, 0, 0, 0.60);

      // 左侧按钮
      &:first-child {
        background: url('../../assets/taskList/tab_left_active.png') no-repeat center center;
        background-size: 100% 100%;
      }

      // 右侧按钮
      &:last-child {
        background: url('../../assets/taskList/tab_right_active.png') no-repeat center center;
        background-size: 100% 100%;
        margin-left: -14px;
      }

      &:first-child.active {
        background: url('../../assets/taskList/tab_left.png') no-repeat center center;
        color: rgba(255, 255, 255, 1);
        background-size: 100% 100%;
      }

      &:last-child.active {
        background: url('../../assets/taskList/tab_right.png') no-repeat center center;
        color: rgba(255, 255, 255, 1);
        background-size: 100% 100%;
      }

      // &:hover:not(.active) {
      //   background: #e8e8e8;
      //   color: #333;
      // }
    }

    .tab-buttons {
      display: flex;
      gap: 8px;

      .tab-btn {
        width: 78px;
        height: 32px;
        background: #f5f5f5;
        border: 1px solid #e8e8e8;
        border-radius: 24px;
        color: #666;
        font-size: 14px;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: linear-gradient( 360deg, #4873FF 0%, #A6BBFD 100%);
          border-color: transparent;
          color: white;
          box-shadow: 0 2px 8px rgba(72, 115, 255, 0.3);
        }

        &:hover:not(.active) {
          background: #e8e8e8;
          border-color: #d0d0d0;
        }

        &:active {
          transform: translateY(1px);
        }
      }
    }
    

    .status-badge {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      margin-right: 8px;

      &.success {
        background-color: #f6ffed;
        color: #52c41a;
        border: 1px solid #b7eb8f;
      }
    }

    .media-selector {
      background-color: #f5f5f5;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      color: #666;
      max-width: 240px;
    }
  }

  // 巡检指导区域
  .guidance-section {
    .media-container {
      position: relative;
      width: 100%;
      min-height: 180px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;

      .media-placeholder {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 180px;
        cursor: pointer;

        .play-icon {
          font-size: 48px;
          color: white;
          margin-bottom: 8px;
        }

        .placeholder-text {
          color: white;
          font-size: 14px;
          opacity: 0.9;
        }
      }

      video {
        width: 100%;
        height: 180px;
        object-fit: cover;
      }

      .audio-container {
        width: 100%;
        padding: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .audio-info {
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          margin-bottom: 12px;

          .audio-icon {
            font-size: 32px;
            margin-right: 8px;
            opacity: 0.8;
          }

          .audio-label {
            font-size: 16px;
            font-weight: 500;
          }
        }

        audio {
          width: 100%;
          max-width: 300px;
          height: 40px;

          &::-webkit-media-controls-panel {
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
          }
        }
      }

      .image-container {
        width: 100%;
        height: 180px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 8px;

        .guide-image {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.2s ease;

          &:hover {
            transform: scale(1.02);
          }

          &:active {
            transform: scale(0.98);
          }
        }
      }
    }
  }

  // 检查要求区域
  .requirements-section {
      background: linear-gradient( 90deg, #EBF3F9 0%, #fff 100%);
      box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
      border-radius: 9px 9px 9px 9px;
      border: 1px solid rgba(72, 115, 255, 0.2);
    .requirements-list {

      .requirement-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 8px;

        .requirement-number {
          // font-weight: 600;
          color: #333;
          // margin-right: 8px;
          min-width: 16px;
        }

        .requirement-content {
          font-size: 15px;
          color: #333;
          line-height: 1.4;
        }
      }
    }


    // 分析结果样式
    .analysis-sections {
      .analysis-results {
        .analysis-item {
          border-radius: 8px;
          padding: 16px;
          margin-bottom: 16px;

          .analysis-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 12px;

            .analysis-number {
              font-weight: 500;
              color: #333;
              margin-right: 4px;
              min-width: 20px;
              font-size: 14px;
            }

            .analysis-requirement {
              flex: 1;
              font-size: 14px;
              color: #333;
              font-weight: 400;
              line-height: 1.4;
              margin-right: 12px;
            }

            .analysis-status {
              padding: 2px 8px;
              border-radius: 4px;
              font-size: 14px;
              font-weight: 500;
              min-width: 40px;
              text-align: center;

              &.normal {
                color: rgba(51, 196, 147, 1);
              }

              &.hazard {
                color: #ff4d4f;
              }
            }
          }

          .analysis-result {
            margin-bottom: 12px;
            padding-left: 12px;

            .result-label {
              font-size: 14px;
              color: #666;
              margin-right: 8px;
              font-weight: 500;
            }

            .result-value {
              font-size: 14px;
              color: #333;
              font-weight: 400;
              line-height: 1.4;
            }
          }

          .hazard-description {
            margin-bottom: 8px;
            // padding-left: 20px;

            .hazard-label {
              font-size: 14px;
              color: #ff4d4f;
              margin-right: 8px;
              font-weight: 500;
            }

            .hazard-content {
              font-size: 14px;
              color: #ff4d4f;
              background: #fff2f0;
              padding: 8px;
              padding-left: 20px;
              border-radius: 4px;
              border: 1px solid #ffccc7;
              display: block;
              margin-top: 4px;
            }
          }

          .uploaded-images {
            .images-label {
              font-size: 13px;
              color: #666;
              margin-bottom: 8px;
              font-weight: 500;
            }

            .images-grid {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;

              .image-item {
                position: relative;
                width: 80px;
                height: 80px;
                border-radius: 8px;
                overflow: hidden;
              }
            }
          }

          // AI分析结果图片样式
          .analysis-images {
            margin-top: 8px;

            .images-grid {
              display: flex;
              gap: 8px;
              flex-wrap: wrap;

              .analysis-image-item {
                position: relative;
                width: 80px;
                height: 80px;
                border-radius: 8px;
                overflow: hidden;
                border: 1px solid #e8e8e8;
                background: #f8f9fa;

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  cursor: pointer;
                  transition: transform 0.2s ease;

                  &:hover {
                    transform: scale(1.02);
                  }
                }

                .image-overlay {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background: rgba(0, 0, 0, 0.3);
                  opacity: 0;
                  transition: opacity 0.2s ease;

                  .play-icon {
                    color: white;
                    font-size: 20px;
                  }

                  &:hover {
                    opacity: 1;
                  }
                }

                .image-actions {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  display: flex;
                  align-items: flex-start;
                  justify-content: flex-end;
                  padding: 4px;
                  opacity: 0;
                  transition: opacity 0.2s ease;

                  &:hover {
                    opacity: 1;
                  }
                }

                .delete-overlay {
                  width: 20px;
                  height: 20px;
                  background: rgba(255, 77, 79, 0.9);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 12px;
                  cursor: pointer;
                  transition: all 0.2s ease;

                  &:hover {
                    background: #ff4d4f;
                    transform: scale(1.1);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  // .upload-section {
    // padding: 16px;
    .uploaded-images {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      overflow-x: auto;
      padding: 4px 0;

      .image-item {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 8px;
        overflow: hidden;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .image-actions {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .play-overlay {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 24px;
          height: 24px;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 12px;
          z-index: 1;
        }

        .delete-overlay {
          position: absolute;
          top: 4px;
          right: 4px;
          width: 20px;
          height: 20px;
          background-color: rgba(255, 77, 79, 0.9);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 10px;
          cursor: pointer;
          z-index: 2;

          &:hover {
            background-color: rgba(255, 77, 79, 1);
          }
        }
      }
    }

    .upload-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;
      padding: 16px;

      .image-upload-wrapper {
        .adm-image-uploader {
          .adm-image-uploader-cell {
            display: none;
          }
        }
      }

      .upload-btn {
        height: 48px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &.photo-btn {
          background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
          border: none;
          color: white;

          &:active {
            background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
            border: none;
            color: white;
          }

          &:disabled {
            opacity: 0.6;
            cursor: not-allowed;

            &:active {
              transform: none;
            }
          }
        }
      }
    }
  // }

  // AI分析结果区域 - 语音识别部分
  .analysis-sections {

    .analysis-results {
      margin-bottom: 16px;

      .analysis-item {
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;

        .analysis-header {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          margin-bottom: 12px;

          .analysis-number {
            font-weight: 500;
            color: #333;
            margin-right: 4px;
            min-width: 20px;
            font-size: 14px;
          }

          .analysis-requirement {
            flex: 1;
            font-size: 14px;
            color: #333;
            font-weight: 400;
            line-height: 1.4;
            margin-right: 12px;
          }

          .analysis-status {
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 500;
            min-width: 40px;
            text-align: center;

            &.normal {
              color: rgba(51, 196, 147, 1);
            }

            &.hazard {
              color: #ff4d4f;
            }
          }
        }

        .analysis-result {
          margin-bottom: 12px;
          padding: 10px;
          padding-left: 12px;
          background-color: rgba(255, 255, 255, 1);

          .result-label {
            font-size: 13px;
            color: #666;
            margin-right: 8px;
            font-weight: 500;
          }

          .result-value {
            font-size: 14px;
            color: #333;
            font-weight: 400;
            line-height: 1.4;
          }
        }

        .hazard-description {
          margin-bottom: 8px;
          // padding-left: 20px;

          .hazard-label {
            font-size: 13px;
            color: #ff4d4f;
            margin-right: 8px;
            font-weight: 500;
          }

          .hazard-content {
            font-size: 14px;
            color: #ff4d4f;
            background: #fff2f0;
            padding: 8px;
            padding-left: 20px;
            border-radius: 4px;
            border: 1px solid #ffccc7;
            display: block;
            margin-top: 4px;
          }
        }

        // 语音识别结果图片样式
        .analysis-images {
          margin-top: 8px;

          .images-grid {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;

            .analysis-image-item {
              position: relative;
              width: 80px;
              height: 80px;
              border-radius: 8px;
              overflow: hidden;
              border: 1px solid #e8e8e8;
              background: #f8f9fa;

              img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                cursor: pointer;
                transition: transform 0.2s ease;

                &:hover {
                  transform: scale(1.02);
                }
              }

              .image-overlay {
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                display: flex;
                align-items: center;
                justify-content: center;
                background: rgba(0, 0, 0, 0.3);
                opacity: 0;
                transition: opacity 0.2s ease;

                .play-icon {
                  color: white;
                  font-size: 20px;
                }

                &:hover {
                  opacity: 1;
                }
              }
            }
          }
        }
      }
    }

    .analysis-actions {
      .rerecognize-btn {
        height: 48px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
        border: none;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &:active {
          background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
        }
      }
    }
  }

  // 语音识别区域
  .voice-section {
    padding: 16px;

    .voice-btn {
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 500;
      background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
      border: none;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      transition: all 0.3s ease;

      &:active {
        background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
      }

      &.recording {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
        animation: pulse 1.5s infinite;
      }

      &.completed {
        background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;

        &:active {
          transform: none;
        }
      }
    }
  }
}

// 录制动画
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 107, 107, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}

// 响应式适配
// 语音输入组件样式
.voice-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

@media (max-width: 375px) {
  .check-item-detail-container {
    .guidance-section,
    .requirements-section,
    .analysis-section {
      margin: 8px 12px;
      padding: 12px;
    }

    .voice-section {
      padding: 12px;
    }
  }

  // 内容区域样式
  .content-section {
    background-color: #fff;
    margin: 8px 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    min-height: 200px;
    padding: 16px;

    .no-analysis {
      padding: 40px 20px;
      text-align: center;
      color: #999;

      p {
        margin: 0;
        font-size: 14px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 375px) {
  .check-item-detail {
    .analysis-sections {
      .analysis-results {
        .analysis-item {
          padding: 12px;

          .analysis-header {
            .analysis-requirement {
              font-size: 13px;
            }

            .analysis-status {
              font-size: 13px;
              padding: 1px 6px;
            }
          }

          .analysis-result {
            padding-left: 12px;

            .result-value {
              font-size: 13px;
            }
          }

          .analysis-images {

            .images-grid {
              .analysis-image-item {
                width: 70px;
                height: 70px;
              }
            }
          }

          .hazard-description {
            padding-left: 16px;

            .hazard-content {
              font-size: 13px;
            padding-left: 20px;
              padding: 6px;
            }
          }
        }
      }
    }
  }
}
