import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Error(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aeed__a"><rect width="18" height="18" rx="0"/></clipPath></defs><g clipPath="url(#14aeed__a)"><path data-follow-fill="#575B66" d="M15.821 11.307V.987A.987.987 0 0 0 14.834 0H1.544a.989.989 0 0 0-.987.988V16.32c0 .545.442.987.988.987h9.253a3.93 3.93 0 0 0 5.023-6Zm-7.553-.156H2.647V9.75H8.27v1.401h-.002Zm0-3.068H2.647V6.682H8.27v1.4h-.002Zm2.252-3.63H2.647v-1.4h7.873v1.4Zm5.01 11.057-.985.983-1.359-1.359-1.608 1.608-.983-.985 1.608-1.608-1.608-1.608.983-.983 1.608 1.608 1.36-1.36.983.984-1.36 1.359 1.362 1.361Z" fill={_fill}/></g></g>
        </svg>
    )
}
