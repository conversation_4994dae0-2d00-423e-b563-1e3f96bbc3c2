/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-05 10:46:38
 * @FilePath: \react-h5-template\src\types\store.d.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
interface User {
    name: string;
    avatar: string;
    email: string;
}

interface UserState {
    user: User | null;
    setUser: (user: User) => void;
    clearUser: () => void;
}

// 定义缓存的类型
interface CacheItem {
    [key: string]: any; // 这里假设缓存项可以是任何类型的数据
  }
  
// 定义状态接口
interface CacheState {
    cache: CacheItem;
    setCache: (key: string, value: any) => void;
    getCache: (key: string) => any;
    removeCache: (key: string) => void;
    clearCache: () => void;
}

type lang = 'zh_CN' | 'en_US'

interface LangStore {
    lang: lang;
    changeLanguage: (lang: lang) => void
    toggleLanguage: () => void
}


// 定义缓存数据的类型，假设缓存的数据可以是任何类型
interface CacheData {
  [key: string]: any;
}

// 定义 store 的类型
interface CacheStore {
  getCache: (key: string) => any;
  setCache: (key: string, value: any) => void;
  clearCache: () => void;
}