/*
 * @Description: 巡检完成度页面
 * @Author: AI Assistant
 * @Date: 2025-07-11
 */
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Select } from 'antd';
import { LeftOutline } from 'antd-mobile-icons';
import { apis } from '@/api/api';
import './ProgressInfo.less';

// 检查要求接口
interface CheckRequirement {
  id: string;
  description: string;
  value: string | null;
  isHazard: number;
  hazardDesc: string | null;
  material: string;
  attachmentCount: number;
  status: 'completed' | 'hazard' | 'pending';
  checkWay: number;
  config: any;
}

// 检查批次接口
interface CheckVersion {
  id: string;
  version: number;
  checkName: string;
  status: number;
  count: number;
  requirements: CheckRequirement[];
  completedCount: number;
  hazardCount: number;
}

// 检查项目接口
interface CheckItem {
  id: string;
  cateName: string;
  configItemId: number;
  checkCount: number;
  requirementCount: number;
  hazardCount: number;
  status: number;
  versions: CheckVersion[];
  totalRequirements: number;
  completedRequirements: number;
}

// 场所分组接口
interface PlaceGroup {
  id: string;
  cateName: string;
  status: number;
  items: CheckItem[];
  totalRequirements: number;
  completedRequirements: number;
  hazardRequirements: number;
}

// 任务信息接口
interface TaskInfo {
  id: number;
  taskName: string;
  completion: number;
  checkCount: number;
  hiddenDangerCount: number;
  checkPersonName: string;
}

const ProgressInfo: React.FC = () => {
  const navigate = useNavigate();
  const [taskInfo, setTaskInfo] = useState<TaskInfo | null>(null);
  const [placeGroups, setPlaceGroups] = useState<PlaceGroup[]>([]);
  const [filterType, setFilterType] = useState<string>('all');
  const [selectedPlace, setSelectedPlace] = useState<string>('all');
  const [loading, setLoading] = useState(true);

  // 从sessionStorage获取任务参数
  const getTaskParams = () => {
    const prameData = JSON.parse(sessionStorage.getItem('prameData') || '{}');
    return {
      taskCode: prameData.taskCode,
      id: prameData.newId
    };
  };

  // 获取任务详情数据
  const fetchTaskDetail = async () => {
    try {
      setLoading(true);
      const { taskCode, id } = getTaskParams();
      
      if (!taskCode || !id) {
        console.error('缺少必要的任务参数');
        return;
      }

      const response = await apis.ginkgoSystem.getDetail({ taskCode, id });
      console.log('ProgressInfo API response:', response);

      if (response.code === 0) {
        const data = response.data;
        
        // 设置任务基本信息
        setTaskInfo({
          id: data.id,
          taskName: data.taskName,
          completion: data.completion,
          checkCount: data.checkCount,
          hiddenDangerCount: data.hiddenDangerCount,
          checkPersonName: data.checkPersonName
        });

        // 处理场所和检查项数据
        const processedGroups = processPlaceData(data.placeList);
        setPlaceGroups(processedGroups);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理场所数据
  const processPlaceData = (placeList: any[]): PlaceGroup[] => {
    return placeList.map(place => {
      const items: CheckItem[] = [];
      let totalRequirements = 0;
      let completedRequirements = 0;
      let hazardRequirements = 0;

      place.itemRespVOList.forEach((item: any) => {
        const versions: CheckVersion[] = [];
        let itemTotalRequirements = 0;
        let itemCompletedRequirements = 0;

        if (item.versions && item.versions.length > 0) {
          item.versions.forEach((version: any) => {
            const requirements: CheckRequirement[] = [];
            let versionCompletedCount = 0;
            let versionHazardCount = 0;

            if (version.results && version.results.length > 0) {
              version.results.forEach((result: any) => {
                const attachmentCount = result.material ? result.material.split(',').filter((m: string) => m.trim()).length : 0;

                let status: 'completed' | 'hazard' | 'pending' = 'pending';
                if (result.value) {
                  status = result.isHazard === 1 ? 'hazard' : 'completed';
                  versionCompletedCount++;
                  itemCompletedRequirements++;
                  completedRequirements++;
                  if (result.isHazard === 1) {
                    versionHazardCount++;
                    hazardRequirements++;
                  }
                }

                requirements.push({
                  id: result.id.toString(),
                  description: result.config?.reqName || result.description || '检查要求',
                  value: result.value,
                  isHazard: result.isHazard,
                  hazardDesc: result.hazardDesc,
                  material: result.material,
                  attachmentCount,
                  status,
                  checkWay: result.checkWay,
                  config: result.config
                });

                itemTotalRequirements++;
                totalRequirements++;
              });
            }

            versions.push({
              id: version.id.toString(),
              version: version.version,
              checkName: version.checkName || `${item.cateName}`,
              status: version.status,
              count: version.count,
              requirements,
              completedCount: versionCompletedCount,
              hazardCount: versionHazardCount
            });
          });
        }

        items.push({
          id: item.configItemId.toString(),
          cateName: item.cateName,
          configItemId: item.configItemId,
          checkCount: item.checkCount,
          requirementCount: item.requirementCount,
          hazardCount: item.hazardCount,
          status: item.status,
          versions,
          totalRequirements: itemTotalRequirements,
          completedRequirements: itemCompletedRequirements
        });
      });

      return {
        id: place.id.toString(),
        cateName: place.cateName,
        status: place.status,
        items,
        totalRequirements,
        completedRequirements,
        hazardRequirements
      };
    });
  };

  // 根据筛选条件计算统计数据
  const calculateFilteredStats = (requirements: CheckRequirement[]) => {
    // 总是基于所有要求计算统计数据
    let total = requirements.length;
    let completed = 0;
    let hazard = 0;
    let pending = 0;

    requirements.forEach(req => {
      if (req.status === 'completed') {
        completed++;
      }
      if (req.status === 'hazard') {
        hazard++;
      }
      if (req.status === 'pending') {
        pending++;
      }
    });

    // 根据筛选类型返回相应的统计数据
    switch (filterType) {
      case 'completed':
        // 在已完成模式下，保留原始总数，显示正确的个数
        return { total: total, completed: completed, hazard: hazard };
      case 'hazard':
        return { total: total, completed: completed, hazard: hazard };
      case 'pending':
        return { total: total, completed: pending, hazard: 0 };
      case 'all':
      default:
        return { total, completed, hazard };
    }
  };

  // 筛选数据
  const getFilteredGroups = () => {
    let filteredGroups = placeGroups;

    // 按场所筛选
    if (selectedPlace !== 'all') {
      filteredGroups = filteredGroups.filter(group => group.id === selectedPlace);
    }

    // 按状态筛选并重新计算统计数据
    return filteredGroups.map(group => {
      const filteredItems = group.items.map(item => {
        const filteredVersions = item.versions.map(version => {
          let filteredRequirements = version.requirements;

          // 根据筛选类型过滤要求
          if (filterType !== 'all') {
            filteredRequirements = version.requirements.filter(req => {
              switch (filterType) {
                case 'completed':
                  return req.status === 'completed';
                case 'hazard':
                  return req.status === 'hazard';
                case 'pending':
                  return req.status === 'pending';
                default:
                  return true;
              }
            });
          }

          // 计算版本统计数据
          const versionStats = calculateFilteredStats(version.requirements);

          return {
            ...version,
            requirements: filteredRequirements,
            // 更新统计数据
            completedCount: versionStats.completed,
            count: versionStats.total,
            hazardCount: versionStats.hazard
          };
        }).filter(version => version.requirements.length > 0);

        // 计算项目统计数据
        const allItemRequirements = item.versions.flatMap(v => v.requirements);
        const itemStats = calculateFilteredStats(allItemRequirements);

        return {
          ...item,
          versions: filteredVersions,
          // 更新统计数据
          totalRequirements: itemStats.total,
          completedRequirements: itemStats.completed,
          hazardCount: itemStats.hazard
        };
      }).filter(item => item.versions.length > 0);

      // 计算组统计数据
      const allGroupRequirements = group.items.flatMap(item =>
        item.versions.flatMap(v => v.requirements)
      );
      const groupStats = calculateFilteredStats(allGroupRequirements);

      return {
        ...group,
        items: filteredItems,
        // 更新统计数据
        totalRequirements: groupStats.total,
        completedRequirements: groupStats.completed,
        hazardRequirements: groupStats.hazard
      };
    }).filter(group => group.items.length > 0);
  };

  // 获取状态显示文本
  const getStatusText = (requirement: CheckRequirement) => {
    if (requirement.status === 'pending') return '';
    if (requirement.status === 'hazard') return '隐患';
    return requirement.value || '正常';
  };

  // 获取状态样式类名
  const getStatusClassName = (requirement: CheckRequirement) => {
    switch (requirement.status) {
      case 'completed':
        return 'status-completed';
      case 'hazard':
        return 'status-hazard';
      case 'pending':
        return 'status-pending';
      default:
        return '';
    }
  };

  // 获取统计显示文本
  const getStatsText = (completed: number, total: number, hazard?: number) => {
    switch (filterType) {
      case 'completed':
        return `${completed}/${total}`;
      case 'hazard':
        return `${hazard || 0}/${total}`;
      case 'pending':
        return `${completed}/${total}`;
      case 'all':
      default:
        return `${completed}/${total}`;
    }
  };

  // 获取统计标签
  const getStatsLabel = () => {
    switch (filterType) {
      case 'completed':
        return '正确';
      case 'hazard':
        return '隐患';
      case 'pending':
        return '未完成';
      case 'all':
      default:
        return '';
    }
  };

  useEffect(() => {
    fetchTaskDetail();
  }, []);

  const filteredGroups = getFilteredGroups();

  console.log(filteredGroups);

  return (
    <div className="progress-info-container">
      {/* 头部 */}
      <div className="header">
        <div className="header-left">
          <LeftOutline onClick={() => navigate(-1)} />
          <span className="title">巡检表</span>
        </div>
        <div className="header-right">
          <Select
            value={selectedPlace}
            onChange={setSelectedPlace}
            style={{ width: 120, marginRight: 8 }}
            size="small"
            placeholder="选择场所"
            options={[
              { value: 'all', label: '全部场所' },
              ...placeGroups.map(group => ({
                value: group.id,
                label: group.cateName
              }))
            ]}
          />
          <Select
            value={filterType}
            onChange={setFilterType}
            style={{ width: 100 }}
            size="small"
            placeholder="筛选状态"
            options={[
              { value: 'all', label: '全部' },
              { value: 'completed', label: '正确' },
              { value: 'hazard', label: '隐患' },
              { value: 'pending', label: '未完成' },
            ]}
          />
        </div>
      </div>

      {/* 内容区域 */}
      <div className="content">
        {loading ? (
          <div className="loading">加载中...</div>
        ) : (
          <>
            {/* 任务信息 */}
            {taskInfo && (
              <div className="task-summary">
                <div className="task-name">{taskInfo.taskName}</div>
                <div className="task-stats">
                  <span>完成度: {taskInfo.completion}%</span>
                  <span>检查数: {taskInfo.checkCount}</span>
                  <span>隐患数: {taskInfo.hiddenDangerCount}</span>
                </div>
              </div>
            )}

            {/* 检查项列表 */}
            {filteredGroups.map(group => (
              <div key={group.id} className="place-group">
                <div className="group-header">
                  <span className="group-name">{group.cateName}</span>
                  <span className="group-stats">
                    {getStatsText(group.completedRequirements, group.totalRequirements, group.hazardRequirements)}
                    {/* {getStatsLabel() && <span className="stats-label"> {getStatsLabel()}</span>} */}
                  </span>
                </div>

                <div className="group-items">
                  {group.items.map((item) => (
                    <div key={item.id} className="check-item-category">
                      <div className="category-header">
                        <span className="category-name">{item.cateName}</span>
                        <span className="category-stats">
                          {getStatsText(item.completedRequirements, item.totalRequirements, item.hazardCount)}
                          {/* {getStatsLabel() && <span className="stats-label"> {getStatsLabel()}</span>} */}
                        </span>
                      </div>

                      <div className="category-versions">
                        {item.versions.map((version) => (
                          <div key={version.id} className="version-item">
                            <div className="version-header">
                              <span className="version-name">{version.checkName}</span>
                              <span className="version-stats">
                                {getStatsText(version.completedCount, version.count, version.hazardCount)}
                                {/* {getStatsLabel() && <span className="stats-label"> {getStatsLabel()}</span>} */}
                              </span>
                            </div>

                            <div className="version-requirements">
                              {version.requirements.map((requirement, reqIndex) => (
                                <div key={requirement.id} className={`requirement-item ${getStatusClassName(requirement)}`}>
                                  <div className="requirement-content">
                                    <div className="requirement-header">
                                      <span className="requirement-number">{reqIndex + 1}.</span>
                                      <span className="requirement-description">{requirement.description}</span>
                                      {requirement.attachmentCount > 0 && (
                                        <span className="attachment-count">附件 {requirement.attachmentCount}个</span>
                                      )}
                                    </div>

                                    {getStatusText(requirement) && (
                                      <div className="requirement-result">
                                        {getStatusText(requirement)}
                                      </div>
                                    )}

                                    {requirement.hazardDesc && (
                                      <div className="hazard-desc">
                                        {requirement.hazardDesc}
                                      </div>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {filteredGroups.length === 0 && (
              <div className="empty-state">
                暂无数据
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ProgressInfo;
