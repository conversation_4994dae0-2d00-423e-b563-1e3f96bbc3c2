/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-12 10:58:34
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-12 11:48:42
 * @FilePath: src/components/question-answer/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述
 */
import chatUserSvg from '@/assets/images/chat/chat_user.svg';
import MarkdownContent from '@/components/markdown-content';
import styles from './index.module.scss';

const QuestionAnswerContainer = ({ qaList, recommendList, sceneData, sendQuestion }) => {
	return (
		<div className={styles['question-answer-container']} id={'qa-list'}>
			{
				qaList.map((item, index) => {
					const isLast = (index === qaList.length - 1);
					return (
						<div key={index}>
							{
								item.senderType === 'user' ? <div className={`${styles['qa-item']}`}>
									<div className={styles['question-avatar']}>
										<img src={chatUserSvg} alt=""/>
									</div>
									<div className={styles['question-content']}>
										{item.content}
									</div>
								</div> : <div className={`${styles['qa-item']} ${styles['answer']}`}>
									<div className={styles['answer-avatar']}>
										<img src={sceneData.avatar} alt=""/>
									</div>
									<div
										className={`${styles['answer-content']} ${(isLast && !item.content) ? styles['cursor-effect'] : ''}`}
										id="answer">
										<MarkdownContent content={item.content || ''} parent={null} loading={false} sceneData={null} />
									</div>
								</div>
							}
						</div>
					);
				})
			}
			{
				!!recommendList.length && <div className={styles['recomment-list']}>
					<div className={styles['recomment-list-title']}>可能想问：</div>
					<div className={styles['list-container']}>
						{
							recommendList.map(l => {
								return <div key={l.id} className={styles['recomment-item']}
								            onClick={() => sendQuestion(l)}>{l.content}</div>;
							})
						}
					</div>
				</div>
			}
		</div>
	);
};

export default QuestionAnswerContainer;