/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-06 17:35:41
 * @FilePath: \react-h5-template\src\route\render-routes.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {Route, Routes} from 'react-router-dom';
import {routes, AppRoute} from './routes';
import AuthRoute from './auth.tsx';

/**
 * 渲染路由
 * @constructor RenderRoutes
 */
export const RenderRoutes = () => {
    const renderRoutes = (routes: AppRoute[]) => {
        return routes.map(route => (
            <Route
                key={route.path}
                path={route.path}
                element={
                    <AuthRoute auth={route.auth}>
                        {route.element}
                    </AuthRoute>
                }
            >
                {route.children && renderRoutes(route.children)}
            </Route>
        ));
    };

    return <Routes>{renderRoutes(routes)}</Routes>;
};
