/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-05 10:57:54
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-16 17:51:00
 * @FilePath: src/views/mobile-questions-answer-view/index.tsx
 * @Version: 1.0.0
 * @Description: 组件描述 文本输入和语音输入相互切换组件
 */
import React, { useState, useRef, useImperativeHandle, forwardRef } from 'react';
import MobileQuestionInput from '@/components/mobile-question-input/index.tsx';
import { SpeechRecognitionContainer } from '@/containers';
import { StopOutlined, SyncOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import './index.less';

// 定义组件 Props 接口
interface MobileQuestionsAnswerViewProps {
	sceneType: string;
	showBtn?: boolean;
	showStop?: boolean;
	loading?: boolean;
	disabled?: boolean;
	onSend: (data: any) => void;
	onStopQuestion?: () => void;
	onReSendQuestion?: () => void;
	setSearchState?: (state: { isOpenThinking: number; isOpenWebSearch: number }) => void;
	setOpenUpload?: () => void;
	taskData?: any;
	hasPendingImages?: boolean;
}

// 定义组件 Ref 接口
interface MobileQuestionsAnswerViewRef {
	focusInput: () => void;
}

const MobileQuestionsAnswerView = forwardRef<MobileQuestionsAnswerViewRef, MobileQuestionsAnswerViewProps>(({
	sceneType,
	showBtn,
	showStop,
	loading,
	disabled,
	onSend,
	onStopQuestion,
	onReSendQuestion,
	setSearchState,
	setOpenUpload,
	taskData,
	hasPendingImages = false
}, ref) => {
	const [voiceShow, setVoiceShow] = useState(false);
	const [questionsData, setQuestionsData] = useState<any>({});
	const mobileQuestionInputRef = useRef<any>(null);

	// 暴露方法给父组件
	useImperativeHandle(ref, () => ({
		focusInput: () => {
			if (mobileQuestionInputRef.current) {
				mobileQuestionInputRef.current.focusInput();
			}
		}
	}));

	const onUpdateHandler = (data: any) => {
		setQuestionsData(data);
		setSearchState && setSearchState({
			isOpenThinking: data.deepThinkingSelected ? 1 : 0,
			isOpenWebSearch: data.onlineSearchSelected ? 1 : 0,
		});
	};

	const onSendHandler = () => {
		onSend({
			...questionsData,
			sceneType
		});
	};

	return (
		<div
			className={'mobile-questions-answer-view'}
		>
			<div
				className={'text-input-wrapper'}
			>
				{
					showBtn && (
						<div
							className={'regenerate-btn'}
						>
							{showStop && <Button icon={<StopOutlined />} onClick={onStopQuestion}>停止生成</Button>}
							{!loading && <Button icon={<SyncOutlined />} onClick={onReSendQuestion}>重新生成</Button>}
						</div>
					)
				}
				<MobileQuestionInput
					ref={mobileQuestionInputRef}
					sceneType={sceneType}
					showStop={showStop}
					onUpdateData={onUpdateHandler}
					onSend={onSendHandler}
					onChange={() => {
						setVoiceShow(true);
					}}
					setOpenUpload={setOpenUpload}
					taskData={taskData}
					hasPendingImages={hasPendingImages}
				/>
			</div>
			<div
				className={'speech-wrapper'}
			>
				{
					voiceShow && (
						<SpeechRecognitionContainer
							onSend={(value: string) => {
								onSend({
									...questionsData,
									questions: value,
									sceneType
								});
							}}
							onClose={() => {
								setVoiceShow(false);
							}}
						/>
					)
				}
			</div>
		</div>
	);
});

export default MobileQuestionsAnswerView;
