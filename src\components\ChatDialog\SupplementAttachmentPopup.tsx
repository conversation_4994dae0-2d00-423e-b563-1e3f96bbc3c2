/*
 * @Description: 补充附件弹出层组件
 * @Features:
 *   - 显示检查项列表，支持图片上传
 *   - 根据chatAiCheck.records数据渲染检查项
 *   - 每个检查项都有独立的图片上传功能
 * @Author: AI Assistant
 * @Date: 2025-01-11
 */
import React, { useState } from 'react';
import { Popup, Button, Toast } from 'antd-mobile';
import { CameraOutline } from 'antd-mobile-icons';
import PhotoCapture from '@/components/PhotoCapture';
import { beforeUpload } from '@/util/method';
import './SupplementAttachmentPopup.less';

interface SupplementRecord {
  id: number;
  itemName: string;
  placeName: string;
  requirementName: string;
  checkPlaceName?: string;
  checkWay?: number;
}

interface UploadedImage {
  file: File;
  dataUrl: string;
  id: string;
}

interface SupplementAttachmentPopupProps {
  visible: boolean;
  onClose: () => void;
  records: SupplementRecord[];
  onConfirm: (uploadedFiles: { [recordId: number]: UploadedImage[] }) => void;
}

const SupplementAttachmentPopup: React.FC<SupplementAttachmentPopupProps> = ({
  visible,
  onClose,
  records,
  onConfirm
}) => {
  const [uploadedFiles, setUploadedFiles] = useState<{ [recordId: number]: UploadedImage[] }>({});
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [currentRecordId, setCurrentRecordId] = useState<number | null>(null);

  // 打开拍照组件
  const handleOpenPhotoCapture = (recordId: number) => {
    setCurrentRecordId(recordId);
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
    setCurrentRecordId(null);
  };

  // 处理拍照完成
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    if (currentRecordId === null) return;

    beforeUpload(file, (res: any) => {
      const newImage: UploadedImage = {
        file,
        dataUrl,
        id: res[0]?.id || Date.now().toString()
      };

      setUploadedFiles(prev => ({
        ...prev,
        [currentRecordId]: [...(prev[currentRecordId] || []), newImage]
      }));

      setShowPhotoCapture(false);
      setCurrentRecordId(null);
      Toast.show('图片上传成功');
    });
  };

  // 删除图片
  const handleDeleteImage = (recordId: number, imageIndex: number) => {
    setUploadedFiles(prev => ({
      ...prev,
      [recordId]: prev[recordId]?.filter((_, index) => index !== imageIndex) || []
    }));
  };

  // 确认提交
  const handleConfirmSubmit = () => {
    onConfirm(uploadedFiles);
    onClose();
  };

  // 取消
  const handleCancel = () => {
    setUploadedFiles({});
    onClose();
  };

  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={onClose}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          overflow: 'hidden'
        }}
      >
        <div className="supplement-attachment-popup">
          <div className="popup-header">
            <h3>补充附件</h3>
          </div>

          <div className="popup-content">
            {records.map((record) => (
              <div key={record.id} className="record-item">
                <div className="record-info">
                  <div className="record-title">
                    {record.placeName} &gt; {record.itemName}
                  </div>
                  <div className="record-requirement">
                    {record.requirementName}
                  </div>
                </div>

                <div className="upload-section">
                  <div
                    className="upload-btn"
                    onClick={() => handleOpenPhotoCapture(record.id)}
                  >
                    {/* <CameraOutline /> */}
                    <div className="upload-btn-icon">
                    </div>
                    <span>点击上传</span>
                  </div>

                  {/* 图片预览区域 */}
                  {uploadedFiles[record.id] && uploadedFiles[record.id].length > 0 && (
                    <div className="image-preview-list">
                      {uploadedFiles[record.id].map((image, index) => (
                        <div key={index} className="image-preview-item">
                          <img
                            src={image.dataUrl}
                            alt={`上传图片${index + 1}`}
                            className="preview-image"
                          />
                          <div
                            className="delete-btn"
                            onClick={() => handleDeleteImage(record.id, index)}
                          >
                            ×
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          <div className="popup-footer">
            <Button
              className="cancel-btn"
              onClick={handleCancel}
              fill="outline"
            >
              取消
            </Button>
            <Button
              className="confirm-btn"
              onClick={handleConfirmSubmit}
              color="primary"
            >
              确认
            </Button>
          </div>
        </div>
      </Popup>

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />
    </>
  );
};

export default SupplementAttachmentPopup;
