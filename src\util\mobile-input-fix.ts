/*
 * @Description: 移动端输入框被软键盘覆盖问题的修复工具
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */

interface MobileInputFixOptions {
  // 输入框容器选择器
  inputContainerSelector?: string;
  // 是否启用自动滚动
  enableAutoScroll?: boolean;
  // 滚动偏移量
  scrollOffset?: number;
  // iOS设备滚动减少系数 (0-1之间，越小调整越保守)
  iosScrollReduction?: number;
  // 调试模式
  debug?: boolean;
}

class MobileInputFix {
  private options: MobileInputFixOptions;
  private originalViewportHeight: number;
  private isKeyboardVisible: boolean = false;
  private activeInput: HTMLElement | null = null;

  constructor(options: MobileInputFixOptions = {}) {
    this.options = {
      inputContainerSelector: '.text-input-wrapper',
      enableAutoScroll: true,
      scrollOffset: 20,
      iosScrollReduction: 0.3, // iOS设备默认减少70%的滚动量
      debug: false,
      ...options
    };

    this.originalViewportHeight = window.innerHeight;
    this.init();
  }

  private init() {
    this.log('初始化移动端输入框修复工具');
    
    // 监听输入框焦点事件
    document.addEventListener('focusin', this.handleFocusIn.bind(this));
    document.addEventListener('focusout', this.handleFocusOut.bind(this));
    
    // 监听窗口大小变化
    window.addEventListener('resize', this.handleResize.bind(this));
    
    // 监听页面滚动
    window.addEventListener('scroll', this.handleScroll.bind(this));
  }

  private handleFocusIn(event: FocusEvent) {
    const target = event.target as HTMLElement;

    if (this.isInputElement(target)) {
      this.log('输入框获得焦点:', target);
      this.activeInput = target;

      // iOS设备延迟更长时间，确保键盘完全弹起
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
      const delay = isIOS ? 500 : 300;

      setTimeout(() => {
        this.handleKeyboardShow();
      }, delay);
    }
  }

  private handleFocusOut(event: FocusEvent) {
    const target = event.target as HTMLElement;
    
    if (this.isInputElement(target)) {
      this.log('输入框失去焦点:', target);
      
      // 延迟处理，避免在输入框间切换时误判
      setTimeout(() => {
        const activeElement = document.activeElement as HTMLElement;
        if (!this.isInputElement(activeElement)) {
          this.handleKeyboardHide();
          this.activeInput = null;
        }
      }, 100);
    }
  }

  private handleResize() {
    const currentHeight = window.innerHeight;
    const heightDiff = this.originalViewportHeight - currentHeight;
    
    this.log('窗口大小变化:', { currentHeight, heightDiff });
    
    // Android设备通过resize事件检测键盘
    if (heightDiff > 150 && !this.isKeyboardVisible) {
      this.handleKeyboardShow();
    } else if (heightDiff < 50 && this.isKeyboardVisible) {
      this.handleKeyboardHide();
    }
  }

  private handleScroll() {
    // 防止页面滚动时输入框位置错乱
    if (this.isKeyboardVisible && this.activeInput) {
      this.adjustInputPosition();
    }
  }

  private handleKeyboardShow() {
    if (this.isKeyboardVisible) return;
    
    this.log('键盘弹起');
    this.isKeyboardVisible = true;
    
    // 调整输入框位置
    this.adjustInputPosition();
    
    // 添加键盘可见的CSS类
    document.body.classList.add('keyboard-visible');
    
    // 设置CSS变量
    const keyboardHeight = this.getKeyboardHeight();
    document.documentElement.style.setProperty('--keyboard-height', `${keyboardHeight}px`);
  }

  private handleKeyboardHide() {
    if (!this.isKeyboardVisible) return;
    
    this.log('键盘收起');
    this.isKeyboardVisible = false;
    
    // 移除键盘可见的CSS类
    document.body.classList.remove('keyboard-visible');
    
    // 清除CSS变量
    document.documentElement.style.removeProperty('--keyboard-height');
    
    // 恢复页面滚动位置
    window.scrollTo(0, 0);
  }

  private adjustInputPosition() {
    if (!this.activeInput || !this.options.enableAutoScroll) return;

    const inputContainer = this.activeInput.closest(this.options.inputContainerSelector!) as HTMLElement;
    if (!inputContainer) return;

    // 获取输入框容器的位置信息
    const rect = inputContainer.getBoundingClientRect();
    const keyboardHeight = this.getKeyboardHeight();
    const viewportHeight = window.innerHeight;

    this.log('调整输入框位置:', { rect, keyboardHeight, viewportHeight });

    // iOS设备特殊处理：减少调整幅度
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    // 计算输入框是否被键盘覆盖
    const inputBottom = rect.bottom;
    const availableHeight = viewportHeight - keyboardHeight;

    if (inputBottom > availableHeight) {
      // 输入框被覆盖，需要滚动
      let scrollAmount = inputBottom - availableHeight + (this.options.scrollOffset || 20);

      // iOS设备减少滚动量，避免抬起太高
      if (isIOS) {
        const reductionFactor = this.options.iosScrollReduction || 0.3;
        scrollAmount = scrollAmount * reductionFactor;
        this.log('iOS设备，减少滚动量 (系数:', reductionFactor, '):', scrollAmount);
      }

      this.log('需要滚动:', scrollAmount);

      // 平滑滚动
      window.scrollBy({
        top: scrollAmount,
        behavior: 'smooth'
      });
    }
  }

  private getKeyboardHeight(): number {
    const currentHeight = window.innerHeight;
    const calculatedHeight = Math.max(0, this.originalViewportHeight - currentHeight);

    // iOS设备的键盘高度检测优化
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);

    if (isIOS) {
      // iOS设备使用更保守的键盘高度估算
      // 通常iOS键盘高度约为视口高度的35-40%
      const estimatedHeight = this.originalViewportHeight * 0.35;

      // 如果计算出的高度过大，使用估算值
      if (calculatedHeight > estimatedHeight * 1.5) {
        this.log('iOS设备键盘高度过大，使用估算值:', estimatedHeight);
        return estimatedHeight;
      }

      // 如果计算出的高度太小，可能是检测不准确
      if (calculatedHeight < 100 && this.isKeyboardVisible) {
        this.log('iOS设备键盘高度太小，使用最小值:', estimatedHeight);
        return estimatedHeight;
      }
    }

    return calculatedHeight;
  }

  private isInputElement(element: HTMLElement): boolean {
    if (!element) return false;
    
    const tagName = element.tagName.toLowerCase();
    const isInput = tagName === 'input' || tagName === 'textarea';
    const isContentEditable = element.contentEditable === 'true';
    const hasInputRole = element.getAttribute('role') === 'textbox';
    
    return isInput || isContentEditable || hasInputRole;
  }

  private log(...args: any[]) {
    if (this.options.debug) {
      console.log('[MobileInputFix]', ...args);
    }
  }

  // 手动触发输入框位置调整
  public adjustInput(inputElement?: HTMLElement) {
    if (inputElement) {
      this.activeInput = inputElement;
    }
    this.adjustInputPosition();
  }

  // 获取当前状态
  public getStatus() {
    return {
      isKeyboardVisible: this.isKeyboardVisible,
      keyboardHeight: this.getKeyboardHeight(),
      activeInput: this.activeInput
    };
  }

  // 销毁实例
  public destroy() {
    document.removeEventListener('focusin', this.handleFocusIn);
    document.removeEventListener('focusout', this.handleFocusOut);
    window.removeEventListener('resize', this.handleResize);
    window.removeEventListener('scroll', this.handleScroll);
    
    document.body.classList.remove('keyboard-visible');
    document.documentElement.style.removeProperty('--keyboard-height');
    
    this.log('移动端输入框修复工具已销毁');
  }
}

// 全局实例
let globalMobileInputFix: MobileInputFix | null = null;

// 初始化
export function initMobileInputFix(options?: MobileInputFixOptions): MobileInputFix {
  if (globalMobileInputFix) {
    globalMobileInputFix.destroy();
  }
  
  globalMobileInputFix = new MobileInputFix(options);
  return globalMobileInputFix;
}

// 获取全局实例
export function getMobileInputFix(): MobileInputFix | null {
  return globalMobileInputFix;
}

// 销毁全局实例
export function destroyMobileInputFix() {
  if (globalMobileInputFix) {
    globalMobileInputFix.destroy();
    globalMobileInputFix = null;
  }
}

export default MobileInputFix;
