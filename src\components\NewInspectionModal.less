.new-inspection-modal {
  background-color: #fff;
  border-radius: 12px 12px 0 0;
  overflow: hidden;

  // 头部样式
  .modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #fff;

    .modal-title {
      font-size: 17px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .header-tabs {
        display: flex;
        border-radius: 6px;
        padding: 2px;

        .header-tab-btn {
          padding: 6px 12px;
          font-size: 15px;
          color: rgba(0, 0, 0, 0.60);
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;

          &.active {
            background: #4285f4;
            color: white;
            font-weight: 500;
          }

          &:hover:not(.active) {
            background: #e8f0fe;
            color: #4285f4;
          }
        }
        .header-tab-btn-left{
          background: url('../assets/newInsp/tab_left.png') no-repeat center center;
          background-size: 100% 100%;
          &.active {
            background: url('../assets/newInsp/tab_left_sel.png') no-repeat center center;
            background-size: 100% 100%;
            color: #fff;
          }
        }
        .header-tab-btn-right{
          background: url('../assets/newInsp/tab_right.png') no-repeat center center;
          background-size: 100% 100%;
          &.active {
            background: url('../assets/newInsp/tab_right_sel.png') no-repeat center center;
            background-size: 100% 100%;
            color: #fff;
          }
        }
      }

      .close-icon {
        font-size: 20px;
        color: #999;
        cursor: pointer;

        &:hover {
          color: #666;
        }
      }
    }
  }

  // 内容区域
  .modal-content {
    padding: 20px;
    padding-bottom: 120px; // 为固定按钮留出空间
    max-height: calc(80vh - 120px); // 减去头部和按钮高度
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    .form-submission {
      background-color: #fff;
      padding: 0px;
      .form-footer{
        display: none;
      }
    }

    // 检查处所名称
    .location-section {
      margin-bottom: 20px;
      // top: 0;
      // left: 0;
      // right: 0;
      // bottom: 0;

      .location-input {
        background: rgba(202, 217, 249, 1);
        // border-radius: 8px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        width: 100vw;
        margin-left: -6%;

        .location-label {
          font-size: 14px;
          color: rgba(61, 61, 61, 1);
          margin-right: 8px;
          min-width: 100px;
        }

        .location-display {
          display: flex;
          align-items: center;
          flex: 1;

          .location-value {
            font-size: 14px;
            color: rgba(61, 61, 61, 1);
            font-weight: 500;
            background: rgba(255, 255, 255, 1);
            border-radius: 4px;
            padding: 4px 8px;
            flex: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            max-width: 220px;
          }

          .location-edit-icon {
            font-size: 16px;
            color: #4285f4;
            cursor: pointer;
            margin-left: 8px;

            &:hover {
              color: #3367d6;
            }
          }
        }

        .location-edit {
          display: flex;
          align-items: center;
          flex: 1;

          .location-input-field {
            flex: 1;
            border: 1px solid #d9d9d9;
            background: rgba(255, 255, 255, 1);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 14px;

            &:focus {
              border-color: #4285f4;
              box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.2);
            }
          }

          .location-save-icon {
            font-size: 16px;
            color: #52c41a;
            cursor: pointer;
            margin-left: 8px;

            &:hover {
              color: #389e0d;
            }
          }
        }
      }
    }

    // 标签页导航
    .tab-navigation {
      display: flex;
      margin-bottom: 20px;
      background: #f8f9fa;
      border-radius: 8px;
      padding: 4px;

      .tab-btn {
        flex: 1;
        padding: 8px 16px;
        border: none;
        background: transparent;
        border-radius: 6px;
        font-size: 14px;
        color: #666;
        cursor: pointer;
        transition: all 0.3s ease;

        &.active {
          background: #4285f4;
          color: white;
          font-weight: 500;
        }

        &:hover:not(.active) {
          background: rgba(66, 133, 244, 0.1);
          color: #4285f4;
        }
      }
    }

    // 智能分析模式
    .analysis-mode {
      .requirements-section {
        margin-bottom: 20px;
        border: 1px solid rgba(72, 115, 255, 0.2);
      }

      // 检查项分组样式
      .check-section {
        margin-bottom: 24px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

        &.photo-section {
          background: linear-gradient( 90deg, #EBF3F9 0%, #fff 100%);
          box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
          border-radius: 9px 9px 9px 9px;
          border: 1px solid #EBF3F9;
        }

        &.voice-section {
          background: linear-gradient( 90deg, #EBF3F9 0%, #fff 100%);
          box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
          border-radius: 9px 9px 9px 9px;
          border: 1px solid #EBF3F9;
        }

        .section-header {
          padding: 12px 16px;
          border-bottom: 1px solid #f0f0f0;

          .section-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
          }

          .photo-section & {
            background: #e6f3ff;
            color: #1890ff;
          }

          .voice-section & {
            background: #ffe6f0;
            color: #eb2f96;
          }
        }

        .requirements-list {
          padding: 16px;

          .requirement-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            .requirement-number {
              color: #666;
              font-size: 14px;
              // margin-right: 8px;
              flex-shrink: 0;
              margin-top: 2px;
            }

            .requirement-content {
              color: #333;
              font-size: 15px;
              line-height: 1.5;
              flex: 1;
            }
          }
        }

        .analysis-results {
          padding: 16px;
          border-top: 1px solid #f0f0f0;
          background: rgba(255, 255, 255, 0.8);

          .results-header {
            font-size: 13px;
            font-weight: 600;
            color: #666;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #f0f0f0;
          }

          .analysis-item {
            position: relative;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 12px;
            border: 1px solid #e9ecef;

            &:last-child {
              margin-bottom: 0;
            }

            .analysis-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .analysis-title {
                display: flex;
                align-items: center;
                flex: 1;

                .analysis-number {
                  color: #666;
                  font-size: 14px;
                  margin-right: 4px;
                  flex-shrink: 0;
                }

                .analysis-requirement {
                  color: #333;
                  font-size: 14px;
                  flex: 1;
                }
              }

              .analysis-status {
                font-size: 14px;
                font-weight: 600;
                padding: 4px 8px;
                border-radius: 4px;
                flex-shrink: 0;

                &.normal {
                  background: #f6ffed;
                  color: #52c41a;
                  border: 1px solid #b7eb8f;
                }

                &.hazard {
                  background: #fff2f0;
                  color: #ff4d4f;
                  border: 1px solid #ffccc7;
                }
              }
            }

            .analysis-result {
              color: #333;
              font-size: 18px;
              font-weight: 600;
              line-height: 1.4;
              margin-left: 0;
            }
          }
        }

        .uploaded-images {
          padding: 16px;
          border-top: 1px solid #f0f0f0;
          background: rgba(255, 255, 255, 0.8);

          .image-item {
            position: relative;
            display: inline-block;
            margin-right: 12px;
            margin-bottom: 12px;

            img {
              width: 80px;
              height: 80px;
              object-fit: cover;
              border-radius: 8px;
              border: 1px solid #f0f0f0;
            }

            .image-actions {
              position: absolute;
              top: -8px;
              right: -8px;

              .delete-overlay {
                width: 20px;
                height: 20px;
                background: #ff4d4f;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                color: white;
                font-size: 12px;
              }
            }
          }
        }

        .section-actions {
          padding: 16px;

          .action-btn {
            height: 44px;
            border-radius: 8px;
            font-size: 15px;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            border-radius: 22px 22px 22px 22px;
            border: 1px solid #4873FF;
            background: transparent;
            span{
              display: flex;
              align-items: center;
              justify-content: center;
            }
            &.photo-btn {
              // background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              // border: none;
              color: rgba(72, 115, 255, 1);
            }
            .photo-btn-icon{
              background: url('../assets/newInsp/icon_camera.png') no-repeat center center;
              background-size: 100% 100%;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }

            &.voice-btn {
              // background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
              // border: none;
              color: rgba(72, 115, 255, 1);
            }
            .voice-btn-icon{
              background: url('../assets/newInsp/icon_say.png') no-repeat center center;
              background-size: 100% 100%;
              width: 20px;
              height: 20px; 
              margin-right: 4px;
            }
          }
        }
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .action-btn {
          height: 48px;
          border-radius: 8px;
          font-size: 16px;
          font-weight: 500;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          &.photo-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
          }

          &.voice-btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border: none;
            color: white;
          }
        }
      }
    }

    // 表单填报模式
    .form-mode {
      // 表单样式继承FormSubmission组件的样式
      padding: 0;
    }

    // 底部按钮
    .modal-footer {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      display: flex;
      gap: 12px;
      padding: 16px 20px;
      padding-bottom: calc(16px + env(safe-area-inset-bottom));
      background: #fff;
      border-top: 1px solid #f0f0f0;
      z-index: 1000;

      .cancel-btn {
        flex: 1;
        height: 44px;
        border-radius: 28px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(72, 115, 255, 1);
        background: rgba(217, 225, 248, 1);
        border: none;
      }

      .confirm-btn {
        flex: 1;
        height: 44px;
        border-radius: 2cqh;
        font-size: 16px;
        border-radius: 1.375rem;
        font-size: 0.9375rem;
        font-weight: 500;
        background: linear-gradient(315deg, rgba(35, 60, 138, 0.9) 0%, rgba(72, 115, 255, 0.9) 100%);
        border: none;
        color: white;

        &:active {
          background: #2c5aa0;
          border-color: #2c5aa0;
        }
      }
    }

    // 检查项目列表
    .check-items {
      margin-bottom: 24px;

      .check-item {
        margin-bottom: 20px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f5f5f5;

        &:last-child {
          border-bottom: none;
          margin-bottom: 0;
          padding-bottom: 0;
        }

        .item-title {
          font-size: 14px;
          color: #333;
          margin-bottom: 12px;
          font-weight: 500;
        }

        // 选项按钮
        .option-buttons {
          display: flex;
          gap: 12px;

          .option-btn {
            min-width: 60px;
            height: 32px;
            border-radius: 16px;
            font-size: 13px;
            font-weight: 500;
            border: 1px solid #e8e8e8;
            background-color: #fff;
            color: #666;

            &.active {
              background-color: #e6f7ff;
              border-color: #91d5ff;
              color: #1890ff;
            }

            &:hover {
              border-color: #40a9ff;
            }

            .adm-button-content {
              font-size: 13px;
            }
          }
        }

        // 数量输入
        .count-input {
          .count-controls {
            display: flex;
            align-items: center;
            gap: 16px;

            .count-btn {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              border: 1px solid #d9d9d9;
              background-color: #fff;
              color: #666;
              font-size: 16px;
              font-weight: 500;
              display: flex;
              align-items: center;
              justify-content: center;

              &:hover {
                border-color: #40a9ff;
                color: #1890ff;
              }

              &:disabled {
                background-color: #f5f5f5;
                border-color: #e8e8e8;
                color: #ccc;
              }
            }

            .count-value {
              font-size: 18px;
              font-weight: 600;
              color: #333;
              min-width: 40px;
              text-align: center;
            }
          }
        }
      }
    }

    // 操作按钮区域
    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 12px;

      .action-btn {
        height: 48px;
        border-radius: 24px;
        font-size: 15px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &.photo-btn {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
          border: none;
          color: white;

          &:active {
            background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
          }
        }

        &.voice-btn {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          border: none;
          color: white;

          &:active {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
          }
        }
      }
    }
  }
  // 检查要求区域
  .requirements-section {
    margin: 8px 16px;
    border-radius: 12px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    background: linear-gradient( 90deg, #EBF3F9 0%, #fff 100%);
    box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
    border: 1px solid rgba(72, 115, 255, 0.2);
    border-radius: 9px 9px 9px 9px;
  .requirements-list {

    .requirement-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-radius: 8px;

      .requirement-number {
        // font-weight: 600;
        color: #333;
        // margin-right: 8px;
        min-width: 16px;
      }

      .requirement-content {
        font-size: 15px;
        color: #333;
        line-height: 1.4;
      }
    }
  }

  
  }
  // AI分析结果区域
  .analysis-sections {

    .analysis-results {
      margin-bottom: 16px;

      .analysis-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        padding: 12px 0;
        border-bottom: 1px solid #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        .analysis-number {
          font-weight: 600;
          color: #333;
          margin-right: 8px;
          min-width: 20px;
        }

        .analysis-requirement {
          font-size: 14px;
          color: #333;
          flex: 1;
        }

        .analysis-result {
          font-size: 14px;
          font-weight: 600;
          color: #333;
        }
      }
    }

    .analysis-actions {
      .rerecognize-btn {
        height: 48px;
        border-radius: 24px;
        font-size: 16px;
        font-weight: 500;
        background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
        border: none;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;

        &:active {
          background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
        }
      }
    }
  }
  .uploaded-images {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    overflow-x: auto;
    padding: 4px 0;

    .image-item {
      position: relative;
      width: 80px;
      height: 80px;
      border-radius: 8px;
      flex-shrink: 0;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .image-actions {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .play-overlay {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 24px;
        height: 24px;
        background-color: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 12px;
        z-index: 1;
      }

      .delete-overlay {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 20px;
        height: 20px;
        background-color: rgba(255, 77, 79, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 10px;
        cursor: pointer;
        z-index: 2;

        &:hover {
          background-color: rgba(255, 77, 79, 1);
        }
      }
    }
  }
}

// 自定义Popup样式
.adm-popup {
  .adm-popup-body {
    border-radius: 12px 12px 0 0;
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
  }

  .adm-popup-mask {
    z-index: 999;
  }
}

// 响应式适配
@media (max-width: 375px) {
  .new-inspection-modal {
    .modal-content {
      padding: 16px;
      padding-bottom: 120px; // 为固定按钮留出空间
      .check-items {
        .check-item {
          .option-buttons {
            gap: 8px;

            .option-btn {
              min-width: 50px;
              height: 28px;
              font-size: 12px;
            }
          }

          .count-input {
            .count-controls {
              gap: 12px;

              .count-btn {
                width: 28px;
                height: 28px;
                font-size: 14px;
              }

              .count-value {
                font-size: 16px;
                min-width: 32px;
              }
            }
          }
        }
      }

      .action-buttons {
        .action-btn {
          height: 44px;
          font-size: 14px;
        }
      }
    }
  }
}
