/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-06 09:42:32
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-16 17:53:00
 * @FilePath: src/tools/questions-answer-tool/index.js
 * @Version: 1.0.0
 * @Description: 组件描述 问答功能封装
 */
import { apis, chatPath } from '@/api/api';
import SceneType from '@/enums/scene-type';
import { destroy } from '@/tools/speech-recognition-xfyun';
import { UPLOAD_CONFIG } from '@/util/const';
import { getGin, getHtml, getReqId } from '@/util/util';
import { message } from 'antd';
import { EventSourcePolyfill } from 'event-source-polyfill';
import lodash from 'lodash';

let eventSource = null;

// 滚动到问答列表最底部
const qaListScrollBottom = () => {
	const div = document.getElementById('qa-list');
	if (div) {
		div.scrollTop = div.scrollHeight;
	}
};

const getSourceDocuments = (reqId, list, setQaList) => {
	apis.getApi.getSourceDocuments({ reqId }).then(res => {
		if (res.code === 0) {
			list[list.length - 2].chatSources = res.data || [];
			list[list.length - 1].chatSources = res.data || [];
			setQaList([...list]);
			setTimeout(() => {
				qaListScrollBottom();
			}, 100);
		}
	});
};

const getWebSearch = (reqId, list, setQaList) => {
	apis.getApi.getWebSearch({ reqId }).then(res => {
		if (res.code === 0) {
			list[list.length - 2].chatWebSearches = res.data || [];
			list[list.length - 1].chatWebSearches = res.data || [];
			setQaList(list);
			setTimeout(() => {
				qaListScrollBottom();
			}, 100);
		}
	});
};

const getRelvantAndQestions = (list, setRecommendList) => {
	let params = {
		query: list[list.length - 2].chatContents[0].content,
		context: list[list.length - 1].chatContents[0].content
	};
	apis.postApi.getRelvantAndQestions(params).then(res => {
		if (res.code === 0 && res.data && res.data.code === 200) {
			setRecommendList(res.data.data.questions);
			setTimeout(() => {
				qaListScrollBottom();
			}, 100);
		}
	});
};

const getPluginsInfo = (reqId, list, setPluginsLoading, setQaList, setCompanyInfo) => {
	let params = {
		reqId
	};
	setPluginsLoading(true);
	apis.getApi.getPluginsInfo(params).then(res => {
		if (res.code === 0 && res.data && res.data.length > 0) {
			if (res.data.length > 1) {
				list[list.length - 2].chatPlugins = res.data || [];
			}
			list[list.length - 2].chatPlugins = res.data || [];
			list[list.length - 1].chatPlugins = res.data || [];
			setQaList(JSON.parse(JSON.stringify(list)));
			setTimeout(() => {
				qaListScrollBottom();
			}, 100);

			if (res.data && res.data.extra) {
				const pluginData = JSON.parse(res.data?.extra) || {};
				for (let key in pluginData) {
					const item = pluginData[key];
					if (key === 'company') {
						setCompanyInfo(item);
					}
				}
			}
		}
		setPluginsLoading(false);
	});
};

/**
 * 获取答案，处理eventsource返回答案
 * @param url
 * @param reqId
 * @param list
 */
const getAnswer = lodash.throttle((url, reqId, list, opts) => {
	const {
		diaActive,
		qaList,
		qaListLoading,
		isOpenWebSearch,
		isNextRecommend,
		isUseKb,
		functions,
		dispatch,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		getQaList
	} = opts;
	dispatch({ type: 'isChat', data: true });
	setLoading(true);
	setRecommendList([]);
	let answer;
	const { accessToken } = getGin();
	eventSource = new EventSourcePolyfill(url, {
		headers: {
			'Authorization': `Bearer ${accessToken}`
		},
	});
	qaListScrollBottom();
	eventSource.addEventListener('message', function (event) {
		setShowStop(true);
		let list = [...qaList];
		let obj = {};
		try {
			obj = JSON.parse(event.data) || {};
		} catch {
			obj = {};
		}
		if (obj.content) {
			if (obj.type === 'path') {
				list[list.length - 1].chatContents[0].content = getHtml(obj.content);
			} else {
				list[list.length - 1].chatContents[0].content = obj.content;
			}
			answer = list[list.length - 1].chatContents[0].content;
			qaListScrollBottom();
		} else if (obj.answerId && !qaListLoading) { // 停止或结束时返回答案id拼接到问答列表
			list[list.length - 1] = {
				id: obj.answerId,
				parentId: obj.questionId,
				chatContents: [{ content: answer }],
				status: obj.status || 0,
				mark: 0,
				senderType: 'assistant'
			};
			list[list.length - 2].id = obj.questionId;
			dispatch({ type: 'isChat', data: false });
			dispatch({ type: 'getLimit', data: true });
			if (isNextRecommend) {
				getRelvantAndQestions(list, setRecommendList);
			}
			if (isOpenWebSearch) {
				getWebSearch(reqId, list, setQaList);
			}
			if (isUseKb === 1) {
				getSourceDocuments(reqId, list, setQaList);
			} else if (functions) {
				getPluginsInfo(reqId, list, setPluginsLoading, setQaList, setCompanyInfo);
			}
		} else if (obj.code === 1000 || obj.code === 2201) {
			message.error(obj.message);
		}
		setQaList(list);
	});


	eventSource.onerror = function (error) {
		setLoading(false);
		setShowStop(false);
		eventSource.close();
		dispatch({ type: 'isChat', data: false });
		dispatch({ type: 'getLimit', data: true });
		getQaList(diaActive, true, false);
		eventSource.removeEventListener('message');
	};
}, 1500);

/**
 * 发送问题
 * @param questions 问答文字
 * @param fileList 上传的图片或文本
 * @param opts 其他参数
 *
 */
const sendQuestions = lodash.throttle(async (questions, fileList, opts) => {
	const {
		limit,
		isChat,
		diaActive,
		sceneType,
		qaList,
		libList,
		qaListLoading,
		isOpenThinking,
		isOpenWebSearch,
		isCarryHistory,
		isNextRecommend,
		isUseKb,
		functions,
		file,
		setReqId,
		setLoading,
		setQaList,
		setQaListLoading,
		setDiaActive,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setFile,
		dispatch,
		getQaList
	} = opts;
	if (limit) {
		return message.info('今日体验次数已用完，开通会员可享不限次数访问。');
	}
	if (isChat) {
		return message.info('当前对话正在进行中');
	}
	if (/^\s*$/.test(questions) && !fileList.length) {
		return message.info('发送内容不能为空');
	}
	setLoading(true);
	dispatch({ type: 'isChat', data: true });
	let id = diaActive;
	const reqId = getReqId();
	const origin = window.location.origin;
	let fileIds;
	// AI编题多轮对话携带第一轮文件信息
	if (sceneType === SceneType.SET_EXAMS) {
		fileIds = !!qaList.length && qaList[0].chatFiles ? qaList[0].chatFiles[0].fileId : fileList.map(file => {
			return file.id;
		});
	} else {
		fileIds = fileList.map(file => {
			return file.id;
		});
	}
	let fileKey = !!fileList.length ? fileList[0].fileKey : 'chatFiles';
	let fileParam = !!fileList.length ? fileList[0]?.fileParam : 'fileIds';
	let url;
	let newQa;
	if (sceneType === SceneType.MY_KNOWLEDGE_BASE) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${fileList.map(file => {
			return file.id;
		})}&params=${encodeURIComponent(JSON.stringify({ libIds: libList.map(lib => lib.id).join(',') }))}`;
		newQa = [
			{
				id: 0,
				chatContents: [{ content: questions }],
				chatFiles: [...fileList],
				status: 0,
				mark: 0,
				senderType: 'user'
			},
			{ id: -1, chatContents: [{ content: '' }], status: 0, mark: 0, senderType: 'assistant' },
		];
	} else if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${file.fileId}`;
		newQa = [
			{
				id: 0,
				chatContents: [{ content: questions }],
				chatFiles: [{ ...file }],
				status: 0,
				mark: 0,
				senderType: 'user'
			},
			{ id: -1, chatContents: [{ content: '' }], status: 0, mark: 0, senderType: 'assistant' },
		];
	} else {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&${fileParam}=${fileIds}&isOpenThinking=${isOpenThinking}&isOpenWebSearch=${isOpenWebSearch}`;
		newQa = [
			{
				id: 0,
				chatContents: [{ content: questions }],
				[fileKey]: !!fileList.length ? [...fileList] : null,
				status: 0,
				mark: 0,
				senderType: 'user'
			},
			{
				id: -1,
				chatContents: [{ content: '' }],
				status: 0,
				mark: 0,
				senderType: 'assistant'
			},
		];
	}
	setReqId(reqId);
	if (diaActive === 0) {
		// 新建会话
		let list = [...newQa];
		setLoading(true);
		setQaList(list);
		const sessionName = questions || fileList[0].fileName;
		let res1 = await apis.postApi.createSession({
			name: sessionName.toString().substring(0, 100),
			scene: sceneType || undefined
		});
		if (res1.code === 0) {
			let res2 = await apis.getApi.getSessionListByUser({
				pageNo: 1,
				pageSize: 100,
				scene: sceneType || undefined
			});
			if (res2.code === 0) {
				const diaList = res2.data?.list || [];
				id = diaList[0].id;
				setDiaActive(id);
				if (sceneType === SceneType.MY_KNOWLEDGE_BASE) {
					url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${fileList.map(file => {
						return file.id;
					})}&params=${encodeURIComponent(JSON.stringify({ libIds: libList.map(lib => lib.id).join(',') }))}`;
				} else if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
					url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}`;
				} else {
					url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(questions)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&${fileParam}=${fileIds}&isOpenThinking=${isOpenThinking}&isOpenWebSearch=${isOpenWebSearch}`;
				}
				getAnswer(url, reqId, list, {
					diaActive,
					qaList: list,
					qaListLoading,
					isOpenWebSearch,
					isNextRecommend,
					isUseKb,
					functions,
					dispatch,
					setLoading,
					setRecommendList,
					setShowStop,
					setPluginsLoading,
					setCompanyInfo,
					setQaList,
					getQaList
				});
			}
		}
	} else {
		let res = await apis.getApi.getQaList({ sessionId: diaActive });
		let list = [
			...res.data || [],
			...newQa
		];
		setQaList(list);
		setTimeout(() => {
			getAnswer(url, reqId, list, {
				diaActive,
				qaList: list,
				qaListLoading,
				isOpenWebSearch,
				isNextRecommend,
				isUseKb,
				functions,
				dispatch,
				setLoading,
				setRecommendList,
				setShowStop,
				setPluginsLoading,
				setCompanyInfo,
				setQaList,
				getQaList
			});
		}, 500);
	}
}, 1500);

/**
 * 重新发送问题
 */
const reSendQuestions = lodash.throttle(async (opts) => {
	const {
		limit,
		isChat,
		isCarryHistory,
		isNextRecommend,
		isUseKb,
		functions,
		libList,
		file,
		qaListLoading,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		setReqId,
		dispatch,
		diaActive,
		isOpenThinking,
		isOpenWebSearch,
		sceneType,
		getQaList
	} = opts;
	if (limit) return message.info('今日体验次数已用完，开通会员可享不限次数访问。');
	if (isChat) return message.info('当前对话正在进行中');
	setLoading(true);
	dispatch({ type: 'isChat', data: true });
	let res = await apis.getApi.getQaList({ sessionId: diaActive });
	let list = res.data || [];
	if (!!list.length) {
		list[list.length - 1].chatContents[0].content = '';
		list[list.length - 1].chatPlugins = null;
		list[list.length - 1].chatSources = null;
		list[list.length - 1].chatWebSearches = null;
		list[list.length - 2].chatPlugins = null;
		list[list.length - 2].chatSources = null;
		list[list.length - 2].chatWebSearches = null;
	}
	setQaList(res.data || []);
	const origin = window.location.origin;
	let fileKey = 'chatFiles';
	let fileParam = 'fileIds';
	const uploadList = UPLOAD_CONFIG[sceneType]?.uploadList || [];
	uploadList.map(item => {
		if (list[list.length - 2][item.fileKey]) {
			fileKey = item.fileKey;
			fileParam = item.fileParam;
		}
	});

	const question = list[list.length - 2]?.chatContents ? list[list.length - 2]?.chatContents[0]?.content : '';
	const fileIds = list[list.length - 2][fileKey] ? list[list.length - 2][fileKey][0].fileId : '';
	const reqId = getReqId();
	setReqId(reqId);
	let url;
	if (sceneType === SceneType.MY_KNOWLEDGE_BASE) {
		url = `${origin}${chatPath}/v2/chat/reSendQuestion?reqId=${reqId}&sessionId=${diaActive}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${fileIds}&params=${encodeURIComponent(JSON.stringify({ libIds: libList.map(lib => lib.id).join(',') }))}`;
	} else if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
		url = `${origin}${chatPath}/v2/chat/reSendQuestion?reqId=${reqId}&sessionId=${diaActive}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${file.fileId}`;
	} else {
		url = `${origin}${chatPath}/v2/chat/reSendQuestion?reqId=${reqId}&sessionId=${diaActive}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&${fileParam}=${fileIds}&isOpenThinking=${isOpenThinking}&isOpenWebSearch=${isOpenWebSearch}`;
	}
	getAnswer(url, reqId, list, {
		diaActive,
		qaList: res.data || [],
		qaListLoading,
		isOpenWebSearch,
		isNextRecommend,
		isUseKb,
		functions,
		dispatch,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		getQaList
	});
}, 1500);

/**
 * 推荐问题
 */
const sendRecomment = lodash.throttle((l, opts) => {
	const {
		limit,
		isCarryHistory,
		isNextRecommend,
		isUseKb,
		functions,
		qaList,
		libList,
		qaListLoading,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		setReqId,
		dispatch,
		diaActive,
		isOpenThinking,
		isOpenWebSearch,
		sceneType,
		getQaList
	} = opts;
	if (limit) return message.info('今日体验次数已用完，开通会员可享不限次数访问。');
	let question = l;
	let list = [];
	list = [
		...qaList,
		{ id: 0, chatContents: [{ content: question }], status: 0, mark: 0, senderType: 'user' },
		{ id: -1, chatContents: [{ content: '' }], status: 0, mark: 0, senderType: 'assistant' },
	];
	setQaList(list);

	let id = diaActive;

	const reqId = getReqId();
	setReqId(reqId);
	const origin = window.location.origin;
	let url;
	if (sceneType === SceneType.MY_KNOWLEDGE_BASE) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}`;
	} else if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}`;
	} else {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&isOpenThinking=${isOpenThinking}&isOpenWebSearch=${isOpenWebSearch}`;
	}
	getAnswer(url, reqId, list, {
		diaActive,
		qaList: list,
		qaListLoading,
		isOpenWebSearch,
		isNextRecommend,
		isUseKb,
		functions,
		dispatch,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		getQaList
	});
}, 1500);

/**
 * 新的对话
 */
const sendNewChat = lodash.throttle(async (item, opts) => {
	const {
		limit,
		isCarryHistory,
		isNextRecommend,
		isUseKb,
		functions,
		qaList,
		libList,
		qaListLoading,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		setReqId,
		setFile,
		setDiaActive,
		dispatch,
		diaActive,
		isOpenThinking,
		isOpenWebSearch,
		sceneType,
		getQaList
	} = opts;
	if (limit) return message.info('今日体验次数已用完，开通会员可享不限次数访问。');
	setLoading(true);
	setDiaActive(-1);
	if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
		setFile({
			...item,
			fileType: item.filePath.split('.')[1] === 'pdf' ? 'pdf' : 'docx'
		});
	}
	let question = item.content;
	let list = [];
	let fileKey = 'chatFiles';
	let fileParam = 'fileIds';
	if (sceneType === SceneType.MY_KNOWLEDGE_BASE || sceneType === SceneType.ENTERPRISE_CUSTOM) {
		list = [
			{
				id: 0,
				chatContents: [{ content: question }],
				chatFiles: item.fileId ? [{ ...item, fileUrl: item.filePath }] : null,
				status: 0,
				mark: 0,
				senderType: 'user'
			},
			{ id: -1, chatContents: [{ content: '' }], status: 0, mark: 0, senderType: 'assistant' },
		];
	} else {
		list = [
			{
				id: 0,
				chatContents: [{ content: question }],
				[fileKey]: item.fileId ? [{ ...item, fileUrl: item.filePath }] : null,
				status: 0,
				mark: 0,
				senderType: 'user'
			},
			{ id: -1, chatContents: [{ content: '' }], status: 0, mark: 0, senderType: 'assistant' },
		];
	}
	setQaList(list);

	let id = diaActive;
	if (id === 0) { // 新建会话
		setLoading(true);
		let name;
		if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
			const title = item.title || item.content;
			name = title.toString().substring(0, 100);
		} else {
			name = question.toString().substring(0, 100);
		}
		const resData = await apis.postApi.createSession({
			name,
			scene: sceneType || undefined
		});
		if (resData.code === 0) {
			// 历史对话
			let aResData = await apis.getApi.getSessionListByUser({
				pageNo: 1,
				pageSize: 100,
				scene: sceneType || undefined
			});
			if (aResData.code === 0) {
				const list = aResData.data?.list || [];
				id = list[0].id;
				setDiaActive(id);
			}
		}
	}
	const reqId = getReqId();
	setReqId(reqId);
	const origin = window.location.origin;
	let url;
	if (sceneType === SceneType.MY_KNOWLEDGE_BASE) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${item.fileId || ''}&params=${encodeURIComponent(JSON.stringify({ libIds: libList.map(lib => lib.id).join(',') }))}&isRecommend=1`;
	} else if (sceneType === SceneType.ENTERPRISE_CUSTOM) {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&fileIds=${item.fileId || ''}&isRecommend=1`;
	} else {
		url = `${origin}${chatPath}/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${id}&question=${encodeURIComponent(question)}&scene=${sceneType}&isCarryHistory=${isCarryHistory}&${fileParam}=${item.fileId || ''}&isRecommend=1&isOpenThinking=${isOpenThinking}&isOpenWebSearch=${isOpenWebSearch}`;
	}
	getAnswer(url, reqId, list, {
		diaActive,
		qaList: list,
		qaListLoading,
		isOpenWebSearch,
		isNextRecommend,
		isUseKb,
		functions,
		dispatch,
		setLoading,
		setRecommendList,
		setShowStop,
		setPluginsLoading,
		setCompanyInfo,
		setQaList,
		getQaList
	});
}, 1500);

/**
 * 停止生成
 * @param id 对话id
 */
const stopQuestion = (id) => {
	apis.putApi.stopQuestion(id);
};

const destroyEvent = () => {
	eventSource?.removeEventListener('message');
	eventSource = null;
};

export {
	sendQuestions,
	reSendQuestions,
	sendRecomment,
	stopQuestion,
	sendNewChat,
	destroyEvent,
};