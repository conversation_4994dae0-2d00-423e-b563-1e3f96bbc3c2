/*
 * @Description: 巡检记录页面
 * @Author: AI Assistant
 * @Date: 2025-06-27
 */
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Input, Dropdown, Toast, ImageViewer, TextArea, Dialog } from 'antd-mobile';
import { Select } from 'antd';
import { LeftOutline, PlayOutline, CameraOutline, RightOutline } from 'antd-mobile-icons';
import NewInspectionModal from '@/components/NewInspectionModal';
import PhotoCapture from '@/components/PhotoCapture';
import { apis } from '@/api/api';
import { beforeUpload,urlName } from '@/util/method';
import './InspectionRecord.less';

// 检查项目配置接口
interface CheckConfig {
  id: number;
  itemCategoryId: number;
  placeCategoryId: number;
  taskId: string;
  reqName: string;
  prompt: string;
  outputType: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'TEXT';
  outputContent: string;
  isMultiple: number;
  isMultiOutput: number;
  checkWay: number;
  isGenHazard: number;
  isRequired: number;
  annexShow: number;
  annexRequired: number;
  annexType: number;
  guidanceFile: number;
  extraAttr: string;
  sortIndex: number;
}

// 检查结果接口
interface CheckResult {
  id: number;
  taskId: string;
  placeCategoryId: number;
  itemCategoryId: number;
  requirementId: number;
  executeId: number;
  executePlaceCategoryId: number;
  executeItemCategoryId: number;
  material: string;
  value: string | null;
  description: string;
  resultType: string;
  businessId: string;
  traceId: string | null;
  contentId: string | null;
  placeName: string;
  itemName: string;
  isHazard: number;
  checkWay: number;
  hazardDesc: string | null;
  config: CheckConfig;
  imageUrls?: string[]; // 添加图片URL数组字段
}

// 版本接口
interface Version {
  id: number;
  version: number;
  cateName: string;
  checkName: string;
  isMultiple: number;
  status: number;
  results: CheckResult[];
}

// 任务数据接口
interface TaskData {
  cateName: string;
  guidanceFileIds: string | null;
  checkWay: number[];
  status: number;
  configItemId: number;
  isMultiple: number;
  checkCount: number;
  requirementCount: number;
  hazardCount: number;
  versions: Version[];
}

// 巡检记录接口 - 基于新的数据结构
interface InspectionRecordData {
  id: string;
  timestamp: string;
  status: 'delete' | 'normal';
  isCollapsed: boolean;
  description?: string;
  results: CheckResult[];
}

const InspectionRecord: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [recordId, setRecordId] = useState<string>(''); // 检查结果id
  const [itemId, setItemId] = useState<string>(''); // 检查项id

  // 当前标签页
  const [activeTab, setActiveTab] = useState<'record' | 'guide'>('record');

  // 新增弹框状态
  const [showNewModal, setShowNewModal] = useState(false);
 
  const [firstData, setFirstData] = useState<any[]>([]);
   
  const [taskData, setTaskData] = useState<TaskData>();

  // 巡检记录数据 - 基于新的数据结构
  const [inspectionRecords, setInspectionRecords] = useState<InspectionRecordData[]>([]);

  useEffect(() => {
    updateData()
  }, [id]);

  // 媒体文件选项配置（支持视频和图片）
  const [dropdownOptions, setDropdownOptions] = useState<{ label: string; value: string; fileUrl: string; fileType: 'video' | 'image' }[]>([
  ]);

  // 拍照相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);

  // 当前选中的媒体文件
  const [selectedMedia, setSelectedMedia] = useState<{ url: string; type: 'video' | 'image' } | null>(null);
  // 当前选中的文件ID
  const [selectedFileId, setSelectedFileId] = useState<string | undefined>(undefined);

  // 图片预览相关状态
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isMultiple, setIsMultiple] = useState(false);

  // 编辑状态管理 - 记录每个记录项是否处于编辑模式
  const [editingRecords, setEditingRecords] = useState<Set<string>>(new Set());

  // 记录名称编辑状态管理
  const [editingRecordNames, setEditingRecordNames] = useState<Set<string>>(new Set());

  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 更新数据
  const updateData = (callback: () => void = () => {}) => {
    // placeId=48&configItemId=186
    apis.ginkgoSystem.getCheckItemDetail({
      placeId: sessionStorage.getItem('stageId'),
      configItemId: id
    }).then(res => {
      console.log('res', res);
      if(res.code == 0) {
        setIsMultiple(res.data.isMultiple===1)
        setFirstData(res.data)
        const taskDataList = res.data
        if(taskDataList.guidanceFileIds) {
          apis.ginkgoUpload.getFileInfos({ ids: taskDataList.guidanceFileIds.join(',') }).then(res => {
            taskDataList.videoList = res.data;
            // 根据文件名扩展名判断文件类型
            const mediaOptions = taskDataList.videoList.map((item: any) => {
              const fileName = item.fileName.toLowerCase();
              const isVideo = fileName.endsWith('.mp4') || fileName.endsWith('.avi') || fileName.endsWith('.mov') || fileName.endsWith('.wmv');
              const isImage = fileName.endsWith('.png') || fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.gif') || fileName.endsWith('.bmp');

              return {
                label: item.fileName,
                value: String(item.id), // 确保 value 是字符串类型
                fileUrl: item.fileUrl,
                fileType: isVideo ? 'video' : (isImage ? 'image' : 'video') // 默认当作视频处理
              };
            });
            setDropdownOptions(mediaOptions);

            // 默认选择第一个文件
            if (mediaOptions.length > 0) {
              const firstMedia = mediaOptions[0];
              setSelectedMedia({
                url: firstMedia.fileUrl,
                type: firstMedia.fileType
              });
              setSelectedFileId(firstMedia.value);
            }
          })
        }else{
          setDropdownOptions([])
          setSelectedMedia(null)
          setSelectedFileId(undefined)
        }
        const arrays = []
        taskDataList.versions.forEach(version => {
          arrays.push({
            id: version.id,
            timestamp: version.createTime[0]+'年'+version.createTime[1]+'月'+version.createTime[2]+'日 '+version.createTime[3]+':'+version.createTime[4]+':'+version.createTime[5], //年月日时分秒
            status: version.status==0?'normal':'delete',
            isCollapsed: false, // 是否折叠
            description: version.checkName, // 描述
            results: version.results,
          })
        })
        setInspectionRecords(arrays);
        setTaskData(taskDataList);
        setTimeout(() => {
          callback()
        }, 300)
        console.log('InspectionRecord 设置 taskData:', taskDataList);
      }
    })

  }

  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    console.log('拍照结果:', file, dataUrl);
    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const imgId = res[0].id
      setInspectionRecords(prev =>
        prev.map(record =>
          record.id === recordId
            ? { ...record, results: record.results.map(result => result.id.toString() === itemId ? { ...result, material: result.material ? `${result.material},${imgId}` : imgId } : result) }
            : record
        )
      );
    })
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? {
              ...record,
              results: record.results.map(result =>
                result.id.toString() === itemId
                  ? { ...result, imageUrls: [...(result.imageUrls || []), dataUrl] }
                  : result
              )
            }
          : record
      )
    );


    // fetchImageUrls(file, dataUrl);
  };

  // 处理图片点击预览
  const handleImageClick = (images: string[], index: number) => {
    setCurrentImages(images);
    setCurrentImageIndex(index);
    setImageViewerVisible(true);
  };

  // 切换编辑模式
  const handleToggleEdit = (recordId: string) => {
    setEditingRecords(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recordId)) {
        newSet.delete(recordId);
      } else {
        newSet.add(recordId);
      }
      return newSet;
    });
  };

  // 删除记录
  const handleDeleteRecord = (recordId: string) => {
    console.log('删除记录:', recordId,taskData);
    // taskDataList.versions
    // 二次确认框
    Dialog.confirm({
      title: '提示',
      content: '确定删除该记录吗？',
      //修改确认 取消css样式
      bodyClassName: 'confirm-cancel-css',

      onConfirm: () => {
        if(taskData?.versions.length == 1) {
          Toast.show('至少保留一条记录');
          return;
        }
          // 这里可以添加删除逻辑
        apis.ginkgoSystem.deleteCheckItem({
          id: recordId,
          executeId: Number(JSON.parse(sessionStorage.getItem('prameData') || '{}').newId)
        }).then(res => {
          console.log('res', res);
          if(res.code == 0) {
            updateData()
            Toast.show('删除成功');
          }else{
            Toast.show(res.message);
          }
        })
      }})
        
  };

  // 完成编辑并上传数据
  const handleCompleteEdit = async (recordId: string) => {
    try {
      // 获取当前记录的数据
      const currentRecord = inspectionRecords.find(record => record.id === recordId);
      if (!currentRecord) return;

      // 模拟上传数据到服务器
      console.log('上传记录数据:', currentRecord);

      const params = currentRecord.results.map((item: any) => ({
        id: item.id,
        value: item.value,
        isHazard: item.isHazard,
        material: item.material,
        hazardDesc: item.hazardDesc,
      }))
      apis.ginkgoSystem.requirementSubmit(params).then(res => {
        console.log('res', res);
        if (res.code === 0) {
          Toast.show('提交成功');
          updateData()
        } else {
          Toast.show(res.message);
        }
      })
      setTimeout(() => {
        handleSaveRecordName(recordId);
      }, 0);

      // apis.ginkgoSystem.updateCheckItem(

      // 这里可以调用实际的API
      // await uploadInspectionRecord(currentRecord);

      Toast.show({
        content: '数据上传成功',
        duration: 2000,
      });

      // 退出编辑模式
      setEditingRecords(prev => {
        const newSet = new Set(prev);
        newSet.delete(recordId);
        return newSet;
      });
    } catch (error) {
      console.error('上传失败:', error);
      Toast.show({
        content: '上传失败，请重试',
        duration: 2000,
      });
    }
  };

  // 修改检查项状态 - 新的数据结构
  const handleStatusChange = (recordId: string, itemId: string, newStatus: 'normal' | 'abnormal') => {
    const isHazard = newStatus === 'abnormal' ? 1 : 0;
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? {
              ...record,
              results: record.results.map(result =>
                result.id.toString() === itemId
                  ? {
                      ...result,
                      isHazard,
                      // 当状态切换到正常时，清空隐患描述
                      hazardDesc: newStatus === 'normal' ? null : result.hazardDesc
                    }
                  : result
              )
            }
          : record
      )
    );
  };

  // 修改记录描述
  const handleDescriptionChange = (recordId: string, description: string) => {
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId ? { ...record, description } : record
      )
    );
  };

  // 保存记录名称
  const handleSaveRecordName = async (recordId: string) => {
    try {
      const currentRecord = inspectionRecords.find(record => record.id === recordId);
      if (!currentRecord) return;

      console.log('保存记录名称:', recordId, currentRecord.description);


      apis.ginkgoSystem.updateCheckName({
        checkName: currentRecord.description || '',
        id: recordId
      }).then(res => {
        console.log('更新检查处所名称 res:', res);
        Toast.show('更新成功');
      });
      // 如果有API，可以取消注释下面的代码：
      /*
      const response = await apis.ginkgoSystem.updateCheckRecord({
        id: recordId,
        checkName: currentRecord.description || '',
        executeId: Number(JSON.parse(sessionStorage.getItem('prameData') || '{}').newId)
      });

      if (response.code === 0) {
        Toast.show('记录名称保存成功');
      } else {
        Toast.show(response.message || '保存失败');
      }
      */
    } catch (error) {
      console.error('保存记录名称失败:', error);
      Toast.show('保存失败，请重试');
    }
  };

  // 获取图片URL的函数
  const fetchImageUrls = async (result: CheckResult) => {
    if (!result.material) return;

    try {
      const response = await apis.ginkgoUpload.getFileInfos({ ids: result.material });
      console.log('获取图片信息:', response);

      if (response.data && Array.isArray(response.data)) {
        const imageUrls = response.data.map((item: any) => item.fileUrl || item.url);

        // 更新result的imageUrls字段 - 新的数据结构
        setInspectionRecords(prev =>
          prev.map(record => ({
            ...record,
            results: record.results.map(r =>
              r.id === result.id
                ? { ...r, imageUrls }
                : r
            )
          }))
        );
      }
    } catch (error) {
      console.error('获取图片URL失败:', error);
    }
  };

  // 初始化时获取所有图片URL - 新的数据结构
  useEffect(() => {
    inspectionRecords.forEach(record => {
      record.results.forEach(result => {
        if (result.material && !result.imageUrls) {
          fetchImageUrls(result);
        }
      });
    });
  }, [inspectionRecords]);

  // 删除图片 - 从指定记录的指定项目中删除指定索引的图片 - 新的数据结构
  const handleDeleteImage = (recordId: string, itemId: string, imageIndex: number) => {
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? {
              ...record,
              results: record.results.map(result =>
                result.id.toString() === itemId
                  ? {
                      ...result,
                      imageUrls: result.imageUrls?.filter((_, index) => index !== imageIndex) || [],
                      material: result.material
                        .split(',')
                        .filter((_, index) => index !== imageIndex)
                        .join(',')
                    }
                  : result
              )
            }
          : record
      )
    );
  };

  const handleBack = () => {
    const taskCode = JSON.parse(sessionStorage.getItem('prameData') || '{}').taskCode;
    const newId = JSON.parse(sessionStorage.getItem('prameData') || '{}').newId;
    debugger;
    navigate(`${urlName}/task-detail/${newId}?taskCode=${taskCode}&stageId=${sessionStorage.getItem('stageId')}`);
  };

  // 处理输入值变化 - 新的数据结构
  const handleValueChange = (recordId: string, itemId: string, value: string) => {
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? {
              ...record,
              results: record.results.map(result =>
                result.id.toString() === itemId
                  ? { ...result, value }
                  : result
              )
            }
          : record
      )
    );
  };

  // 处理隐患描述变化
  const handleHazardDescChange = (recordId: string, itemId: string, hazardDesc: string) => {
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? {
              ...record,
              results: record.results.map(result =>
                result.id.toString() === itemId
                  ? { ...result, hazardDesc }
                  : result
              )
            }
          : record
      )
    );
  };

  // 处理折叠切换
  const handleToggleCollapse = (recordId: string) => {
    setInspectionRecords(prev =>
      prev.map(record =>
        record.id === recordId
          ? { ...record, isCollapsed: !record.isCollapsed }
          : record
      )
    );
  };

  // 处理拍照
  const handleTakePhoto = (recordId: string, itemId: string) => {
    console.log('拍照:', recordId, itemId);
    // 这里可以添加拍照逻辑
    setRecordId(recordId);
    setItemId(itemId);
    setShowPhotoCapture(true);
  };

  // 处理新增巡检
  const handleNewInspection = () => {
    console.log('打开NewInspectionModal, 当前taskData:', taskData);
    // setShowNewModal(true);
    apis.ginkgoSystem.createNewCheckItem({
      itemConfigId: id, // 检查项id
      executeId: Number(JSON.parse(sessionStorage.getItem('prameData')).newId), // 执行任务id
      name: taskData?.cateName+'-'+ new Date().getTime(),
    }).then(res => {
      console.log('res', res);
      if(res.code == 0) {
        updateData(
          () => {
            setShowNewModal(true);
          }
        )
      }
    })
  };

  // 处理新增提交
  const handleNewSubmit = (data: any) => {
    console.log('新增巡检数据:', data);
    // 调用接口提交数据
    if(data.type == 'form') {
      apis.ginkgoSystem.requirementSubmit(data.formData).then(res => {
        console.log('res', res);
        if(res.code == 0) {
          Toast.show('提交成功');
          updateData()
          setShowNewModal(false);
        } else {
          Toast.show(res.message);
        }
      })
    }
    
  };


  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal':
        return '#52c41a';
      case 'abnormal':
        return '#ff4d4f';
      default:
        return '#d9d9d9';
    }
  };

  // 获取状态文字
  const getStatusText = (status: string) => {
    switch (status) {
      case 'normal':
        return '正常';
      case 'abnormal':
        return '隐患';
      default:
        return '';
    }
  };

  // 渲染图片项 - 根据编辑状态显示不同的交互元素
  const renderImageItem = (image: string, imgIndex: number, recordId: string, itemId: string, images: string[]) => {
    const isEditing = editingRecords.has(recordId);

    return (
      <div
        key={imgIndex}
        className="image-item"
        onClick={() => !isEditing && handleImageClick(images, imgIndex)}
      >
        <img src={image} alt={`检查图片${imgIndex + 1}`} />

        {/* 编辑模式：显示删除按钮 */}
        {isEditing && (
          <div
            className="delete-image-btn"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteImage(recordId, itemId, imgIndex);
            }}
          >
            ×
          </div>
        )}

        {/* 非编辑模式：显示预览提示 */}
        {!isEditing && (
          <div
            className="preview-overlay"
            onClick={() => handleImageClick(images, imgIndex)}
          >
            <span className="preview-text">点击预览</span>
          </div>
        )}
      </div>
    );
  };

  // 渲染状态选择器 - 编辑模式显示按钮，非编辑模式显示文字
  const renderStatusSelector = (result: CheckResult, recordId: string) => {
    const isEditing = editingRecords.has(recordId);
    const status = result.isHazard === 1 ? 'abnormal' : 'normal';

    if (isEditing) {
      // 编辑模式：显示可点击的状态切换按钮
      return (
        <div className="status-selector">
          <Button
            className={`status-btn ${status === 'normal' ? 'active' : ''}`}
            size="small"
            onClick={() => handleStatusChange(recordId, result.id.toString(), 'normal')}
          >
            正常
          </Button>
          <Button
            className={`status-btn ${status === 'abnormal' ? 'active' : ''}`}
            size="small"
            onClick={() => handleStatusChange(recordId, result.id.toString(), 'abnormal')}
          >
            隐患
          </Button>
        </div>
      );
    }

    // 非编辑模式：显示当前状态文字
    return (
      <span
        className="status-text"
        style={{ color: getStatusColor(status) }}
      >
        {getStatusText(status)}
      </span>
    );
  };

  return (
    <div className="inspection-record-container">
      {/* 头部 */}
      <header className="inspection-record-header">
        <LeftOutline
          className="back-icon"
          onClick={handleBack}
        />
        <h1 className="page-title"> {taskData?.cateName} </h1>
      </header>

      {/* 标签页 */}
      <div className="tab-bar">
        <div
          className={`tab-item ${activeTab === 'record' ? 'active' : ''}`}
          onClick={() => setActiveTab('record')}
        >
          巡检记录
        </div>
        <div
          className={`tab-item ${activeTab === 'guide' ? 'active' : ''}`}
          onClick={() => setActiveTab('guide')}
        >
          巡检指导
        </div>
      </div>

      {/* 巡检记录内容 */}
      {activeTab === 'record' && (
        <div className="record-content">
          {/* 遍历所有巡检记录 */}
          {inspectionRecords.map((record) => (
            <div key={record.id} className="record-section">
              {/* 记录时间栏 */}
              <div
                className="current-record"
                onClick={() => !editingRecordNames.has(record.id) && handleToggleCollapse(record.id)}
              >
                <div className="record-info">
                  {editingRecords.has(record.id) ? (
                    <Input
                      className="record-name-input"
                      value={record.description || ''}
                      onChange={(value) => handleDescriptionChange(record.id, value)}
                      placeholder="请输入记录名称"
                      onClick={(e) => e.stopPropagation()}
                    />
                  ) : (
                    <div className="record-name-container">
                      <span className="record-name">
                        {record.description || '未命名记录'}
                      </span>
                    </div>
                  )}
                  <span className="record-time">{record.timestamp}</span>
                </div>

                <div className="record-actions" onClick={(e) => e.stopPropagation()}>
                  {editingRecords.has(record.id) ? (
                    <Button
                      className="complete-btn"
                      size="small"
                      color="primary"
                      onClick={() => handleCompleteEdit(record.id)}
                    >
                      完成
                    </Button>
                  ) : (
                    <Button
                      className="edit-btn"
                      size="small"
                      onClick={() => handleToggleEdit(record.id)}
                    >
                      编辑
                    </Button>
                  )}

                  <span
                    className="delete-btn"
                    onClick={() => handleDeleteRecord(record.id)}
                  >
                    删除
                  </span>
                </div>

                <RightOutline className={`collapse-icon ${record.isCollapsed ? '' : 'collapsed'}`} />
              </div>

              {/* 检查项目列表 */}
              {!record.isCollapsed && (
                <div className="inspection-items">
                  {record.results.map((result, index) => {
                    // 使用真实的图片URL，如果还没有获取到则显示加载状态
                    const images = result.imageUrls || [];
                    console.log('images', images, result.material, result.imageUrls);
                    return (
                      <div key={result.id} className="inspection-item">
                        <div className="item-header">
                          <span className="item-number">{index + 1}.</span>
                          <span className="item-title">{result.config.reqName}</span>

                          {/* 状态显示和切换 */}
                          {renderStatusSelector(result, record.id)}

                          <RightOutline className="arrow-icon" />
                        </div>

                        <div className="item-content">
                          {/* 隐患描述显示和编辑 */}
                          {result.isHazard === 1 && (
                            <div className="hazard-description">
                              <div className="hazard-label">隐患描述：</div>
                              {editingRecords.has(record.id) ? (
                                <TextArea
                                  placeholder="请输入隐患描述..."
                                  value={result.hazardDesc || ''}
                                  onChange={(value) => handleHazardDescChange(record.id, result.id.toString(), value)}
                                  rows={2}
                                  className="hazard-input"
                                />
                              ) : (
                                <div className="hazard-content">
                                  {result.hazardDesc || '暂无隐患描述'}
                                </div>
                              )}
                            </div>
                          )}

                          {/* 根据outputType渲染不同的输入组件 */}
                          {result.config.outputType === 'SINGLE_CHOICE' && (
                            <div className="button-group-section">
                              <div className="option-buttons">
                                {result.config.outputContent.split('；').map((option, optionIndex) => {
                                  const cleanOption = option.replace(/"/g, '');
                                  return (
                                    <Button
                                      key={optionIndex}
                                      className={`option-btn ${result.value === cleanOption ? 'active' : ''} ${!editingRecords.has(record.id) ? 'disabled' : ''}`}
                                      onClick={() => editingRecords.has(record.id) && handleValueChange(record.id, result.id.toString(), cleanOption)}
                                      size="small"
                                      disabled={!editingRecords.has(record.id)}
                                    >
                                      {cleanOption}
                                    </Button>
                                  );
                                })}
                              </div>
                            </div>
                          )}

                          {/* 文本输入 */}
                          {result.config.outputType === 'TEXT' && (
                            <div className="text-input-section">
                              <TextArea
                                placeholder="请输入内容..."
                                value={result.value || ''}
                                onChange={(value) => editingRecords.has(record.id) && handleValueChange(record.id, result.id.toString(), value)}
                                disabled={!editingRecords.has(record.id)}
                                rows={2}
                              />
                            </div>
                          )}

                          {/* 图片和拍照区域 */}
                          <div className="media-section">
                            {/* 已上传的图片 */}
                            {images.length > 0 && (
                              <div className="uploaded-images">
                                {images.map((imageUrl, imgIndex) =>
                                  renderImageItem(imageUrl, imgIndex, record.id, result.id.toString(), images)
                                )}
                              </div>
                            )}

                            {/* 图片加载状态 */}
                            {result.material && !result.imageUrls && (
                              <div className="loading-images">
                                <span>正在加载图片...</span>
                              </div>
                            )}

                            {/* 拍照按钮 */}
                            {editingRecords.has(record.id) && (
                              <div
                                className="photo-btn"
                                onClick={() => handleTakePhoto(record.id, result.id.toString())}
                              >
                                {/* <CameraOutline /> */}
                                <div className="upload-btn-icon">
                                </div>
                                <span>点击上传</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}

                  {/* <div className="description-section">
                    <TextArea
                      placeholder="请输入备注说明..."
                      value={record.description || ''}
                      onChange={(value) => handleDescriptionChange(record.id, value)}
                      rows={3}
                      className="description-input"
                      disabled={!editingRecords.has(record.id)}
                    />
                  </div> */}

                  
                </div>
              )}
            </div>
          ))}
          <div className="record-content-bottom">

          </div>

          {/* 底部提示和新增按钮 */}
          
            <div className="bottom-section">
              <div className="tip-text">
                {isMultiple ? '如有多处需要检查，请点击新增' : '后台配置为单次巡检，无法新增'}
              </div>
            <Button
              className="add-btn"
              block
              onClick={handleNewInspection}
              disabled={!isMultiple} >
              新增
            </Button>
          </div>
        </div>
      )}

      {/* 巡检指导内容 */}
      {activeTab === 'guide' && (
        <div className="guide-content">
          <div className="guide-section">
            <div className="video-selector">
              {dropdownOptions.length > 0 ? (
                <Select
                  placeholder="选择巡检指导文件"
                  style={{ width: '100%' }}
                  options={dropdownOptions}
                  value={selectedFileId}
                  onChange={(value) => {
                    console.log('Select onChange triggered, value:', value);
                    const selectedOption = dropdownOptions.find(option => option.value === value);
                    console.log('Found option:', selectedOption);
                    if (selectedOption) {
                      setSelectedMedia({
                        url: selectedOption.fileUrl,
                        type: selectedOption.fileType
                      });
                      setSelectedFileId(value);
                    }
                  }}
                />
              ) : (
                <div className="no-video-tip">
                  <span className="no-video-text">暂无指导文件</span>
                </div>
              )}
            </div>

            {selectedMedia && (
              <div className="media-container">
                {selectedMedia.type === 'video' ? (
                  <div className="video-container">
                    <video
                      key={selectedMedia.url}
                      controls
                      poster="/images/video-poster.jpg"
                      className="guide-video"
                      preload="metadata"
                    >
                      <source src={selectedMedia.url} type="video/mp4" />
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                ) : (
                  <div className="image-container">
                    <img
                      src={selectedMedia.url}
                      alt="巡检指导图片"
                      className="guide-image"
                      onClick={() => {
                        setCurrentImages([selectedMedia.url]);
                        setCurrentImageIndex(0);
                        setImageViewerVisible(true);
                      }}
                    />
                  </div>
                )}
              </div>
            )}

            {/* <div className="video-description">
              <div className="description-title">
                {dropdownOptions.find(option => option.videoUrl === selectedVideo)?.label || '巡检指导视频'}
              </div>
              <div className="description-content">
                请观看视频了解正确的巡检操作方法，确保按照标准流程进行巡检作业。
              </div>
            </div> */}
          </div>
        </div>
      )}

      {/* 新增巡检弹框 */}
      <NewInspectionModal
        visible={showNewModal}
        onClose={() => {
          console.log('关闭NewInspectionModal, 当前taskData:', taskData);
          setShowNewModal(false);
        }}
        onSubmit={handleNewSubmit}
        taskData={taskData}
        onDataUpdate={updateData}
      />

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 图片预览组件 */}
      <ImageViewer
        image={currentImages[currentImageIndex]}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
        getContainer={null}
      />
    </div>
  );
};

export default InspectionRecord;
