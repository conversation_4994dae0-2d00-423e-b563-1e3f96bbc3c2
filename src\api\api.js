/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2025-01-06 10:23:19
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-20 19:55:24
 * @FilePath: \note-exam\src\api\api.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import getServices from "./axios-request";
import {getUrlParameter} from "@/util/index";

let apiUrl = import.meta.env.VITE_API_URL;

const projectPath = import.meta.env.VITE_API_URL+ '/system-api';
const getToken = () => {
  return getUrlParameter('token') ||sessionStorage.getItem("token");
}

const chatPath = '/ginkgo/chat-api';
// 上传文件
// const uploadFile = params => post(`${projectPath}/upload/file`, params);
const apis = {
  pageSize: 8, // 列表每页条数
  sdVideo:{
    videoThumbnail:apiUrl+"/mtweb/data/video/getVideoThumbnail",
  },
  // 上传文件
  uploadFile: getServices("/ginkgo/system-api/upload/file", () => getToken(), {
    uploadFile: { url: "uploadFile", type: "post",getResp:true }, // 上传文件
    uploadFiles: { url: "uploadFiles", type: "post",getResp:true }, // 批量上传文件
  }),
  ginkgoUser:getServices("/ginkgo/system-api/system/user/", () => getToken(), {
    // getUserDetail: { url: "getUserDetail", type: "get" }, // 获取用户信息
    // systerm/auth/refresh-token?refreshToken
    refreshToken: { url: "systerm/auth/refresh-token", type: "get"}, // 刷新token
  }),
  // 智能分析
  // /ginkgo/system/scene/getItemSceneByAiCheck
  ginkgoSystem: getServices("/ginkgo/system-api/system/", () => getToken(), {
    getItemSceneByAiCheck: { url: "scene/getItemSceneByAiCheck", type: "get",getResp:true }, // 获取场景
    executeTaskList: { url: "check/execute/task/list", type: "get",getResp:true }, // 执行任务列表
    createTask: { url: "check/execute/task/create", type: "postjson",getResp:true }, // 创建任务
    getDetail: { url: "check/execute/task/getDetail", type: "get",getResp:true }, // 获取任务详情
    completeTask: { url: "check/execute/completeTask", type: "postjson",getResp:true }, // 完成任务
    requirementSubmit: { url: "check/execute/requirementSubmit", type: "postjson",getResp:true }, // 提交任务
    createNewCheckItem: { url: "check/execute/createNewCheckItem", type: "postjson",getResp:true }, // 创建新检查项
    // /system-api/system/check/execute/task/getCheckItemDetail?placeId=48&configItemId=186
    getCheckItemDetail: { url: "check/execute/task/getCheckItemDetail", type: "get",getResp:true }, // 获取检查项详情
    // /check/execute/attachmentSubmit
    attachmentSubmit: { url: "check/execute/attachmentSubmit", type: "postjson",getResp:true }, // 提交附件
    // /ginkgo/system/check/execute/updateCheckName
    updateCheckName: { url: "check/execute/updateCheckName", type: "postjson",getResp:true }, // 更新检查项名称
    // /ginkgo/system/check/execute/deleteCheckItem
    deleteCheckItem: { url: "check/execute/deleteCheckItem", type: "delete",getResp:true }, // 删除检查项
    // /ginkgo/system/check/execute/task/updateTaskName
    updateTaskName: { url: "check/execute/task/updateTaskName", type: "postjson",getResp:true }, // 更新任务名称
  }),
  ginkgoUpload: getServices("/ginkgo/system-api/", () => getToken(), {
    createPreSignUrl: { url: "upload/createPreSignUrl", type: "postjson",getResp:true }, // 巡检文件上传创建预签名
    createPreSignFileRecord: { url: "upload/createPreSignFileRecord", type: "postjson",getResp:true }, // 巡检文件上传创建预签名
    getFileInfos: { url: "upload/getFileInfos", type: "get",getResp:true }, // 获取文件组信息
  }),
  chatApi: getServices("/ginkgo/chat-api/", () => getToken(), {
    sendQuestion: { url: "v2/chat/sendQuestion", type: "get",getResp:true }, // 发送问题
    getDialogue: { url: "v2/chat/getDialogue", type: "get",getResp:true }, // 获取对话
    updateStatus: { url: "v2/chat/aiCheck/updateStatus", type: "postjson",getResp:true }, // 更新状态
  }),
  // const getSign = params => get(`${projectPath}/wx/mp/getSign`, params);
  wxMp: getServices("/ginkgo/system-api/wx/mp/", () => getToken(), {
    getSign: { url: "getSign", type: "get",getResp:true }, // 获取签名
  }),
};

export { apis,projectPath,chatPath };
