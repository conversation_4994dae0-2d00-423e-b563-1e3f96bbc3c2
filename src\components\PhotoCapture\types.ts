// PhotoCapture 组件的类型定义

export interface PhotoCaptureProps {
  /** 图片捕获成功回调 */
  onImageCaptured?: (file: File, dataUrl: string) => void;
  /** 上传成功回调 */
  onUploadSuccess?: (response: any) => void;
  /** 上传失败回调 */
  onUploadError?: (error: any) => void;
  /** 图片质量 (0-1) */
  quality?: number;
  /** 上传地址 */
  uploadUrl?: string;
  /** 最大宽度 */
  maxWidth?: number;
  /** 最大高度 */
  maxHeight?: number;
  /** 是否显示 */
  visible?: boolean;
  /** 关闭回调 */
  onClose?: () => void;
  /** 标题 */
  title?: string;
}

export interface PhotoCaptureState {
  /** 原始图片 */
  originalImage: string | null;
  /** 裁剪后图片 */
  croppedImage: string | null;
  /** 当前步骤 */
  currentStep: 'select' | 'preview';
  /** 是否正在上传 */
  uploading: boolean;
} 