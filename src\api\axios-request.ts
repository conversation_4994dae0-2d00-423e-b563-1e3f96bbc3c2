import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import qs from 'qs';
import { mapValues, isPlainObject, isEmpty } from 'lodash';
import Pubsub from 'pubsub-js';
import { message } from 'antd';
import { urlName,getToken } from '@/util/method';

import { apis } from './api';
const sysUrl: string = ''; // 服务 IP 地址
const mockURL: string = ''; // mock IP 地址
let hasExecuted = false; // 标志变量，防止多次执行
const executionDelay = 3000; // 延迟时间，单位为毫秒
function handleLoginError() {
  if (!hasExecuted) { // 检查是否已经执行
    hasExecuted = true; // 设置标志为已执行
    // 显示错误消息
    // message.error('登录失效，自动登陆');
    axios.post(`/ginkgo/system-api/system/auth/refresh-token?refreshToken=${sessionStorage.getItem('refreshToken')}`).then(res => {
      if(res.data.code === 0){
        sessionStorage.setItem('token', res.data.data.accessToken);
        sessionStorage.setItem('refreshToken', res.data.data.refreshToken);
      }
    })

    // 设置一个定时器，3秒后重置标志
    setTimeout(() => {
      hasExecuted = false; // 3秒后可以再次执行
    }, executionDelay);
  }
}
interface RequestConfig extends AxiosRequestConfig {
  showMessage?: boolean;
  getResp?: boolean;
}

const instance = () => {
  // 创建 axios 实例
  const axiosInstance = axios.create({
    timeout: 10000,
    headers: {
      'Authorization': `Bearer ${getToken()}`,
    },
  });

  // 请求拦截器
  axiosInstance.interceptors.request.use(
    (config) => {
      const { method, url } = config;
      // 可以在这里添加全局的日志或其他操作
      console.log(`[Request] ${method?.toUpperCase()} ${url}`);
      return config;
    },
    (error: any) => {
      console.error('请求错误：', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  axiosInstance.interceptors.response.use(
    (response) => {
      // 对返回数据进行统一处理
      console.log('[Response Data]:', response.data);

      if (response.data.code=== 2999) {
          Pubsub.publish('jumpPayBefore', `${urlName}/knowledge-before/pay-before/`+response.data.data.uniqueId+'?subjectCode='+response.data.data.subjectCode);
      }else if(response.data.code === 1002||response.data.code === 1001){
        handleLoginError();
        
      }else if(response.data.code!== 0){
        // message.error(response.data.message);
     }

      // 例如：可以在这里自动解构 `response.data`，只返回 data 部分
      return response;
    },
    (error) => {
      // 全局处理错误，例如网络错误、服务器返回 4xx/5xx 错误
      if (error.response) {
        console.error('[Response Error]:', error.response.status, error.response.data);
      } else {
        console.error('[Network Error]:', error.message);
      }

      // 可以在这里进行全局的错误处理，也可以抛出异常供业务逻辑捕获
      return Promise.reject(error);
    }
  );

  return axiosInstance;
};

/**
 * @description 根据参数，判断是get还是post请求
 * @param {{url: string, method: string, data: {[key: string]: string}}} options
 */
const fetch = (options: RequestConfig): Promise<AxiosResponse> => {
  const { method = 'get', data = {}, url } = options;
  switch (method.toLowerCase()) {
    case 'post':
      return instance().post(url as string, qs.stringify(data));
    case 'postjson':
      return instance().post(url as string, data, { headers: { 'Content-type': 'application/json' } });
    case 'postfile':
      return instance().post(url as string, data);
    case 'postfrom':
      return instance().post(url as string, data, { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } });
    case 'delete':
      return instance().delete(`${url}${!isEmpty(data) ? `&${qs.stringify(data)}` : ''}`);
    default:
      return instance().get(`${url}${!isEmpty(data) ? `&${qs.stringify(data)}` : ''}`);
  }
};

/**
 * @description 对返回的数据做判断，返回异常，则报错，返回正常，则将data值返回
 * @param {{url: string, method: string, data: {[key: string]: string}}} options
 */
async function request(options: RequestConfig): Promise<any> {
  const tempOptions: RequestConfig = { ...options };
  try {
    const response: AxiosResponse = await fetch(tempOptions);
    let { data } = response;
    if (typeof data === 'string') {
      try {
        data = JSON.parse(data);
      } catch (e) {
        console.log(e);
      }
    }
    const { code } = data;

    if (tempOptions.getResp) {
      return data;
    }

    if (!code) { // 返回 code 为 0，表示返回数据正常，直接将 data 返回
      return tempOptions.getResp ? data : data.data;
    } else {
      tempOptions.showMessage && console.error(data.message || data.msg); // 提示后台错误信息
      throw response;
    }
  } catch (response) {
    throw response;
  }
}

interface ServiceConfig {
  url: string;
  type?: 'get' | 'post' | 'postjson' | 'postfile' | 'postfrom' | 'delete';
  prefix?: 'base' | 'mock';
  load?: boolean;
  showMessage?: boolean;
  getResp?: boolean;
}

/**
 * @description 创建异步请求函数
 * @param {string} url 请求地址
 * @param {string} method 请求方式（get 或 post）
 * @param {function} getToken
 */
const generateRequest = (
  url: string,
  method: 'get' | 'post' | 'postjson' | 'postfile' | 'postfrom' | 'delete',
  getToken: () => string,
  load: boolean = true,
  showMessage: boolean = true,
  getResp: boolean = false
) =>
  async (data: Record<string, any> = {}): Promise<any> => {
    if (load) {
      // store.dispatch(changeLoading(true));
    }
    return request({ url: `${url}${url.includes('?') ? '&' : '?'}`, method, data, showMessage, getResp });

    // return request({ url: `${url}${url.includes('?') ? '&' : '?'}token=${getToken()}`, method, data, showMessage, getResp });
  };

/**
 * @description getService中对于post方法不需要写成对象,直接写字符串就行了
 * @param {string} source
 * @param {function} getToken 获取 token 的方法
 * @param {{[key:string]: ServiceConfig | string}} data
 * @param {string} [basePrefix]
 * @returns
 */
function getServices(
  source: string,
  getToken: () => string,
  data: Record<string, ServiceConfig | string>,
  basePrefix?: 'mock'
): Record<string, (data?: Record<string, any>) => Promise<any>> {
  let apiPrefix = sysUrl;
  if (basePrefix === 'mock') {
    apiPrefix = mockURL;
  }
  return mapValues(data, (val: ServiceConfig | string) => {
    if (isPlainObject(val)) {
      const { url, type = 'post', prefix, load = true, showMessage = true, getResp } = val as ServiceConfig;
      let api: string;
      switch (prefix) {
        case 'base':
          api = pathResolve(sysUrl, source, url);
          break;
        case 'mock':
          api = pathResolve(mockURL, source, url);
          break;
        default:
          api = pathResolve(apiPrefix, source, url);
      }
      return generateRequest(api, type, getToken, load, showMessage, getResp);
    }
    return generateRequest(pathResolve(apiPrefix, source, val as string), 'post', getToken);
  });
}

const pathResolve = (apiPrefix: string, source: string, url: string): string => {
  return apiPrefix + source + url;
};

export default getServices;
