# ChatDialog 对话模式组件

一个功能丰富的移动端对话组件，支持文本输入、语音录制、进度显示等功能。

## 功能特点

- ✅ **渐变色头部设计** - 美观的渐变色头部，支持自定义标题
- ✅ **进度条和步骤指示** - 可配置的步骤进度显示
- ✅ **对话气泡样式** - 用户和AI助手的差异化气泡设计
- ✅ **Markdown内容渲染** - 支持富文本内容显示
- ✅ **语音录制功能** - 支持按住录音，自动语音识别
- ✅ **语音输入模式切换** - 文本/语音输入模式切换
- ✅ **打字效果加载动画** - AI回复时的打字效果
- ✅ **自动滚动到底部** - 新消息自动滚动显示
- ✅ **字符计数显示** - 输入字符数量限制和显示
- ✅ **移动端触摸适配** - 完整的移动端手势支持
- ✅ **可配置显示选项** - 灵活的组件配置选项

## 基本用法

```tsx
import React, { useState } from 'react';
import ChatDialog from '@/components/ChatDialog';

const MyComponent = () => {
  const [visible, setVisible] = useState(false);
  const [messages, setMessages] = useState([]);

  const handleSendMessage = (message: string) => {
    // 处理发送消息
    console.log('发送消息:', message);
  };

  const handleVoiceMessage = (audioBlob: Blob) => {
    // 处理语音消息
    console.log('语音消息:', audioBlob);
  };

  return (
    <ChatDialog
      visible={visible}
      onClose={() => setVisible(false)}
      title="智能助手"
      messages={messages}
      onSendMessage={handleSendMessage}
      onVoiceMessage={handleVoiceMessage}
      placeholder="请输入您的问题..."
    />
  );
};
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| visible | boolean | - | 是否显示对话框 |
| onClose | () => void | - | 关闭对话框回调 |
| title | string | "请简单描述" | 对话框标题 |
| progress | number | 0 | 进度百分比 (0-100) |
| progressSteps | string[] | ["请简单描述", "对话模式", "建议分析", "建议优化"] | 进度步骤标签 |
| currentStep | number | 1 | 当前步骤索引 |
| messages | ChatMessage[] | [] | 消息列表 |
| onSendMessage | (message: string) => void | - | 发送文本消息回调 |
| onVoiceMessage | (audioBlob: Blob) => void | - | 发送语音消息回调 |
| loading | boolean | false | 是否显示加载状态 |
| placeholder | string | "你好，请问有什么可以帮助您的吗？" | 输入框占位符 |
| maxLength | number | 500 | 最大输入字符数 |
| showVoiceButton | boolean | true | 是否显示语音按钮 |
| showProgress | boolean | true | 是否显示进度条 |

## 消息数据结构

```tsx
interface ChatMessage {
  id: string;           // 消息唯一ID
  type: 'user' | 'assistant';  // 消息类型
  content: string;      // 消息内容
  timestamp: number;    // 时间戳
}
```

## 样式定制

组件使用 Less 样式，可以通过覆盖 CSS 变量来定制样式：

```less
.chat-dialog {
  // 自定义头部渐变色
  .chat-header {
    background: linear-gradient(135deg, #your-color-1 0%, #your-color-2 100%);
  }
  
  // 自定义用户消息气泡颜色
  .user-bubble {
    background: #your-color;
  }
  
  // 自定义AI助手气泡颜色
  .assistant-bubble {
    background: #your-color;
  }
}
```

## 语音功能使用

组件支持语音录制功能，需要用户授权麦克风权限：

1. **切换语音模式**: 点击语音图标切换到语音输入模式
2. **开始录音**: 按住语音输入区域开始录音
3. **结束录音**: 松开手指结束录音并发送
4. **权限处理**: 自动请求麦克风权限，失败时显示提示

## 进度控制

组件支持步骤进度显示，适用于多步骤对话场景：

```tsx
// 更新进度
setCurrentStep(2);  // 设置当前步骤
setProgress(50);    // 设置进度百分比
```

## 注意事项

1. **语音功能**: 需要 HTTPS 环境或 localhost 才能使用麦克风
2. **移动端适配**: 组件已针对移动端优化，支持触摸手势
3. **内容渲染**: AI消息支持 Markdown 格式渲染
4. **性能优化**: 大量消息时建议实现虚拟滚动
5. **权限处理**: 语音功能需要处理用户拒绝权限的情况

## 集成示例

查看 `ChatDialogExample.tsx` 文件获取完整的集成示例，包括：
- 消息状态管理
- 模拟AI回复
- 进度控制
- 语音处理
- 错误处理
