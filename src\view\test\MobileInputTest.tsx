/*
 * @Description: 移动端输入框键盘覆盖问题测试页面
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
import React, { useEffect, useState } from 'react';
import { NavBar, Button, TextArea, Input, Toast } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import { useNavigate } from 'react-router-dom';
import { initMobileInputFix, destroyMobileInputFix, getMobileInputFix } from '@/util/mobile-input-fix';

const MobileInputTest: React.FC = () => {
  const navigate = useNavigate();
  const [keyboardStatus, setKeyboardStatus] = useState({
    isVisible: false,
    height: 0
  });
  const [testMessage, setTestMessage] = useState('');
  const [testInput, setTestInput] = useState('');

  useEffect(() => {
    // 初始化移动端输入框修复工具
    const mobileInputFix = initMobileInputFix({
      inputContainerSelector: '.test-input-container',
      enableAutoScroll: true,
      scrollOffset: 50,
      debug: true
    });

    // 定期检查键盘状态
    const statusInterval = setInterval(() => {
      const status = mobileInputFix.getStatus();
      setKeyboardStatus({
        isVisible: status.isKeyboardVisible,
        height: status.keyboardHeight
      });
    }, 200);

    return () => {
      clearInterval(statusInterval);
      destroyMobileInputFix();
    };
  }, []);

  const handleBack = () => {
    navigate(-1);
  };

  const handleTestScroll = () => {
    const mobileInputFix = getMobileInputFix();
    if (mobileInputFix) {
      mobileInputFix.adjustInput();
      Toast.show('手动调整输入框位置');
    }
  };

  const handleSendMessage = () => {
    if (testMessage.trim()) {
      Toast.show(`发送消息: ${testMessage}`);
      setTestMessage('');
    }
  };

  return (
    <div style={{ height: '100vh', backgroundColor: '#f5f5f5' }}>
      <NavBar
        style={{ backgroundColor: 'white' }}
        onBack={handleBack}
        backIcon={<LeftOutline />}
      >
        移动端输入框测试
      </NavBar>

      {/* 状态显示 */}
      <div style={{ 
        padding: '16px', 
        backgroundColor: 'white', 
        margin: '8px', 
        borderRadius: '8px',
        fontSize: '14px'
      }}>
        <div>键盘状态: {keyboardStatus.isVisible ? '可见' : '隐藏'}</div>
        <div>键盘高度: {keyboardStatus.height}px</div>
        <div>视口高度: {window.innerHeight}px</div>
        <Button 
          size="small" 
          color="primary" 
          onClick={handleTestScroll}
          style={{ marginTop: '8px' }}
        >
          手动调整位置
        </Button>
      </div>

      {/* 填充内容，模拟聊天记录 */}
      <div style={{ padding: '16px' }}>
        {Array.from({ length: 20 }, (_, index) => (
          <div
            key={index}
            style={{
              backgroundColor: 'white',
              padding: '12px',
              margin: '8px 0',
              borderRadius: '8px',
              fontSize: '14px'
            }}
          >
            这是第 {index + 1} 条测试消息，用于填充页面内容，测试滚动效果。
          </div>
        ))}
      </div>

      {/* 测试输入区域1 - 普通输入框 */}
      <div 
        className="test-input-container"
        style={{
          position: 'fixed',
          bottom: '120px',
          left: '16px',
          right: '16px',
          backgroundColor: 'white',
          padding: '12px',
          borderRadius: '8px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
          测试输入框1 (固定在底部120px)
        </div>
        <Input
          placeholder="请输入测试内容..."
          value={testInput}
          onChange={setTestInput}
        />
      </div>

      {/* 测试输入区域2 - 模拟对话输入框 */}
      <div 
        className="test-input-container"
        style={{
          position: 'fixed',
          bottom: '0',
          left: '0',
          right: '0',
          backgroundColor: 'white',
          padding: '16px',
          borderTop: '1px solid #f0f0f0',
          boxShadow: '0 -2px 8px rgba(0,0,0,0.1)'
        }}
      >
        <div style={{ marginBottom: '8px', fontSize: '12px', color: '#666' }}>
          对话输入框 (固定在底部)
        </div>
        <div style={{ display: 'flex', gap: '8px', alignItems: 'flex-end' }}>
          <TextArea
            placeholder="请输入消息..."
            value={testMessage}
            onChange={setTestMessage}
            rows={1}
            autoSize={{ minRows: 1, maxRows: 3 }}
            style={{ flex: 1 }}
          />
          <Button 
            color="primary" 
            onClick={handleSendMessage}
            disabled={!testMessage.trim()}
          >
            发送
          </Button>
        </div>
      </div>

      {/* 键盘状态指示器 */}
      {keyboardStatus.isVisible && (
        <div
          style={{
            position: 'fixed',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            backgroundColor: 'rgba(0,0,0,0.8)',
            color: 'white',
            padding: '8px 16px',
            borderRadius: '20px',
            fontSize: '12px',
            zIndex: 9999,
            pointerEvents: 'none'
          }}
        >
          键盘已弹起 ({keyboardStatus.height}px)
        </div>
      )}
    </div>
  );
};

export default MobileInputTest;
