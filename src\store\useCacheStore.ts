/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 20:10:16
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-04 20:14:06
 * @FilePath: \react-h5-template\src\store\useCacheStore.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
  // 使用 TypeScript 创建 Zustand store
  import { create } from 'zustand';
  
  const useCacheStore = create<CacheState>((set, get) => ({
    cache: {}, // 这里存放缓存数据
    // 添加缓存项
    setCache: (key: string, value: any) => set(state => ({
      cache: {
        ...state.cache,
        [key]: value
      }
    })),
  
    // 获取缓存项
    getCache: (key: string) => get().cache[key],
  
    // 清除缓存项
    removeCache: (key: string) => set(state => {
      const newCache = { ...state.cache };
      delete newCache[key];
      return { cache: newCache };
    }),
  
    // 清除所有缓存
    clearCache: () => set({ cache: {} })
  }));
  
  export default useCacheStore;
  