/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 11:56:31
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 21:34:25
 * @FilePath: src/components/speech-recognition/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述
 */
import { Input, message } from 'antd';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import speakIcon from './images/icon_speak.svg';
import styles from './index.module.scss';

const { TextArea } = Input;

const STATUS = {
	NORMAL: 'normal',   // 默认状态，当前文本框内为空
	SPEECH: 'speech',   // 语音状态，当前正在语音讲话中
	PAUSED: 'paused',   // 文本框内有已经输入的内容。
};

const getBrowserInterfaceSize = () => {
	let pageWidth = window.innerWidth;
	let pageHeight = window.innerHeight;

	if (typeof pageWidth !== 'number') {
		//在标准模式下面
		if (document.compatMode === 'CSS1Compat') {
			pageWidth = document.documentElement.clientWidth;
			pageHeight = document.documentElement.clientHeight;
		} else {
			pageWidth = document.body.clientWidth;
			pageHeight = window.body.clientHeight;
		}
	}

	return {
		pageWidth: pageWidth,
		pageHeight: pageHeight,
	};
};

const SpeechRecognition = ({
	                           speechText,
	                           currentStatus,
	                           onTextChange,
	                           onSpeech,
	                           onCancelSpeech,
	                           onClear,
	                           onSend,
	                           onClose,
	                           loading = false,
                           }) => {
	const [textValue, setTextValue] = useState('按住说话');
	const [isPressed, setIsPressed] = useState(false); // 添加按压状态
	const [fullScreen, setFullScreen] = useState(false);
	const speakBtnRef = useRef(null);
	const textAreaRef = useRef(null);
	const speechWrapperRef = useRef(null); // 添加语音录制区域的ref

	// 将录音相关状态提升到组件级别
	const timeoutRef = useRef(null);
	const startTouchClientRef = useRef([0, 0]);
	const isRecordingRef = useRef(false);

	const { pageWidth, pageHeight } = getBrowserInterfaceSize();

	useEffect(() => {
		const contextmenuHandler = (e) => {
			e.preventDefault();
			e.stopPropagation();
			return false;
		};

		// 防止长按弹出菜单的处理函数
		const preventDefaultHandler = (e) => {
			e.preventDefault();
			e.stopPropagation();
			return false;
		};

		// 防止拖拽的处理函数
		const preventDragHandler = (e) => {
			e.preventDefault();
			e.stopPropagation();
			return false;
		};

		document.addEventListener('contextmenu', contextmenuHandler);
		document.addEventListener('selectstart', preventDefaultHandler);
		document.addEventListener('dragstart', preventDragHandler);

		const speakTouchStartHandler = (e) => {
			// 立即阻止默认行为
			e.preventDefault();
			e.stopPropagation();

			const touch = e.touches[0];
			startTouchClientRef.current = [touch.clientX, touch.clientY];
			isRecordingRef.current = false; // 重置录音状态
			setIsPressed(true); // 设置按压状态
			setTextValue('继续按住说话...'); // 立即更新提示文本

			// 缩短长按时间，提升用户体验
			const touchDuration = 200; // 定义长按时间 (单位: 毫秒)，从500ms改为200ms
			timeoutRef.current = setTimeout(() => {
				isRecordingRef.current = true; // 标记开始录音
				onSpeech();
				setTextValue('松手结束录音'); // 更新提示文本
			}, touchDuration);
		};

		const speakTouchEndHandler = (e) => {
			// 阻止默认行为
			e.preventDefault();
			e.stopPropagation();

			// 重置按压状态
			setIsPressed(false);

			// 清除定时器
			clearTimeout(timeoutRef.current);

			// 如果正在录音，则停止录音
			if (isRecordingRef.current) {
				const touch = e.changedTouches[0];
				// 检查是否是上滑取消
				if (startTouchClientRef.current[1] > touch.clientY + 80) {
					setTextValue('录音已取消');
					// 延迟重置文本
					setTimeout(() => {
						setTextValue('按住说话');
					}, 1000);
				} else {
					setTextValue('录音结束，正在识别...');
					// 延迟重置文本
					setTimeout(() => {
						setTextValue('按住说话');
					}, 1000);
				}
				onCancelSpeech();
				isRecordingRef.current = false;
			} else {
				// 如果还没开始录音就抬起了，重置状态
				setTextValue('按住说话');
			}
		};

		// 防止移动端长按选择文本
		const speakTouchMoveHandler = (e) => {
			e.preventDefault();
			e.stopPropagation();
		};

		if (speakBtnRef.current) {
			const speakBtn = speakBtnRef.current;

			// 添加触摸事件
			speakBtn.addEventListener('touchstart', speakTouchStartHandler, { passive: false });
			speakBtn.addEventListener('touchend', speakTouchEndHandler, { passive: false });
			speakBtn.addEventListener('touchmove', speakTouchMoveHandler, { passive: false });
			speakBtn.addEventListener('touchcancel', speakTouchEndHandler, { passive: false });

			// 添加其他防止默认行为的事件
			speakBtn.addEventListener('contextmenu', contextmenuHandler, { passive: false });
			speakBtn.addEventListener('selectstart', preventDefaultHandler, { passive: false });
			speakBtn.addEventListener('dragstart', preventDragHandler, { passive: false });
			speakBtn.addEventListener('drag', preventDragHandler, { passive: false });

			// 为图片元素添加额外的防护
			const imgElement = speakBtn.querySelector('img');
			if (imgElement) {
				imgElement.addEventListener('contextmenu', contextmenuHandler, { passive: false });
				imgElement.addEventListener('selectstart', preventDefaultHandler, { passive: false });
				imgElement.addEventListener('dragstart', preventDragHandler, { passive: false });
				imgElement.addEventListener('drag', preventDragHandler, { passive: false });
				imgElement.addEventListener('touchstart', preventDefaultHandler, { passive: false });
				imgElement.addEventListener('touchmove', preventDefaultHandler, { passive: false });
			}
		}

		// 为语音录制区域添加触摸事件处理
		if (speechWrapperRef.current) {
			const speechWrapper = speechWrapperRef.current;

			// 语音录制区域的触摸事件处理
			const speechTouchEndHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();

				// 如果正在录音，则停止录音
				if (isRecordingRef.current) {
					const touch = e.changedTouches[0];
					const startY = startTouchClientRef.current[1];
					const endY = touch.clientY;

					// 检查是否是上滑取消（上滑超过80px）
					if (startY > endY + 80) {
						setTextValue('录音已取消');
						setTimeout(() => {
							setTextValue('按住说话');
						}, 1000);
					} else {
						setTextValue('录音结束，正在识别...');
						setTimeout(() => {
							setTextValue('按住说话');
						}, 1000);
					}
					onCancelSpeech();
					isRecordingRef.current = false;
					setIsPressed(false);
				}
			};

			const speechTouchMoveHandler = (e) => {
				e.preventDefault();
				e.stopPropagation();

				// 检查上滑距离，提供视觉反馈
				if (isRecordingRef.current) {
					const touch = e.touches[0];
					const startY = startTouchClientRef.current[1];
					const currentY = touch.clientY;

					if (startY > currentY + 80) {
						setTextValue('松手取消录音');
					} else {
						setTextValue('松手结束录音');
					}
				}
			};

			speechWrapper.addEventListener('touchend', speechTouchEndHandler, { passive: false });
			speechWrapper.addEventListener('touchmove', speechTouchMoveHandler, { passive: false });
			speechWrapper.addEventListener('touchcancel', speechTouchEndHandler, { passive: false });
		}

		return () => {
			document.removeEventListener('contextmenu', contextmenuHandler);
			document.removeEventListener('selectstart', preventDefaultHandler);
			document.removeEventListener('dragstart', preventDragHandler);
		};
	}, []);

	useEffect(() => {
		if (currentStatus === STATUS.NORMAL) {
			setTextValue('按住说话');
		} else if (currentStatus === STATUS.PAUSED) {
			setTextValue('按住继续说话');
		}
	}, [currentStatus]);

	useEffect(() => {
		if (textAreaRef.current && textAreaRef.current.resizableTextArea) {
			const textareaElement = textAreaRef.current.resizableTextArea.textArea;
			if (textareaElement) {
				textareaElement.scrollTop = textareaElement.scrollHeight;
			}
		}
	}, [speechText]);

	const onTextAreaChangeHandler = (e) => {
		onTextChange(e);
	};

	return (
		<div
			className={classNames(styles['speech-recognition'], {
				[styles['full-screen']]: fullScreen,
			})}
		>
			<div
				className={styles['bottom-wrapper']}
			>
				<div className={styles['close-button-wrapper']}>
					<div
						className={styles['close-button']}
						onClick={onClose}
					/>
				</div>
				<div className={styles['input-wrapper']}>
					<TextArea
						ref={textAreaRef}
						rootClassName={styles['text-area']}
						autoSize={false}
						value={speechText}
						onChange={onTextAreaChangeHandler}
						placeholder={'请说话，我在听...'}
					/>
					<div
						className={styles['full-screen-btn']}
						onClick={() => {
							setFullScreen(origin => !origin);
						}}>
					</div>
				</div>
				<div
					className={styles['action-wrapper']}
				>
					<div
						className={classNames(styles['wait-wrapper'], {
							[styles['hide']]: currentStatus === STATUS.SPEECH,
						})}
					>
						<div
							className={styles['clear-wrapper']}
							onClick={onClear}
						>
							<div className={styles['clear-button']}/>
							<div className={styles['clear-text']}>
								清空
							</div>
						</div>
						<div
							className={styles['speak-wrapper']}
						>
							<div
								className={styles['speak-text']}
							>
								{textValue}
							</div>
							<div
								className={classNames(styles['speak-button'], {
									[styles['pressed']]: isPressed,
									[styles['recording']]: currentStatus === STATUS.SPEECH,
								})}
								ref={speakBtnRef}
							>
								<img src={speakIcon} alt={''}/>
							</div>
						</div>
						<div
							className={classNames(styles['send-wrapper'], {
								[styles['disabled']]: speechText.length === 0 || loading,
								[styles['active']]: speechText.length > 0 && !loading,
							})}
							onClick={loading ? undefined : onSend}
						>
							<div className={styles['send-button']}/>
							<div
								className={styles['send-text']}
							>
								{loading ? '分析中...' : '发送'}
							</div>
						</div>
					</div>
					<div
						ref={speechWrapperRef}
						className={classNames(styles['speech-wrapper'], {
							[styles['hide']]: currentStatus !== STATUS.SPEECH,
						})}
					>
						<div
							className={styles['text']}
						>
							松手编辑，上滑取消
						</div>
						<div className={styles['animation-wrapper']}>
							<div className={styles['animation']}/>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SpeechRecognition;