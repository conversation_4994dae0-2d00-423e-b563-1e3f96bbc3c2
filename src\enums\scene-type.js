/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-04 09:27:33
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-04 10:05:19
 * @FilePath: src/enums/scene-type.js
 * @Version: 1.0.0
 * @Description: 组件描述 场景类型
 */

const SceneType = {
	// 小应智能助手
	DEFAULT: 'default',
	// 隐患查治助手
	CHECK_LIST: 'check_list',
	// 台账管理
	SEARCH_LEDGER: 'search_ledger',
	// 安全法规咨询
	MY_KNOWLEDGE_BASE: 'my_knowledge_base',
	// 编题出卷
	SET_EXAMS: 'set_exams',
	// 隐患图片分析
	IMAGE_ANALYSIS: 'image_analysis',
	// 企业量身定制
	ENTERPRISE_CUSTOM: 'enterprise_custom',
	// 风险图片分析
	RISK_ANALYSIS: 'risk_analysis',
	// 动火作业
	FIRE_OPERATION: 'fire_operation',
	// 指挥调度
	SC_DISPATCH_AGENT: 'sc-dispatch-agent',
	// 会议纪要-sta
	MEETING_SUMMARY_01: 'meeting-summary-01',
	// 会议纪要-predi
	MEETING_SUMMARY_02: 'meeting-summary-02',
	// 会议纪要-experiment
	MEETING_SUMMARY_03: 'meeting-summary-03',
	// 校园安全
	CAMPUS_INSPECTION: 'campus_inspection',
	// 视频学习
	VIDEO_LEARNING: 'video_learning',
};

export default SceneType;