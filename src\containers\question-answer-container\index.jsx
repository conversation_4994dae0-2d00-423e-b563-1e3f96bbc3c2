/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-03-12 10:56:51
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-03-12 11:51:45
 * @FilePath: src/containers/question-answer-container/index.jsx
 * @Version: 1.0.0
 * @Description: 组件描述
 */
import { apis } from '@/api/api';
import QuestionAnswer from '../../components/question-answer';
import { useEffect, useState } from 'react';

let timer = null;

const QuestionAnswerContainer = ({ data }) => {
	const [qaList, setQaList] = useState([]);
	const [recommendList, setRecommendList] = useState([]);
	const [useCount, setUseCount] = useState(0);

	const qaListScrollBottom = () => {
		const div = document.getElementById('qa-list');
		if (div) {
			div.scrollTop = div.scrollHeight;
		}
	};

	const getAnwer = (answer, list, cb) => {
		let slice_answer = '', i = 1;
		timer = setInterval(() => {
			if (slice_answer.length < answer.length) {
				slice_answer = answer.substring(0, i * 5);
				let _list = [...list];
				_list[_list.length - 1].content = slice_answer;
				setQaList(_list);
				i++;
			} else {
				if (timer) {
					clearInterval(timer);
					timer = null;
					console.log('close');
					cb();
				}
			}
		}, 100);
	};

	const getRecommend = (id) => {
		let params = {
			size: 3,
			ids: id
		};
		const count = useCount + 1;
		setUseCount(count);
		if (count === 3) {
			return;
		}
		apis.getApi.getChatRecommend(params).then(res => {
			if (res.code === 0) {
				setRecommendList(res.data);

				setTimeout(() => {
					qaListScrollBottom();
				}, 100);
			}
		});
	};

	const sendQuestion = (item) => {
		let slice_answer = '';
		let list = [
			...qaList,
			{ content: item.content, senderType: 'user' },
			{ content: slice_answer }
		];
		setQaList(list);
		setRecommendList([]);
		apis.getApi.getAnswerByNologin({ id: item.id }).then(res => {
			if (res.code === 0) {
				const answer = res.data;
				getAnwer(answer, list, () => {
					getRecommend(item.id);
				});
			}
		});
	};

	useEffect(() => {
		if (data) {
			sendQuestion(data.qaData);
		}
	}, [data]);

	useEffect(() => {
		qaListScrollBottom();
	}, [qaList]);

	useEffect(() => {
		return () => {
			if (timer) {
				clearInterval(timer);
				timer = null;
			}
		};
	}, []);

	return (
		<QuestionAnswer
			qaList={qaList}
			recommendList={recommendList}
			sceneData={data.sceneData}
			sendQuestion={sendQuestion}
		/>
	);
};

export default QuestionAnswerContainer;