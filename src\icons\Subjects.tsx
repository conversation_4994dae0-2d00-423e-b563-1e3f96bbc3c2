import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Subjects(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aeee__a"><rect rx="0" height="18" width="18"/></clipPath></defs><g clipPath="url(#14aeee__a)"><path data-follow-fill="#575B66"  d="M7.551 2.889v4.419a.922.922 0 0 1-.918.918h-4.41a.922.922 0 0 1-.918-.918v-4.42c0-.503.414-.917.918-.917h4.42c.503 0 .908.414.908.918Zm0 7.929v4.419a.922.922 0 0 1-.918.918h-4.41a.922.922 0 0 1-.918-.918v-4.41c0-.504.414-.918.918-.918h4.42a.895.895 0 0 1 .908.909Zm8.613-6.723H9.486a.622.622 0 0 1-.62-.621v-.36c0-.342.278-.621.62-.621h6.678c.342 0 .621.279.621.62v.36a.628.628 0 0 1-.62.622Zm0 3.663H9.486a.622.622 0 0 1-.62-.621v-.36c0-.342.278-.621.62-.621h6.678c.342 0 .621.279.621.62v.36a.622.622 0 0 1-.62.622Zm0 4.23H9.486a.622.622 0 0 1-.62-.621v-.36c0-.342.278-.621.62-.621h6.678c.342 0 .621.279.621.62v.36a.634.634 0 0 1-.62.622Zm0 3.663H9.486a.622.622 0 0 1-.62-.621v-.36c0-.342.278-.621.62-.621h6.678c.342 0 .621.279.621.62v.36a.628.628 0 0 1-.62.622Z" fill={_fill}/></g></g>
        </svg>
    )
}
