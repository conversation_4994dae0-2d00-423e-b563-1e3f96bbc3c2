# 微信语音识别工具

这是一个封装了微信JSSDK语音识别功能的工具库，提供了简单易用的API来实现语音录制和转换功能。

## 功能特性

- ✅ 微信JSSDK配置和初始化
- ✅ 语音录制开始/停止
- ✅ 语音转文字功能
- ✅ 录音状态管理
- ✅ 错误处理和用户反馈
- ✅ TypeScript类型支持

## 使用方法

### 1. 配置微信JSSDK

```javascript
import { configWeChat } from '@/tools/speech-recognition-wechat';

const config = {
  appId: 'your-app-id',
  timestamp: '1234567890',
  nonceStr: 'random-string',
  signature: 'your-signature'
};

configWeChat(
  config,
  (res) => {
    console.log('配置成功', res);
  },
  (error) => {
    console.error('配置失败', error);
  }
);
```

### 2. 开始录音

```javascript
import { startRecordWeChat } from '@/tools/speech-recognition-wechat';

startRecordWeChat(
  (res) => {
    console.log('开始录音成功', res);
  },
  (error) => {
    console.error('开始录音失败', error);
  }
);
```

### 3. 停止录音并转换

```javascript
import { stopRecordWeChat } from '@/tools/speech-recognition-wechat';

stopRecordWeChat(
  (text) => {
    console.log('转换结果:', text);
    // 处理转换后的文字
  },
  (error) => {
    console.error('转换失败', error);
  }
);
```

### 4. 监听录音自动结束

```javascript
import { onVoiceRecordEnd } from '@/tools/speech-recognition-wechat';

onVoiceRecordEnd((text, error) => {
  if (text) {
    console.log('自动录音结束，转换结果:', text);
  } else {
    console.error('自动录音转换失败:', error);
  }
});
```

### 5. 状态管理

```javascript
import { getRecordStatus, resetRecordStatus, RECORD_STATUS } from '@/tools/speech-recognition-wechat';

// 获取当前录音状态
const status = getRecordStatus();
console.log('当前状态:', status);

// 状态值
console.log('状态常量:', RECORD_STATUS);
// { IDLE: 'idle', RECORDING: 'recording', PROCESSING: 'processing' }

// 重置状态（在出现异常时使用）
resetRecordStatus();
```

## 完整使用示例

```javascript
import { 
  configWeChat, 
  startRecordWeChat, 
  stopRecordWeChat, 
  onVoiceRecordEnd,
  getRecordStatus,
  RECORD_STATUS 
} from '@/tools/speech-recognition-wechat';

class VoiceRecognition {
  constructor() {
    this.isRecording = false;
    this.setupVoiceRecordEnd();
  }

  // 初始化微信配置
  async initWeChat(config) {
    return new Promise((resolve, reject) => {
      configWeChat(config, resolve, reject);
    });
  }

  // 设置自动录音结束监听
  setupVoiceRecordEnd() {
    onVoiceRecordEnd((text, error) => {
      this.isRecording = false;
      if (text) {
        this.handleVoiceResult(text);
      } else {
        this.handleVoiceError(error);
      }
    });
  }

  // 开始录音
  startRecord() {
    if (getRecordStatus() !== RECORD_STATUS.IDLE) {
      console.warn('录音正在进行中');
      return;
    }

    startRecordWeChat(
      (res) => {
        this.isRecording = true;
        console.log('开始录音成功');
      },
      (error) => {
        console.error('开始录音失败:', error);
      }
    );
  }

  // 停止录音
  stopRecord() {
    if (getRecordStatus() !== RECORD_STATUS.RECORDING) {
      console.warn('当前没有正在进行的录音');
      return;
    }

    stopRecordWeChat(
      (text) => {
        this.isRecording = false;
        this.handleVoiceResult(text);
      },
      (error) => {
        this.isRecording = false;
        this.handleVoiceError(error);
      }
    );
  }

  // 处理语音识别结果
  handleVoiceResult(text) {
    console.log('语音识别结果:', text);
    // 在这里处理识别到的文字
  }

  // 处理语音识别错误
  handleVoiceError(error) {
    console.error('语音识别错误:', error);
    // 在这里处理错误情况
  }
}

// 使用示例
const voiceRecognition = new VoiceRecognition();

// 初始化
voiceRecognition.initWeChat({
  appId: 'your-app-id',
  timestamp: '1234567890',
  nonceStr: 'random-string',
  signature: 'your-signature'
}).then(() => {
  console.log('微信JSSDK初始化成功');
}).catch((error) => {
  console.error('微信JSSDK初始化失败:', error);
});
```

## 注意事项

1. 使用前需要先配置微信JSSDK
2. 确保在微信环境中使用
3. 需要在微信公众号后台配置JS接口安全域名
4. 录音功能需要用户授权
5. 建议在录音前检查当前状态，避免重复操作

## 错误处理

工具库内置了完善的错误处理机制：
- 参数验证
- 状态检查
- 用户友好的错误提示
- 详细的控制台日志

所有错误都会通过Toast组件显示给用户，同时在控制台输出详细信息供开发调试使用。
