// General styles for the container
.inspection-container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

// Header styles
.inspection-header {
    display: flex;
    align-items: center;
    padding: 10px 16px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    flex-shrink: 0;

    .header-icon {
        width: 17px;
        height: 17px;
        cursor: pointer;
    }

    .header-title {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 28px;
        // Adjust for the icon on the left to truly center the title
        transform: translateX(-20px);

        .title-logo {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }

        span {
            font-size: 18px;
            font-weight: 600;
            color: #1f2329;
        }
    }

    .view-toggle-btn {
        background: none;
        border: none;
        color: #4873FF;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        padding: 8px 12px;
        border-radius: 6px;
        transition: background-color 0.2s ease;

        &:hover {
            background-color: rgba(72, 115, 255, 0.1);
        }
    }
}

// Main content area
.inspection-main {
    flex-grow: 1;
    padding: 24px 16px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

// Avatar styles
.avatar-container {
    margin-bottom: 24px;
    .avatar-image {
        width: 152px;
        height: 152px;
        border-radius: 50%;
        border: 4px solid #fff;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        object-fit: cover;
    }
}

// Info card styles
.info-card, .selection-card {
    background-color: rgba(245, 245, 245, 1);
    border-radius: 12px;
    padding: 16px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    text-align: left;

    p {
        margin: 0;
        color: #3d4145;
        font-size: 16px;
        line-height: 1.6;
    }
}

.selection-card {
    background-color: rgba(245, 245, 245, 1);
    box-shadow: none;
    padding: 9px 16px 16px 16px;
    
    .selection-prompt {
        // color: #646a73;
        color: rgba(0, 0, 0, 0.80);
        font-size: 16px;
        margin-bottom: 16px;
    }

    .button-group {
        display: flex;
        justify-content: space-around;
        gap: 12px;
    }

    .inspection-button {
        flex: 1;
        padding: 10px 0;
        border: none;
        height: 40px;
        line-height: 20px;
        border-radius: 20px;
        color: white;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
        background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);

        &:hover {
            background-color: #3566e0;
        }
    }
}

// 拍照演示卡片样式
.photo-demo-card {
    background-color: rgba(245, 245, 245, 1);
    border-radius: 12px;
    padding: 16px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
    text-align: center;

    .demo-prompt {
        margin: 0 0 16px 0;
        color: rgba(0, 0, 0, 0.80);
        font-size: 16px;
    }

    .photo-demo-button {
        width: 100%;
        padding: 12px 0;
        border: none;
        height: 40px;
        line-height: 20px;
        border-radius: 20px;
        color: white;
        font-size: 15px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        background: linear-gradient(315deg, rgba(255, 87, 34, 0.9) 0%, rgba(255, 152, 0, 0.9) 100%);

        &:hover {
            background: linear-gradient(315deg, rgba(255, 87, 34, 1) 0%, rgba(255, 152, 0, 1) 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
        }
    }
}

// 视图切换卡片样式
.view-switch-card {
    background-color: rgba(245, 245, 245, 1);
    border-radius: 12px;
    padding: 16px;
    width: 100%;
    max-width: 400px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    margin-top: 20px;
    text-align: center;

    .switch-button {
        width: 100%;
        padding: 12px 0;
        border: 1px solid #4873FF;
        background: white;
        color: #4873FF;
        font-size: 16px;
        font-weight: 500;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
            background: #4873FF;
            color: white;
        }
    }
}

.main-layout{
    // display: flex;
    padding: 16px 8px 0 8px;

    .view-toggle {
        display: flex;
        justify-content: center;
        margin: 16px 0;
        gap: 8px;

        .toggle-btn {
            padding: 8px 16px;
            border: 1px solid #4873FF;
            background: white;
            color: #4873FF;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;

            &:hover {
                background: #E4EAFF;
            }

            &.active {
                background: #4873FF;
                color: white;
            }
        }
    }
    .userConf{
        display: flex;
        .userConfRight{
            width: calc(100% - 48px);
            background-color: #E9ECF6;
            border-radius: 10px;
            margin-left: 12px;
            font-size: 15px;
            line-height: 24px;
            padding: 10px;
    
        } 
    }
    .pageList{
        display: flex;
        flex-wrap: wrap;
        margin-top: 16px;
        justify-content: flex-end;
        margin-bottom: 20px;
        .pageBottom{
            display: flex;
            justify-content: flex-end;
            width: calc(100% - 48px);
            margin-top: 30px;
            .button{
                width: 50%;
                background: #E4EAFF;
                color: #4873FF;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 10px;
            }
        }
        .pageItem{
            border: 1px solid rgba(72, 115, 255, 0.60);
            width: calc(100% - 48px);
            padding: 0px 10px;
            height: 45px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-radius: 6px;
            margin-top: 14px;
            .pageItemBox{
                display: flex;
                align-items: center;
            }
            .pageItemTitle{
                border-radius: 10px;
                font-size: 15px;
                color: #000000;
                margin-left: 10px;
            }
        }
    }
}
.main-layout-bg{
    width: 100%;
    height: 216px;
    background: linear-gradient( 180deg, #A8D6FA 0%, rgba(178,218,252,0) 100%);
    border-radius: 0px 0px 0px 0px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}
