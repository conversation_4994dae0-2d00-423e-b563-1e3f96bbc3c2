# 移动端软键盘覆盖输入框问题解决方案

## 🔍 问题描述

在移动端浏览器中，当用户点击输入框时，软键盘弹起会覆盖输入框，导致用户无法看到正在输入的内容。这个问题在对话模式中尤为明显。

## 🛠️ 解决方案

### 方案1：CSS 环境变量 (推荐用于简单场景)

使用 CSS 的 `env()` 函数来处理安全区域：

```css
.input-container {
  bottom: env(safe-area-inset-bottom, 0px);
  padding-bottom: env(keyboard-inset-height, 0px);
}
```

### 方案2：JavaScript 动态检测 (推荐用于复杂场景)

使用 `mobile-input-fix.ts` 工具：

```typescript
import { initMobileInputFix, destroyMobileInputFix } from '@/util/mobile-input-fix';

// 初始化
const mobileInputFix = initMobileInputFix({
  inputContainerSelector: '.text-input-wrapper',
  enableAutoScroll: true,
  scrollOffset: 30,
  debug: true
});

// 清理
destroyMobileInputFix();
```

### 方案3：视口高度动态调整

使用 `keyboard-handler.ts` 工具：

```typescript
import { initKeyboardHandler } from '@/util/keyboard-handler';

const keyboardHandler = initKeyboardHandler({
  onKeyboardShow: (height) => {
    // 键盘弹起处理
  },
  onKeyboardHide: () => {
    // 键盘收起处理
  },
  adjustViewport: true
});
```

## 📱 平台差异

### iOS
- 主要通过 `focusin/focusout` 事件检测
- 视口高度变化较小
- 需要延迟检测键盘状态

### Android
- 主要通过 `resize` 事件检测
- 视口高度变化明显
- 键盘弹起时 `window.innerHeight` 会减小

## 🎯 最佳实践

### 1. 输入框容器样式
```css
.text-input-wrapper {
  position: fixed;
  bottom: 0;
  width: 100%;
  background: white;
  transition: transform 0.3s ease;
}

/* 键盘可见时 */
body.keyboard-visible .text-input-wrapper {
  transform: translateY(calc(-1 * var(--keyboard-height, 0px) / 2));
}
```

### 2. 消息容器样式
```css
.messages-container {
  padding-bottom: calc(16px + env(keyboard-inset-height, 0px));
  transition: padding-bottom 0.3s ease;
}

body.keyboard-visible .messages-container {
  padding-bottom: calc(var(--keyboard-height, 0px) + 20px);
}
```

### 3. 自动滚动处理
```typescript
// 输入框获得焦点时自动滚动
const scrollInputIntoView = (inputElement: HTMLElement) => {
  setTimeout(() => {
    inputElement.scrollIntoView({
      behavior: 'smooth',
      block: 'center'
    });
  }, 300); // 等待键盘动画完成
};
```

## 🔧 工具使用

### MobileInputFix 工具

```typescript
// 基本使用
const mobileInputFix = initMobileInputFix();

// 高级配置
const mobileInputFix = initMobileInputFix({
  inputContainerSelector: '.custom-input-wrapper',
  enableAutoScroll: true,
  scrollOffset: 50,
  debug: true
});

// 手动调整
mobileInputFix.adjustInput(inputElement);

// 获取状态
const status = mobileInputFix.getStatus();
console.log(status.isKeyboardVisible, status.keyboardHeight);
```

### KeyboardHandler 工具

```typescript
// 完整的键盘处理
const keyboardHandler = initKeyboardHandler({
  onKeyboardShow: (height) => {
    document.body.classList.add('keyboard-visible');
    document.documentElement.style.setProperty('--keyboard-height', `${height}px`);
  },
  onKeyboardHide: () => {
    document.body.classList.remove('keyboard-visible');
    document.documentElement.style.removeProperty('--keyboard-height');
  },
  adjustViewport: true
});
```

## 🧪 测试

使用 `MobileInputTest.tsx` 页面进行测试：

1. 在移动设备或模拟器中打开测试页面
2. 点击不同位置的输入框
3. 观察键盘弹起时的行为
4. 检查输入框是否被正确调整位置

## 📝 注意事项

1. **性能考虑**：避免频繁的DOM操作和样式计算
2. **兼容性**：测试不同品牌和版本的移动设备
3. **用户体验**：确保动画流畅，避免突兀的跳跃
4. **调试模式**：开发时启用 debug 模式查看详细日志

## 🔄 在项目中的应用

### InlineChat 组件
已集成 `mobile-input-fix` 工具，自动处理对话模式中的输入框覆盖问题。

### 其他输入组件
可以根据需要在其他包含输入框的组件中集成相应的解决方案。

## 🚀 未来优化

1. 支持更多的输入框类型检测
2. 优化不同设备的适配策略
3. 添加更多的自定义配置选项
4. 提供更好的调试和监控工具
