/**
 * 微信JSSDK类型声明
 */
declare module 'weixin-js-sdk' {
  interface WxConfig {
    debug?: boolean;
    appId: string;
    timestamp: number;
    nonceStr: string;
    signature: string;
    jsApiList?: string[];
    openTagList?: string[];
    success?: (res: any) => void;
    fail?: (res: any) => void;
  }

  interface WxCheckJsApiConfig {
    jsApiList: string[];
    success?: (res: any) => void;
    fail?: (res: any) => void;
  }

  interface WxRecordConfig {
    success?: (res: { localId: string }) => void;
    fail?: (res: any) => void;
  }

  interface WxTranslateVoiceConfig {
    localId: string;
    isShowProgressTips?: number;
    success?: (res: { translateResult: string }) => void;
    fail?: (res: any) => void;
  }

  interface WxVoiceRecordEndConfig {
    complete?: (res: { localId: string }) => void;
  }

  interface WxMiniProgramEnvResult {
    miniprogram: boolean;
  }

  interface WxMiniProgramNavigateConfig {
    url: string;
    success?: (res: any) => void;
    fail?: (res: any) => void;
    complete?: (res: any) => void;
  }

  interface WxMiniProgram {
    getEnv(callback: (res: WxMiniProgramEnvResult) => void): void;
    navigateTo(config: WxMiniProgramNavigateConfig): void;
  }

  interface WxSDK {
    config(config: WxConfig): void;
    ready(callback: () => void): void;
    error(callback: (res: any) => void): void;
    checkJsApi(config: WxCheckJsApiConfig): void;
    startRecord(config?: WxRecordConfig): void;
    stopRecord(config?: WxRecordConfig): void;
    translateVoice(config: WxTranslateVoiceConfig): void;
    onVoiceRecordEnd(config: WxVoiceRecordEndConfig): void;
    miniProgram: WxMiniProgram;
  }

  const wx: WxSDK;
  export default wx;
}
