import { useState } from 'react';
import { Collapse  } from 'antd';
import lianwangSelSvg from "@/assets/images/chat/icon_lianwang_sel.svg"
import style from "./WebsearchContent.module.scss"

const WebsearchContent = ({chatWebSearches}) => {
    const [activeKey, setActiveKey] = useState('1')
    return <div className={style['websearch-content']}>
        <Collapse activeKey={activeKey} expandIconPosition='end' onChange={()=>setActiveKey(activeKey ? '' : '1')} items={[
                {
                    key: '1',
                    label: <div style={{display: 'flex', alignItems: 'center', gap: '0.25rem'}}><img src={lianwangSelSvg}/> 参考来源</div>,
                    children: <div className={style['websearch']}>
                        {/* <div className={style['websearch-title']}>参考来源</div> */}
                        <div className={style['websearch-list']}>
                            {
                                chatWebSearches.map((item,index) => {
                                    return <div className={style['websearch-list-item']} key={index} onClick={()=>window.open(item.link)}>
                                        <img src={item.icon || lianwangSelSvg} alt="" />
                                        {index + 1}：
                                        {item.title}
                                    </div>
                                })
                            }
                        </div>
                    </div>
                }
            ]} />
    </div>
}

export default WebsearchContent