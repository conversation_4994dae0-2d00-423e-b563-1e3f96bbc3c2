.task-detail-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;

  // 头部样式
  .task-detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;

    .back-icon {
      font-size: 20px;
      color: #333;
      cursor: pointer;
    }

    .header-content {
      display: flex;
      align-items: center;
      flex: 1;
      position: relative;
      justify-content: flex-start;

      .page-title {
        font-size: 16px;
        font-weight: 500;
        margin: 0;
        color: #333;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.2s ease;
         // 溢出隐藏
         overflow: hidden;
         text-overflow: ellipsis;
         white-space: nowrap;
         max-width: 270px;

        &:hover {
          background-color: rgba(0, 0, 0, 0.05);
        }

        &:active {
          background-color: rgba(0, 0, 0, 0.1);
        }
      }
    }

    .patrol-btn {
      background-color: #4873FF;
      border: none;
      color: white;
      border-radius: 16px;
      font-size: 12px;
      padding: 6px 12px;
      height: 32px;
      background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
      border-radius: 9px 9px 9px 9px;
    }
    .disabled{
      background-color: #cbcbcb;
      color: #fff;
      cursor: not-allowed;
    }
  }

  // 模式切换标签
  .mode-tabs {
    display: flex;
    background-color: rgba(203, 203, 203, 1);
    padding: 0;
    position: relative;
    z-index: 999;

    .tab-item {
      flex: 1;
      height: 54px;
      line-height: 54px;
      text-align: center;
      font-size: 17px;
      
      color: #fff;
      cursor: pointer;
      position: relative;

      &.active {
        background: linear-gradient(135deg, #4873FF 0%, #5B8CFF 100%);
        color: white;
      }

      &:first-child.active {
        border-radius: 0 13px 13px 0;
      }

      &:last-child {
        background-color: rgba(203, 203, 203, 1);

        &.active {
          background: linear-gradient(135deg, #4873FF 0%, #5B8CFF 100%);
          border-radius: 13px 0 0 13px;
        }
      }
    }
  }
  .progress-info {
    display: flex;
    align-items: center;
    position: absolute;
    width: 65px;
    height: 84px;
    background: #00B578;
    left: 0px;
    top: 110px;
    z-index: 999;
    flex-direction: column;
    justify-content: center;

    .progress-text {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 14px;
      color: rgba(255,255,255,0.8);
      line-height: 21px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 2px;
    }

    .progress-percent {
      font-family: DIN Black, DIN Black;
      font-weight: 900;
      font-size: 20px;
      color: #FFFFFF;
      line-height: 24px;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 2px;
    }
    .progress-look{
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 11px;
      color: rgba(255,255,255,0.6);
      line-height: 15px;
      text-align: left;
      font-style: normal;
      text-transform: none;
    }
  }
  // 进度条区域
  .progress-section {
    background-color: #fff;
    margin-bottom: 8px;
    position: relative;
    width: 100%;
    height: 84px;
    overflow-x: auto;
    position: absolute;
    top: 110px;
    left: 0px;
    z-index: 99;
    

    // .stages-container-wrapper {
    //   display: flex;
    //   align-items: center;
    //   justify-content: center;
    //   width: 100%;
    //   height: 100%;
    //   background-color: #fff;
    // }

    .stages-container {
      display: flex;
      align-items: flex-start;
      position: relative;
      padding: 0 5px;
      width: max-content;
      white-space: nowrap;
      margin-left: 58px;
      align-items: center;
      height: 100%;
      .stage-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: relative;
        flex: 1;
        width: 86px;
        height: 100%;
        &.active {
          background-color: rgba(0, 0, 0, 0.08);
          .stage-dot {
            .pending-dot,.current-dot,.check-mark {
              width: 100%;
              height: 100%;
              background: url('../../assets/taskList/current-dot.png') no-repeat center center;
              background-size: 100% 100%;
            }
          }
        }

        .stage-dot {
          width: 30px;
          height: 30px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 8px;
          position: relative;
          z-index: 3;

          .check-mark {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/check-mark.png') no-repeat center center;
            background-size: 100% 100%;
          }

          .current-dot {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/current-dot.png') no-repeat center center;
            background-size: 100% 100%;
          }

          .pending-dot {
            width: 100%;
            height: 100%;
            background: url('../../assets/taskList/pending-dot.png') no-repeat center center;
            background-size: 100% 100%;
          }
        }

        .stage-name {
          font-size: 14px;
          color: #333;
          text-align: center;
          font-weight: 400;
          line-height: 1.2;
          max-width: 70px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        // 虚线连接线 - 精确还原图片中的虚线样式
        &:not(:last-child)::before {
          content: '';
          position: absolute;
          top: 24px;
          left: calc(50%);
          width: calc(100% - 28px);
          height: 2px;
          background: url('../../assets/taskList/current-line.png') no-repeat center center;
          background-size: 200% 100%;
          width: 100%;
          height: 14px;
          z-index: 1;
        }

        // 不同状态的样式
        &.completed {

          .stage-name {
            color: #333;
          }

          // 已完成阶段后的连接线为绿色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #52c41a 0px,
            //   #52c41a 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/completed-line.png') no-repeat center center;
            background-size: 100% 100%;
            width: 100%;
            height: 14px;
          }
        }

        &.current {

          .stage-name {
            color: #333;
            font-weight: 500;
          }

          // 当前阶段后的连接线为灰色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #d9d9d9 0px,
            //   #d9d9d9 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/current-line.png') no-repeat center center;
            background-size: 230% 100%;
            width: 100%;
            height: 14px;
          }
        }

        &.pending {
          .stage-dot {
            background-color: #f5f5f5;
            border: 2px solid #d9d9d9;
          }

          .stage-name {
            color: #999;
          }

          // 未开始阶段的连接线为灰色虚线
          &:not(:last-child)::before {
            // background-image: repeating-linear-gradient(
            //   to right,
            //   #d9d9d9 0px,
            //   #d9d9d9 6px,
            //   transparent 6px,
            //   transparent 12px
            // );
            background: url('../../assets/taskList/current-line.png') no-repeat center center;
            background-size: 200% 100%;
            width: 100%;
            height: 14px;
          }
        }
      }
    }
  }

  .chat-container{
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: calc(100vh - 200px);
    background-color: #fff;
  }

  // 检查项目列表
  .check-items-container {
    flex: 1;
    margin-top: 88px;
    padding: 10px 12px 12px 12px;
    overflow-y: auto;

    .check-item {
      height: 98px;
      background: linear-gradient( 180deg, #FFFFFF 0%, #EBF3F9 100%);
      border-radius: 12px;
      padding: 16px;
      margin-bottom: 9px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      cursor: pointer;
      transition: all 0.2s ease;
      border: 1px solid rgba(72.00000330805779, 115.00000074505806, 255, 0.2);
      &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
      }

      .item-header {
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        position: relative;
        padding-left: 60px;

        .status-badge {
          position: absolute;
          left: -16px;
          top: -3px;
          font-size: 14px;
          padding: 4px 8px;
          padding-right: 12px;
          border-radius: 0px 12px 12px 0px;
          margin-right: 12px;
          padding-left: 10px;

          &.pending {
            background: linear-gradient( 45deg, #47C0B0 0%, #49A5C8 100%);
            color: white;
          }

          &.completed {
            background: linear-gradient( 45deg, #C8CCD9 0%, #A8A8A8 100%);
            color: white;
          }
        }

        .item-name {
          font-size: 17px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.8);
          font-weight: 700;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .requirements-count {
          font-size: 14px;
          color: #666;
          margin-right: 8px;
        }

        .arrow {
          font-size: 16px;
          color: #999;
        }
      }

      .item-stats {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .stats-text {
          font-size: 14px;
          color: #666;
          flex: 1;
        }

        .action-buttons {
          display: flex;
          gap: 8px;
          align-items: center;

          .voice-btn,
          .camera-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            border: 1px solid #E5E5E5;
            background-color: rgba(72, 115, 255, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0px;

            .antd-mobile-icon {
              font-size: 16px;
              color: #4873FF;
            }

            &:active {
              border-color: #4873FF;
              background-color: rgba(72, 115, 255, 0.01);
            }
            span{
              width: 100%;
              height: 100%;
            }
          }

          .voice-btn .voice-btn-icon{
            background: url('../../assets/taskList/<EMAIL>') no-repeat center center;
            background-size: 50% 50%;
            width: 100%;
            height: 100%;
            display: block;
          }
          .camera-btn .camera-btn-icon{
            background: url('../../assets/taskList/<EMAIL>') no-repeat center center;
            background-size: 50% 50%;
            width: 100%;
            height: 100%;
            display: block;
          }
        }
      }
    }
    .check-item:last-child{
      margin-bottom: 120px;
    }
  }

  // 分析结果容器样式
  .analysis-results-container {
    margin: 16px;
    padding: 16px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .results-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f0f0;

      .results-title {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
      }

      .clear-btn {
        font-size: 12px;
        height: 28px;
        border-color: #d9d9d9;
        color: #666;

        &:active {
          border-color: #4873FF;
          color: #4873FF;
        }
      }
    }

    .section-title {
      font-size: 14px;
      font-weight: 500;
      color: #666;
      margin: 0 0 12px 0;
    }

    .analyzing-section,
    .photo-analysis-section,
    .voice-analysis-section {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .analyzing-item {
      padding: 12px;
      background: linear-gradient(90deg, #f0f8ff 0%, #e6f3ff 50%, #f0f8ff 100%);
      background-size: 200% 100%;
      animation: shimmer 2s infinite;
      border-radius: 6px;
      border-left: 3px solid #4873FF;

      .analyzing-text {
        font-size: 13px;
        color: #4873FF;
        font-weight: 500;
      }
    }

    @keyframes shimmer {
      0% {
        background-position: -200% 0;
      }
      100% {
        background-position: 200% 0;
      }
    }

    .analysis-item {
      padding: 8px 12px;
      margin-bottom: 8px;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 3px solid #4873FF;

      .analysis-text {
        font-size: 13px;
        color: #333;
        line-height: 1.4;
      }

      &:last-child {
        margin-bottom: 0;
      }
    }

    .captured-images-section {
      .images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 8px;
        margin-top: 8px;

        .image-preview {
          position: relative;
          width: 80px;
          height: 80px;
          border-radius: 6px;
          overflow: hidden;
          border: 1px solid #e9ecef;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
      }
    }
  }

  // Popup 容器样式重置
  :global(.edit-task-name-popup-body) {
    padding: 0 !important;
    background: transparent !important;
    border-radius: 0 !important;
    box-shadow: none !important;
  }

  // 编辑任务名称弹框样式 - 使用更高优先级
  .edit-task-name-modal {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%) !important;
    border-radius: 24px 24px 0 0 !important;
    width: 100% !important;
    max-width: 440px !important;
    box-shadow:
      0 -12px 40px rgba(0, 0, 0, 0.15),
      0 -4px 16px rgba(0, 0, 0, 0.08),
      0 0 0 1px rgba(255, 255, 255, 0.1) !important;
    position: relative !important;
    backdrop-filter: blur(20px) !important;
    animation: slideUpEnhanced 0.5s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
    overflow: hidden !important;
    min-height: 280px !important;

    // 顶部装饰性指示条
    &::before {
      content: '' !important;
      position: absolute !important;
      top: 16px !important;
      left: 50% !important;
      transform: translateX(-50%) !important;
      width: 48px !important;
      height: 5px !important;
      background: linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #94a3b8 100%) !important;
      border-radius: 3px !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
      z-index: 10 !important;
    }

    &:hover::before {
      background: linear-gradient(90deg, #cbd5e1 0%, #94a3b8 50%, #64748b 100%) !important;
      width: 56px !important;
      transform: translateX(-50%) scale(1.05) !important;
    }

    // 添加微妙的背景纹理
    &::after {
      content: '' !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      bottom: 0 !important;
      background:
        radial-gradient(circle at 20% 20%, rgba(72, 115, 255, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(168, 85, 247, 0.03) 0%, transparent 50%) !important;
      pointer-events: none !important;
      z-index: 0 !important;
    }

    .modal-header {
      display: flex !important;
      align-items: center !important;
      justify-content: space-between !important;
      padding: 32px 32px 24px !important;
      border-bottom: 2px solid transparent !important;
      background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%),
        linear-gradient(90deg, rgba(72, 115, 255, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%) !important;
      position: relative !important;
      z-index: 2 !important;
      backdrop-filter: blur(10px) !important;

      // 添加装饰性边框
      &::before {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 32px !important;
        right: 32px !important;
        height: 2px !important;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(72, 115, 255, 0.2) 20%,
          rgba(72, 115, 255, 0.4) 50%,
          rgba(168, 85, 247, 0.2) 80%,
          transparent 100%) !important;
        border-radius: 1px !important;
      }

      .modal-title {
        font-size: 22px !important;
        font-weight: 800 !important;
        color: transparent !important;
        letter-spacing: -0.04em !important;
        background: linear-gradient(135deg, #1e293b 0%, #475569 30%, #4f46e5 70%, #7c3aed 100%) !important;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent !important;
        background-clip: text !important;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
        position: relative !important;
        z-index: 1 !important;
      }

      .close-icon {
        font-size: 26px !important;
        color: #64748b !important;
        cursor: pointer !important;
        padding: 12px !important;
        border-radius: 50% !important;
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%) !important;
        border: 2px solid rgba(226, 232, 240, 0.6) !important;
        box-shadow:
          0 4px 12px rgba(0, 0, 0, 0.08),
          0 2px 4px rgba(0, 0, 0, 0.04),
          inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
        backdrop-filter: blur(8px) !important;
        position: relative !important;
        overflow: hidden !important;

        &::before {
          content: '' !important;
          position: absolute !important;
          top: 0 !important;
          left: 0 !important;
          right: 0 !important;
          bottom: 0 !important;
          background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%) !important;
          opacity: 0 !important;
          transition: opacity 0.3s ease !important;
          border-radius: 50% !important;
        }

        &:hover {
          color: #ef4444 !important;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(254, 242, 242, 0.95) 100%) !important;
          border-color: rgba(239, 68, 68, 0.3) !important;
          transform: rotate(90deg) scale(1.15) !important;
          box-shadow:
            0 6px 20px rgba(239, 68, 68, 0.15),
            0 3px 8px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;

          &::before {
            opacity: 1 !important;
          }
        }

        &:active {
          transform: rotate(90deg) scale(1.05) !important;
          box-shadow:
            0 3px 12px rgba(239, 68, 68, 0.2),
            0 1px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.7) !important;
        }
      }
    }

    .modal-content {
      padding: 32px 32px 28px !important;
      background:
        linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%),
        radial-gradient(circle at 30% 30%, rgba(72, 115, 255, 0.02) 0%, transparent 60%),
        radial-gradient(circle at 70% 70%, rgba(168, 85, 247, 0.02) 0%, transparent 60%) !important;
      position: relative !important;
      z-index: 1 !important;

      // 输入框容器样式
      :global(.adm-input) {
        border: 3px solid transparent !important;
        border-radius: 16px !important;
        padding: 20px 24px !important;
        font-size: 17px !important;
        line-height: 1.6 !important;
        background:
          linear-gradient(145deg, #ffffff 0%, #f8fafc 100%),
          linear-gradient(90deg, rgba(72, 115, 255, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%) !important;
        background-clip: padding-box, border-box !important;
        transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1) !important;
        box-shadow:
          0 4px 16px rgba(0, 0, 0, 0.06),
          0 2px 8px rgba(0, 0, 0, 0.04),
          inset 0 1px 0 rgba(255, 255, 255, 0.8) !important;
        font-weight: 600 !important;
        color: #1e293b !important;
        backdrop-filter: blur(8px) !important;
        position: relative !important;

        &:focus {
          border-color: transparent !important;
          background:
            linear-gradient(145deg, #ffffff 0%, #f8fafc 100%),
            linear-gradient(90deg, #4f46e5 0%, #7c3aed 100%) !important;
          background-clip: padding-box, border-box !important;
          box-shadow:
            0 0 0 4px rgba(79, 70, 229, 0.15),
            0 8px 24px rgba(79, 70, 229, 0.2),
            0 4px 12px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
          outline: none !important;
          transform: translateY(-2px) scale(1.02) !important;
        }

        &::placeholder {
          color: #94a3b8 !important;
          font-size: 16px !important;
          font-weight: 500 !important;
          opacity: 0.8 !important;
        }

        &:hover:not(:focus) {
          border-color: transparent !important;
          background:
            linear-gradient(145deg, #ffffff 0%, #f1f5f9 100%),
            linear-gradient(90deg, rgba(72, 115, 255, 0.08) 0%, rgba(168, 85, 247, 0.08) 100%) !important;
          background-clip: padding-box, border-box !important;
          box-shadow:
            0 6px 20px rgba(0, 0, 0, 0.08),
            0 3px 12px rgba(0, 0, 0, 0.06),
            inset 0 1px 0 rgba(255, 255, 255, 0.9) !important;
          transform: translateY(-1px) !important;
        }
      }

      // 输入框包装器
      .adm-input-wrapper {
        position: relative;

        .adm-input-clear {
          color: #999;
          transition: all 0.2s ease;
          border-radius: 50%;
          padding: 4px;

          &:hover {
            color: #666;
            background: rgba(0, 0, 0, 0.05);
            transform: scale(1.1);
          }

          &:active {
            transform: scale(0.95);
          }
        }
      }
    }

    .modal-footer {
      display: flex !important;
      gap: 24px !important;
      padding: 28px 32px 40px !important;
      border-top: 2px solid transparent !important;
      background:
        linear-gradient(145deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%),
        linear-gradient(90deg, rgba(72, 115, 255, 0.03) 0%, rgba(168, 85, 247, 0.03) 100%) !important;
      position: relative !important;
      z-index: 2 !important;
      backdrop-filter: blur(10px) !important;

      // 添加装饰性顶部边框
      &::before {
        content: '' !important;
        position: absolute !important;
        top: 0 !important;
        left: 32px !important;
        right: 32px !important;
        height: 2px !important;
        background: linear-gradient(90deg,
          transparent 0%,
          rgba(72, 115, 255, 0.15) 20%,
          rgba(72, 115, 255, 0.3) 50%,
          rgba(168, 85, 247, 0.15) 80%,
          transparent 100%) !important;
        border-radius: 1px !important;
      }

      .cancel-btn {
        flex: 1;
        height: 48px;
        border: 2px solid #e8eaed;
        color: #6b7280;
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
        border-radius: 12px;
        font-size: 16px;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(107, 114, 128, 0.05) 0%, rgba(107, 114, 128, 0.02) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &:hover {
          border-color: #d1d5db;
          color: #374151;
          background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08), 0 2px 8px rgba(0, 0, 0, 0.06);

          &::before {
            opacity: 1;
          }
        }

        &:active {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
        }
      }

      .confirm-btn {
        flex: 1;
        height: 48px;
        background: linear-gradient(135deg, #4873FF 0%, #3461E6 100%);
        border: 2px solid transparent;
        color: white;
        border-radius: 12px;
        font-size: 16px;
        font-weight: 700;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        box-shadow: 0 4px 16px rgba(72, 115, 255, 0.35), 0 2px 8px rgba(72, 115, 255, 0.2);
        position: relative;
        overflow: hidden;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 50%, rgba(255, 255, 255, 0) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        &::after {
          content: '';
          position: absolute;
          top: 50%;
          left: 50%;
          width: 0;
          height: 0;
          background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
          border-radius: 50%;
          transform: translate(-50%, -50%);
          transition: all 0.3s ease;
        }

        &:hover {
          background: linear-gradient(135deg, #3461E6 0%, #2850D6 100%);
          transform: translateY(-3px);
          box-shadow: 0 6px 20px rgba(72, 115, 255, 0.45), 0 3px 12px rgba(72, 115, 255, 0.3);

          &::before {
            opacity: 1;
          }

          &::after {
            width: 100px;
            height: 100px;
            opacity: 0.6;
          }
        }

        &:active {
          transform: translateY(-1px);
          box-shadow: 0 3px 12px rgba(72, 115, 255, 0.4), 0 2px 8px rgba(72, 115, 255, 0.25);

          &::after {
            width: 120px;
            height: 120px;
            opacity: 0.8;
          }
        }

        &:disabled {
          background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
          color: #6b7280;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          cursor: not-allowed;
          transform: none;
          text-shadow: none;

          &:hover {
            background: linear-gradient(135deg, #d1d5db 0%, #9ca3af 100%);
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

            &::before,
            &::after {
              opacity: 0;
            }
          }

          &:active {
            transform: none;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    // 响应式设计
    @media (max-width: 375px) {
      max-width: 100%;
      border-radius: 20px 20px 0 0;

      .modal-header {
        padding: 20px 24px 16px;

        .modal-title {
          font-size: 18px;
        }

        .close-icon {
          font-size: 22px;
          padding: 6px;
        }
      }

      .modal-content {
        padding: 24px;

        :global(.adm-input) {
          font-size: 16px;
          padding: 14px 18px;
        }
      }

      .modal-footer {
        padding: 20px 24px 28px;
        gap: 16px;

        .cancel-btn,
        .confirm-btn {
          height: 44px;
          font-size: 16px;
        }
      }
    }

    // 超小屏幕优化
    @media (max-width: 320px) {
      .modal-header {
        padding: 16px 20px 12px;

        .modal-title {
          font-size: 16px;
        }
      }

      .modal-content {
        padding: 20px;
      }

      .modal-footer {
        padding: 16px 20px 24px;
        gap: 12px;

        .cancel-btn,
        .confirm-btn {
          height: 42px;
          font-size: 15px;
        }
      }
    }

    // 弹出动画
    animation: slideUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  // 弹出动画关键帧
  @keyframes slideUp {
    0% {
      transform: translateY(100%) scale(0.95);
      opacity: 0;
      filter: blur(4px);
    }
    50% {
      transform: translateY(-5%) scale(1.02);
      opacity: 0.8;
      filter: blur(1px);
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
      filter: blur(0);
    }
  }

  // 增强版弹出动画
  @keyframes slideUpEnhanced {
    0% {
      transform: translateY(100%) scale(0.9) rotateX(10deg);
      opacity: 0;
      filter: blur(8px);
    }
    30% {
      transform: translateY(20%) scale(0.95) rotateX(5deg);
      opacity: 0.3;
      filter: blur(4px);
    }
    60% {
      transform: translateY(-8%) scale(1.05) rotateX(-2deg);
      opacity: 0.8;
      filter: blur(1px);
    }
    80% {
      transform: translateY(2%) scale(1.02) rotateX(1deg);
      opacity: 0.95;
      filter: blur(0.5px);
    }
    100% {
      transform: translateY(0) scale(1) rotateX(0deg);
      opacity: 1;
      filter: blur(0);
    }
  }
}
