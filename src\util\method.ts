/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-09 09:36:34
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-13 18:20:59
 * @FilePath: \fSafetyh5\src\util\method.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */

import { apis } from '@/api/api';
import { EventSourcePolyfill } from 'event-source-polyfill';
import lodash from 'lodash';

export function getUrlParameter(paramName: string, url: string = window.location.href): string | null {
    // 创建一个 URLSearchParams 对象
    const params = new URLSearchParams(new URL(url).search);
    // 获取指定的参数值
    return params.get(paramName);
}


/**
 * 计算两个地理坐标之间的距离
 * @param {number} lat1 - 第一个点的纬度（度）
 * @param {number} lon1 - 第一个点的经度（度）
 * @param {number} lat2 - 第二个点的纬度（度）
 * @param {number} lon2 - 第二个点的经度（度）
 * @returns {number} 距离，单位为公里
 */
export function distanceBetweenPoints(lat1:number, lon1:number, lat2:number, lon2:number) {
    // 将度数转换为弧度
    const toRadians = (degrees) => degrees * Math.PI / 180;
    
    const R = 6371; // 地球平均半径，单位为公里
    
    // 将纬度和经度转换为弧度
    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
              Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    // 计算距离
    const distance = R * c;
    // 保留两位小数
    const distanceStr = distance.toFixed(2);
    return distanceStr;
}

/**
 * 获取url
 * @param {*} url 
 */
export const getUrlParmse = (urls = window.location.href) => {
    const url = decodeURIComponent(urls)
    let arr = url.split('?');
    let params
    let obj = {token: '',tid: ''};

    if (arr.length > 1) {
        params = arr[1].split('&');
        for (let i = 0; i < params.length; i++) {
            let param = params[i].split('=');
            obj[param[0]] = param[1];
        }
    }

    return obj as any;
}

export const urlName = '/inspection'

export const getUserInfo = () => {
    const userInfo = sessionStorage.getItem('userInfo');
    if (userInfo) {
        return JSON.parse(userInfo);
    }
    return null;
}

export const getUserId = () => {
    const userInfo = getUserInfo();
    if (userInfo) {
        return userInfo.uniqueId;
    }
    return '';
}

export const letterToNumber = (char)=> {
    if (char >= 'a' && char <= 'z') {
      // 小写字母
      return char.charCodeAt(0) - 'a'.charCodeAt(0);
    } else if (char >= 'A' && char <= 'Z') {
      // 大写字母
      return char.charCodeAt(0) - 'A'.charCodeAt(0);
    } else {
      return -1; // 非字母返回 -1
    }
  }

export const arraysEqual =(arr1=[], arr2=[])=> {
    // 首先检查数组的长度
    if (arr1.length !== arr2.length) {
        return false;
    }

    // 循环比较每个元素
    for (let i = 0; i < arr1.length; i++) {
        // 如果发现不相等的元素，返回false
        if (arr1[i] !== arr2[i]) {
            return false;
        }
    }

    // 如果所有元素都相等，返回true
    return true;
}

export const beforeUpload = (file:any,callback:any) => {
    // 创建预签名url
    let params = [
        {
            fileName: file.name,
            type: 'aiCheck'
        }
    ]
    apis.ginkgoUpload.createPreSignUrl(params).then(res => {
        if(res.code === 0) {
            const list = res.data || []
            Promise.all(list.map(async (l, i) => {
                const presignedUrl = l.fileUrl || ''
                if(presignedUrl) {
                    const chunk = file
                    const response = await fetch(presignedUrl, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/octet-stream',
                        },
                        body: chunk
                    });
    
                } 
            })).then(() => {
                let params = list.map((l, i) => {return {...l, size: file.size, fileUrl: l.fileUrl?.split('?')[0] || ''}})
    
                apis.ginkgoUpload.createPreSignFileRecord(params).then(async res2 => {
                    if(res2.code === 0) {
                        callback(res2.data);
                    }
                })
            })
                
        }
    })
}

// EventSource处理配置接口
// interface EventSourceConfig {
//   url: string;
//   qaList: any[];
//   onMessage?: (qaList: any[], answer: string) => void;
//   onComplete?: (qaList: any[], answerId: string, questionId: string) => void;
//   onError?: (error: any) => void;
//   setLoading?: (loading: boolean) => void;
//   setShowStop?: (show: boolean) => void;
//   setRecommendList?: (list: any[]) => void;
//   setQaList?: (list: any[]) => void;
//   qaListScrollBottom?: () => void;
//   getRelvantAndQestions?: (list: any[]) => void;
//   sceneData?: any;
//   dispatch?: any;
//   qaListLoading?: boolean;
//   diaActive?: any;
//   getQaList?: (diaActive: any, arg1: boolean, arg2: boolean) => void;
// }


export const getToken = () => {
  return getUrlParameter('token') || sessionStorage.getItem("token");
}

/**
 * EventSource处理公共函数
 * @param config EventSource配置对象
 * @returns EventSource实例和停止函数
 */
export const createEventSourceHandler = (config) => {
  const {
    url,
    qaList,
    onMessage,
    onComplete,
    onError,
    setLoading,
    setShowStop,
    setRecommendList,
    setQaList,
    qaListScrollBottom,
    getRelvantAndQestions,
    sceneData,
    dispatch,
    qaListLoading,
  } = config;

  let eventSource: any = null;
  let answer: string = '';

  const throttledHandler = lodash.throttle(() => {
    dispatch?.({ type: 'isChat', data: true });
    setLoading?.(true);
    setRecommendList?.([]);

    // 使用可变引用来保持最新的list状态
    let currentList = [...qaList];

    eventSource = new EventSourcePolyfill(url, {
      headers: {
        'Authorization': `Bearer ${getToken()}`
      },
    });

    qaListScrollBottom?.();

    eventSource.addEventListener('message', function (event: any) {
      setShowStop?.(true);
      let obj: any = {};

      try {
        obj = JSON.parse(event.data) || {};
      } catch {
        obj = {};
      }

      if (obj.content) {
        // 确保currentList不为空且有有效的chatContents
        if (currentList.length > 0 && currentList[currentList.length - 1]?.chatContents?.[0]) {
          if (obj.type === 'path') {
            currentList[currentList.length - 1].chatContents[0].content = getHtml(obj.content) || obj.content;
          } else {
            currentList[currentList.length - 1].chatContents[0].content = obj.content;
          }
          answer = currentList[currentList.length - 1].chatContents[0].content;
          qaListScrollBottom?.();
          onMessage?.(currentList, answer);
        } else {
          // 如果currentList为空或结构不正确，创建新的消息项
          const newMessage = {
            id: Date.now().toString(),
            chatContents: [{ content: obj.type === 'path' ? (getHtml?.(obj.content) || obj.content) : obj.content }],
            senderType: 'assistant'
          };
          currentList.push(newMessage);
          answer = newMessage.chatContents[0].content;
          qaListScrollBottom?.();
          onMessage?.(currentList, answer);
        }
        // 立即更新状态
        setQaList?.(currentList);
      } else if (obj.answerId && !qaListLoading) {
        // 停止或结束时返回答案id拼接到问答列表
        if (currentList.length > 0) {
          currentList[currentList.length - 1] = {
            id: obj.answerId,
            parentId: obj.questionId,
            chatContents: [{ content: answer }],
            status: obj.status || 0,
            mark: 0,
            senderType: 'assistant'
          };

          // 确保有足够的元素才设置questionId
          if (currentList.length > 1) {
            currentList[currentList.length - 2].id = obj.questionId;
          }
        } else if (answer) {
          // 如果currentList为空但有answer，创建消息项
          const newMessage = {
            id: obj.answerId,
            parentId: obj.questionId,
            chatContents: [{ content: answer }],
            status: obj.status || 0,
            mark: 0,
            senderType: 'assistant'
          };
          currentList.push(newMessage);
        }

        dispatch?.({ type: 'isChat', data: false });
        dispatch?.({ type: 'getLimit', data: true });

        if (sceneData?.isNextRecommend) {
          getRelvantAndQestions?.(currentList);
        }

        // 更新状态并调用完成回调
        setQaList?.(currentList);
        onComplete?.(currentList, obj.answerId, obj.questionId);
      } else if (obj.code === 1000 || obj.code === 2201) {
        // 处理错误情况
        console.error(obj.message);
      }
    });

    eventSource.onerror = function (error: any) {
      eventSource?.close();
      eventSource?.removeEventListener('message');
      onError?.(error);
    };
  }, 1500);

  // 停止EventSource的函数
  const stopEventSource = () => {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }
    setLoading?.(false);
    setShowStop?.(false);
    dispatch?.({ type: 'isChat', data: false });
  };

  return {
    start: throttledHandler,
    stop: stopEventSource,
    eventSource
  };
};

/**
 * @description 生成对话id
 */
export const getReqId = () => {
	return (`${new Date().getTime()}-${Math.floor(Math.random() * (9999 - 1000 + 1)) + 1000}`);
};


/**
 * @description markdown文本处理
 */
export const getHtml = (answer:any) => {
	let _answer = answer;
	if (answer.includes('<|startofexec|>')) {
		let url = answer.replace('<|startofexec|>', '').replace('<|endofexec|>', '').replace('vfiles.gingpt.cn', 'www.gingpt.cn');
		let type = url.slice(-3);
		if (type === 'wav') {
			return `<audio controls src=${url}><meta name="referrer" content="never"></audio>`;
		} else {
			let poster = url.slice(0, -4) + '.png';
			return `<video controls >
                <source src=${url} poster=${poster}>
            </video>`;
		}

	} else {
		_answer = answer;
	}

	function processExamTags(input) {
		return input.replace(/<exam>([\s\S]*?)(<\/exam>|$)/g, (match, content, closingTag) => {
			// 处理内容中的换行符
			const processed = content
				.replace(/\n{3,}/g, '<br/><br/>')  // 3个及以上换行 → 双<br/>
				.replace(/\n{2}/g, '<br/>');       // 2个换行 → 单<br/>

			// 根据是否存在闭合标签重组内容
			return closingTag === '</exam>'
				? `<exam>${processed}</exam>`
				: `<exam>${processed}`;
		});
	}

	_answer = processExamTags(_answer);

	let str = _answer.replaceAll('<exam>', '<think>').replaceAll('</exam>', '</think>').replaceAll('**<riskcontent>**', '').replaceAll('**</riskcontent>**', '');
	// let str = _answer.replaceAll('<think>', '<blockquote>').replaceAll('</think>', '</blockquote>').replaceAll('<exam>', '<blockquote>').replaceAll('</exam>', '</blockquote>').replaceAll('**<riskcontent>**', '').replaceAll('**</riskcontent>**', '');

	function replenishLabelEnd(label) {
		const hasBlockquoteStart = str.indexOf(`<${label}>`) !== -1;
		const hasBlockquoteEnd = str.indexOf(`</${label}>`) !== -1;

		if (hasBlockquoteStart && !hasBlockquoteEnd) {
			str = str + `</${label}>`;
		}
	}
	replenishLabelEnd('blockquote')
	replenishLabelEnd('think')
	return str;
};
/**
 * @description markdown文本处理
 * @param {Number} arr 文件字节大小
 */
export const getFileSize = (sizeInBytes) => {
	if (sizeInBytes <= 0) return '0 B';

	var units = ['B', 'K', 'M', 'G', 'T'];
	var i = Math.floor(Math.log(sizeInBytes) / Math.log(1024));

	return parseFloat((sizeInBytes / Math.pow(1024, i)).toFixed(2)) + ' ' + units[i];
};



