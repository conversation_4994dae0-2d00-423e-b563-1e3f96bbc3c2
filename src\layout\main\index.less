.main-layout,.main-layout-noHeader {
  // display: flex;
  // flex-direction: column;
  // height: 100%;
  overflow: auto;
  .layout-header{
    flex: 0 0 auto;
    background-color: #f5f5f5;
    // box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
    height: 47px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    .goBackBox{
      width: 100px;
      height: 100%;
      display: flex;
      align-items: center;
      position: absolute;
      left: 15px;
      z-index: 1000;
      .ListTwo{
        width: 18px;
        height: 18px;
        background: url('../../image/mistankes/bank.png') no-repeat;
        background-size: 100% 100%;
      }
    }
    .titleBox{
      display: flex;
      align-items: center;
      .titleCC{
        flex: 1;
        text-align: center;
        font-size: 18px;
        color: #333;
        width: 100%;
        margin: 0 auto;
        font-weight: 700;
      }
    }
    .icon-mine{
      position: absolute;
      right: 10px;
      z-index: 1000;
    }
    .goBack{
      width: 10px;
      height: 20px;
      background: url('../../assets/home/<USER>') no-repeat;
      background-size: 100% 100%;
    }
    .title{
      flex: 1;
      text-align: center;
      font-size: 18px;
      color: #333;
      width: 100%;
    }
  }
  .layout-content {
    flex: 1 1 auto;
    overflow-y: auto;
    margin-bottom: 68px;
  }

  .layout-tab {
    flex: 0 0 auto;
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 80px;
    z-index: 1000;
    background-color: #fff;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    .adm-tab-bar{
      // margin-top: 10px;
    }
  }
  .footer {
    background: #fff;
    border-radius: 0px;
    padding: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: fixed;
    bottom:0px;
    width: 100%;
    left: 0px;
    .adm-tab-bar-item-title{
      font-size: 14px;
    }
  }
}
.main-layout-noHeader{
  height: calc(100% - 20px);
  background-color: #f5f5f5;
  .layout-content {
    margin-bottom: 100px;
  }
}

// .main-layout-home{
//   height: calc(100% - 136px);
// }
