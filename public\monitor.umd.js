!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).MonitorJS={})}(this,(function(e){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var r={};
/*!
	    localForage -- Offline Storage, Improved
	    Version 1.10.0
	    https://localforage.github.io/localForage
	    (c) 2013-2017 Mozilla, Apache License 2.0
	*/
!function(e){e.exports=function e(t,r,o){function i(s,c){if(!r[s]){if(!t[s]){if(!c&&n)return n(s);if(a)return a(s,!0);var u=new Error("Cannot find module '"+s+"'");throw u.code="MODULE_NOT_FOUND",u}var f=r[s]={exports:{}};t[s][0].call(f.exports,(function(e){var n=t[s][1][e];return i(n||e)}),f,f.exports,e,t,r,o)}return r[s].exports}for(var a=n,s=0;s<o.length;s++)i(o[s]);return i}({1:[function(e,n,r){(function(e){var t,r,o=e.MutationObserver||e.WebKitMutationObserver;if(o){var i=0,a=new o(f),s=e.document.createTextNode("");a.observe(s,{characterData:!0}),t=function(){s.data=i=++i%2}}else if(e.setImmediate||void 0===e.MessageChannel)t="document"in e&&"onreadystatechange"in e.document.createElement("script")?function(){var t=e.document.createElement("script");t.onreadystatechange=function(){f(),t.onreadystatechange=null,t.parentNode.removeChild(t),t=null},e.document.documentElement.appendChild(t)}:function(){setTimeout(f,0)};else{var c=new e.MessageChannel;c.port1.onmessage=f,t=function(){c.port2.postMessage(0)}}var u=[];function f(){var e,t;r=!0;for(var n=u.length;n;){for(t=u,u=[],e=-1;++e<n;)t[e]();n=u.length}r=!1}function l(e){1!==u.push(e)||r||t()}n.exports=l}).call(this,void 0!==t?t:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(e,t,n){var r=e(1);function o(){}var i={},a=["REJECTED"],s=["FULFILLED"],c=["PENDING"];function u(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=c,this.queue=[],this.outcome=void 0,e!==o&&h(this,e)}function f(e,t,n){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function l(e,t,n){r((function(){var r;try{r=t(n)}catch(t){return i.reject(e,t)}r===e?i.reject(e,new TypeError("Cannot resolve promise with itself")):i.resolve(e,r)}))}function d(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function h(e,t){var n=!1;function r(t){n||(n=!0,i.reject(e,t))}function o(t){n||(n=!0,i.resolve(e,t))}function a(){t(o,r)}var s=p(a);"error"===s.status&&r(s.value)}function p(e,t){var n={};try{n.value=e(t),n.status="success"}catch(e){n.status="error",n.value=e}return n}function v(e){return e instanceof this?e:i.resolve(new this(o),e)}function m(e){var t=new this(o);return i.reject(t,e)}function y(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var n=e.length,r=!1;if(!n)return this.resolve([]);for(var a=new Array(n),s=0,c=-1,u=new this(o);++c<n;)f(e[c],c);return u;function f(e,o){function c(e){a[o]=e,++s!==n||r||(r=!0,i.resolve(u,a))}t.resolve(e).then(c,(function(e){r||(r=!0,i.reject(u,e))}))}}function g(e){var t=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var n=e.length,r=!1;if(!n)return this.resolve([]);for(var a=-1,s=new this(o);++a<n;)c(e[a]);return s;function c(e){t.resolve(e).then((function(e){r||(r=!0,i.resolve(s,e))}),(function(e){r||(r=!0,i.reject(s,e))}))}}t.exports=u,u.prototype.catch=function(e){return this.then(null,e)},u.prototype.then=function(e,t){if("function"!=typeof e&&this.state===s||"function"!=typeof t&&this.state===a)return this;var n=new this.constructor(o);return this.state!==c?l(n,this.state===s?e:t,this.outcome):this.queue.push(new f(n,e,t)),n},f.prototype.callFulfilled=function(e){i.resolve(this.promise,e)},f.prototype.otherCallFulfilled=function(e){l(this.promise,this.onFulfilled,e)},f.prototype.callRejected=function(e){i.reject(this.promise,e)},f.prototype.otherCallRejected=function(e){l(this.promise,this.onRejected,e)},i.resolve=function(e,t){var n=p(d,t);if("error"===n.status)return i.reject(e,n.value);var r=n.value;if(r)h(e,r);else{e.state=s,e.outcome=t;for(var o=-1,a=e.queue.length;++o<a;)e.queue[o].callFulfilled(t)}return e},i.reject=function(e,t){e.state=a,e.outcome=t;for(var n=-1,r=e.queue.length;++n<r;)e.queue[n].callRejected(t);return e},u.resolve=v,u.reject=m,u.all=y,u.race=g},{1:1}],3:[function(e,n,r){(function(t){"function"!=typeof t.Promise&&(t.Promise=e(2))}).call(this,void 0!==t?t:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(e,t,n){var r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e};function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(){try{if("undefined"!=typeof indexedDB)return indexedDB;if("undefined"!=typeof webkitIndexedDB)return webkitIndexedDB;if("undefined"!=typeof mozIndexedDB)return mozIndexedDB;if("undefined"!=typeof OIndexedDB)return OIndexedDB;if("undefined"!=typeof msIndexedDB)return msIndexedDB}catch(e){return}}var a=i();function s(){try{if(!a||!a.open)return!1;var e="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),t="function"==typeof fetch&&-1!==fetch.toString().indexOf("[native code");return(!e||t)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange}catch(e){return!1}}function c(e,t){e=e||[],t=t||{};try{return new Blob(e,t)}catch(o){if("TypeError"!==o.name)throw o;for(var n=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),r=0;r<e.length;r+=1)n.append(e[r]);return n.getBlob(t.type)}}"undefined"==typeof Promise&&e(3);var u=Promise;function f(e,t){t&&e.then((function(e){t(null,e)}),(function(e){t(e)}))}function l(e,t,n){"function"==typeof t&&e.then(t),"function"==typeof n&&e.catch(n)}function d(e){return"string"!=typeof e&&(console.warn(e+" used as a key, but it is not a string."),e=String(e)),e}function h(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}var p="local-forage-detect-blob-support",v=void 0,m={},y=Object.prototype.toString,g="readonly",b="readwrite";function w(e){for(var t=e.length,n=new ArrayBuffer(t),r=new Uint8Array(n),o=0;o<t;o++)r[o]=e.charCodeAt(o);return n}function _(e){return new u((function(t){var n=e.transaction(p,b),r=c([""]);n.objectStore(p).put(r,"key"),n.onabort=function(e){e.preventDefault(),e.stopPropagation(),t(!1)},n.oncomplete=function(){var e=navigator.userAgent.match(/Chrome\/(\d+)/),n=navigator.userAgent.match(/Edge\//);t(n||!e||parseInt(e[1],10)>=43)}})).catch((function(){return!1}))}function S(e){return"boolean"==typeof v?u.resolve(v):_(e).then((function(e){return v=e}))}function E(e){var t=m[e.name],n={};n.promise=new u((function(e,t){n.resolve=e,n.reject=t})),t.deferredOperations.push(n),t.dbReady?t.dbReady=t.dbReady.then((function(){return n.promise})):t.dbReady=n.promise}function I(e){var t=m[e.name].deferredOperations.pop();if(t)return t.resolve(),t.promise}function N(e,t){var n=m[e.name].deferredOperations.pop();if(n)return n.reject(t),n.promise}function k(e,t){return new u((function(n,r){if(m[e.name]=m[e.name]||B(),e.db){if(!t)return n(e.db);E(e),e.db.close()}var o=[e.name];t&&o.push(e.version);var i=a.open.apply(a,o);t&&(i.onupgradeneeded=function(t){var n=i.result;try{n.createObjectStore(e.storeName),t.oldVersion<=1&&n.createObjectStore(p)}catch(n){if("ConstraintError"!==n.name)throw n;console.warn('The database "'+e.name+'" has been upgraded from version '+t.oldVersion+" to version "+t.newVersion+', but the storage "'+e.storeName+'" already exists.')}}),i.onerror=function(e){e.preventDefault(),r(i.error)},i.onsuccess=function(){var t=i.result;t.onversionchange=function(e){e.target.close()},n(t),I(e)}}))}function j(e){return k(e,!1)}function T(e){return k(e,!0)}function O(e,t){if(!e.db)return!0;var n=!e.db.objectStoreNames.contains(e.storeName),r=e.version<e.db.version,o=e.version>e.db.version;if(r&&(e.version!==t&&console.warn('The database "'+e.name+"\" can't be downgraded from version "+e.db.version+" to version "+e.version+"."),e.version=e.db.version),o||n){if(n){var i=e.db.version+1;i>e.version&&(e.version=i)}return!0}return!1}function C(e){return new u((function(t,n){var r=new FileReader;r.onerror=n,r.onloadend=function(n){var r=btoa(n.target.result||"");t({__local_forage_encoded_blob:!0,data:r,type:e.type})},r.readAsBinaryString(e)}))}function R(e){return c([w(atob(e.data))],{type:e.type})}function x(e){return e&&e.__local_forage_encoded_blob}function D(e){var t=this,n=t._initReady().then((function(){var e=m[t._dbInfo.name];if(e&&e.dbReady)return e.dbReady}));return l(n,e,e),n}function L(e){E(e);for(var t=m[e.name],n=t.forages,r=0;r<n.length;r++){var o=n[r];o._dbInfo.db&&(o._dbInfo.db.close(),o._dbInfo.db=null)}return e.db=null,j(e).then((function(t){return e.db=t,O(e)?T(e):t})).then((function(r){e.db=t.db=r;for(var o=0;o<n.length;o++)n[o]._dbInfo.db=r})).catch((function(t){throw N(e,t),t}))}function A(e,t,n,r){void 0===r&&(r=1);try{var o=e.db.transaction(e.storeName,t);n(null,o)}catch(o){if(r>0&&(!e.db||"InvalidStateError"===o.name||"NotFoundError"===o.name))return u.resolve().then((function(){if(!e.db||"NotFoundError"===o.name&&!e.db.objectStoreNames.contains(e.storeName)&&e.version<=e.db.version)return e.db&&(e.version=e.db.version+1),T(e)})).then((function(){return L(e).then((function(){A(e,t,n,r-1)}))})).catch(n);n(o)}}function B(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function F(e){var t=this,n={db:null};if(e)for(var r in e)n[r]=e[r];var o=m[n.name];o||(o=B(),m[n.name]=o),o.forages.push(t),t._initReady||(t._initReady=t.ready,t.ready=D);var i=[];function a(){return u.resolve()}for(var s=0;s<o.forages.length;s++){var c=o.forages[s];c!==t&&i.push(c._initReady().catch(a))}var f=o.forages.slice(0);return u.all(i).then((function(){return n.db=o.db,j(n)})).then((function(e){return n.db=e,O(n,t._defaultConfig.version)?T(n):e})).then((function(e){n.db=o.db=e,t._dbInfo=n;for(var r=0;r<f.length;r++){var i=f[r];i!==t&&(i._dbInfo.db=n.db,i._dbInfo.version=n.version)}}))}function P(e,t){var n=this;e=d(e);var r=new u((function(t,r){n.ready().then((function(){A(n._dbInfo,g,(function(o,i){if(o)return r(o);try{var a=i.objectStore(n._dbInfo.storeName).get(e);a.onsuccess=function(){var e=a.result;void 0===e&&(e=null),x(e)&&(e=R(e)),t(e)},a.onerror=function(){r(a.error)}}catch(e){r(e)}}))})).catch(r)}));return f(r,t),r}function $(e,t){var n=this,r=new u((function(t,r){n.ready().then((function(){A(n._dbInfo,g,(function(o,i){if(o)return r(o);try{var a=i.objectStore(n._dbInfo.storeName).openCursor(),s=1;a.onsuccess=function(){var n=a.result;if(n){var r=n.value;x(r)&&(r=R(r));var o=e(r,n.key,s++);void 0!==o?t(o):n.continue()}else t()},a.onerror=function(){r(a.error)}}catch(e){r(e)}}))})).catch(r)}));return f(r,t),r}function M(e,t,n){var r=this;e=d(e);var o=new u((function(n,o){var i;r.ready().then((function(){return i=r._dbInfo,"[object Blob]"===y.call(t)?S(i.db).then((function(e){return e?t:C(t)})):t})).then((function(t){A(r._dbInfo,b,(function(i,a){if(i)return o(i);try{var s=a.objectStore(r._dbInfo.storeName);null===t&&(t=void 0);var c=s.put(t,e);a.oncomplete=function(){void 0===t&&(t=null),n(t)},a.onabort=a.onerror=function(){var e=c.error?c.error:c.transaction.error;o(e)}}catch(e){o(e)}}))})).catch(o)}));return f(o,n),o}function q(e,t){var n=this;e=d(e);var r=new u((function(t,r){n.ready().then((function(){A(n._dbInfo,b,(function(o,i){if(o)return r(o);try{var a=i.objectStore(n._dbInfo.storeName).delete(e);i.oncomplete=function(){t()},i.onerror=function(){r(a.error)},i.onabort=function(){var e=a.error?a.error:a.transaction.error;r(e)}}catch(e){r(e)}}))})).catch(r)}));return f(r,t),r}function z(e){var t=this,n=new u((function(e,n){t.ready().then((function(){A(t._dbInfo,b,(function(r,o){if(r)return n(r);try{var i=o.objectStore(t._dbInfo.storeName).clear();o.oncomplete=function(){e()},o.onabort=o.onerror=function(){var e=i.error?i.error:i.transaction.error;n(e)}}catch(e){n(e)}}))})).catch(n)}));return f(n,e),n}function X(e){var t=this,n=new u((function(e,n){t.ready().then((function(){A(t._dbInfo,g,(function(r,o){if(r)return n(r);try{var i=o.objectStore(t._dbInfo.storeName).count();i.onsuccess=function(){e(i.result)},i.onerror=function(){n(i.error)}}catch(e){n(e)}}))})).catch(n)}));return f(n,e),n}function W(e,t){var n=this,r=new u((function(t,r){e<0?t(null):n.ready().then((function(){A(n._dbInfo,g,(function(o,i){if(o)return r(o);try{var a=i.objectStore(n._dbInfo.storeName),s=!1,c=a.openKeyCursor();c.onsuccess=function(){var n=c.result;n?0===e||s?t(n.key):(s=!0,n.advance(e)):t(null)},c.onerror=function(){r(c.error)}}catch(e){r(e)}}))})).catch(r)}));return f(r,t),r}function U(e){var t=this,n=new u((function(e,n){t.ready().then((function(){A(t._dbInfo,g,(function(r,o){if(r)return n(r);try{var i=o.objectStore(t._dbInfo.storeName).openKeyCursor(),a=[];i.onsuccess=function(){var t=i.result;t?(a.push(t.key),t.continue()):e(a)},i.onerror=function(){n(i.error)}}catch(e){n(e)}}))})).catch(n)}));return f(n,e),n}function J(e,t){t=h.apply(this,arguments);var n=this.config();(e="function"!=typeof e&&e||{}).name||(e.name=e.name||n.name,e.storeName=e.storeName||n.storeName);var r,o=this;if(e.name){var i=e.name===n.name&&o._dbInfo.db?u.resolve(o._dbInfo.db):j(e).then((function(t){var n=m[e.name],r=n.forages;n.db=t;for(var o=0;o<r.length;o++)r[o]._dbInfo.db=t;return t}));r=e.storeName?i.then((function(t){if(t.objectStoreNames.contains(e.storeName)){var n=t.version+1;E(e);var r=m[e.name],o=r.forages;t.close();for(var i=0;i<o.length;i++){var s=o[i];s._dbInfo.db=null,s._dbInfo.version=n}var c=new u((function(t,r){var o=a.open(e.name,n);o.onerror=function(e){o.result.close(),r(e)},o.onupgradeneeded=function(){o.result.deleteObjectStore(e.storeName)},o.onsuccess=function(){var e=o.result;e.close(),t(e)}}));return c.then((function(e){r.db=e;for(var t=0;t<o.length;t++){var n=o[t];n._dbInfo.db=e,I(n._dbInfo)}})).catch((function(t){throw(N(e,t)||u.resolve()).catch((function(){})),t}))}})):i.then((function(t){E(e);var n=m[e.name],r=n.forages;t.close();for(var o=0;o<r.length;o++)r[o]._dbInfo.db=null;var i=new u((function(t,n){var r=a.deleteDatabase(e.name);r.onerror=function(){var e=r.result;e&&e.close(),n(r.error)},r.onblocked=function(){console.warn('dropInstance blocked for database "'+e.name+'" until all open connections are closed')},r.onsuccess=function(){var e=r.result;e&&e.close(),t(e)}}));return i.then((function(e){n.db=e;for(var t=0;t<r.length;t++)I(r[t]._dbInfo)})).catch((function(t){throw(N(e,t)||u.resolve()).catch((function(){})),t}))}))}else r=u.reject("Invalid arguments");return f(r,t),r}var H={_driver:"asyncStorage",_initStorage:F,_support:s(),iterate:$,getItem:P,setItem:M,removeItem:q,clear:z,length:X,key:W,keys:U,dropInstance:J};function V(){return"function"==typeof openDatabase}var K="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Q="~~local_forage_type~",G=/^~~local_forage_type~([^~]+)~/,Y="__lfsc__:",Z=Y.length,ee="arbf",te="blob",ne="si08",re="ui08",oe="uic8",ie="si16",ae="si32",se="ur16",ce="ui32",ue="fl32",fe="fl64",le=Z+ee.length,de=Object.prototype.toString;function he(e){var t,n,r,o,i,a=.75*e.length,s=e.length,c=0;"="===e[e.length-1]&&(a--,"="===e[e.length-2]&&a--);var u=new ArrayBuffer(a),f=new Uint8Array(u);for(t=0;t<s;t+=4)n=K.indexOf(e[t]),r=K.indexOf(e[t+1]),o=K.indexOf(e[t+2]),i=K.indexOf(e[t+3]),f[c++]=n<<2|r>>4,f[c++]=(15&r)<<4|o>>2,f[c++]=(3&o)<<6|63&i;return u}function pe(e){var t,n=new Uint8Array(e),r="";for(t=0;t<n.length;t+=3)r+=K[n[t]>>2],r+=K[(3&n[t])<<4|n[t+1]>>4],r+=K[(15&n[t+1])<<2|n[t+2]>>6],r+=K[63&n[t+2]];return n.length%3==2?r=r.substring(0,r.length-1)+"=":n.length%3==1&&(r=r.substring(0,r.length-2)+"=="),r}function ve(e,t){var n="";if(e&&(n=de.call(e)),e&&("[object ArrayBuffer]"===n||e.buffer&&"[object ArrayBuffer]"===de.call(e.buffer))){var r,o=Y;e instanceof ArrayBuffer?(r=e,o+=ee):(r=e.buffer,"[object Int8Array]"===n?o+=ne:"[object Uint8Array]"===n?o+=re:"[object Uint8ClampedArray]"===n?o+=oe:"[object Int16Array]"===n?o+=ie:"[object Uint16Array]"===n?o+=se:"[object Int32Array]"===n?o+=ae:"[object Uint32Array]"===n?o+=ce:"[object Float32Array]"===n?o+=ue:"[object Float64Array]"===n?o+=fe:t(new Error("Failed to get type for BinaryArray"))),t(o+pe(r))}else if("[object Blob]"===n){var i=new FileReader;i.onload=function(){var n=Q+e.type+"~"+pe(this.result);t(Y+te+n)},i.readAsArrayBuffer(e)}else try{t(JSON.stringify(e))}catch(n){console.error("Couldn't convert value into a JSON string: ",e),t(null,n)}}function me(e){if(e.substring(0,Z)!==Y)return JSON.parse(e);var t,n=e.substring(le),r=e.substring(Z,le);if(r===te&&G.test(n)){var o=n.match(G);t=o[1],n=n.substring(o[0].length)}var i=he(n);switch(r){case ee:return i;case te:return c([i],{type:t});case ne:return new Int8Array(i);case re:return new Uint8Array(i);case oe:return new Uint8ClampedArray(i);case ie:return new Int16Array(i);case se:return new Uint16Array(i);case ae:return new Int32Array(i);case ce:return new Uint32Array(i);case ue:return new Float32Array(i);case fe:return new Float64Array(i);default:throw new Error("Unkown type: "+r)}}var ye={serialize:ve,deserialize:me,stringToBuffer:he,bufferToString:pe};function ge(e,t,n,r){e.executeSql("CREATE TABLE IF NOT EXISTS "+t.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],n,r)}function be(e){var t=this,n={db:null};if(e)for(var r in e)n[r]="string"!=typeof e[r]?e[r].toString():e[r];var o=new u((function(e,r){try{n.db=openDatabase(n.name,String(n.version),n.description,n.size)}catch(e){return r(e)}n.db.transaction((function(o){ge(o,n,(function(){t._dbInfo=n,e()}),(function(e,t){r(t)}))}),r)}));return n.serializer=ye,o}function we(e,t,n,r,o,i){e.executeSql(n,r,o,(function(e,a){a.code===a.SYNTAX_ERR?e.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[t.storeName],(function(e,s){s.rows.length?i(e,a):ge(e,t,(function(){e.executeSql(n,r,o,i)}),i)}),i):i(e,a)}),i)}function _e(e,t){var n=this;e=d(e);var r=new u((function(t,r){n.ready().then((function(){var o=n._dbInfo;o.db.transaction((function(n){we(n,o,"SELECT * FROM "+o.storeName+" WHERE key = ? LIMIT 1",[e],(function(e,n){var r=n.rows.length?n.rows.item(0).value:null;r&&(r=o.serializer.deserialize(r)),t(r)}),(function(e,t){r(t)}))}))})).catch(r)}));return f(r,t),r}function Se(e,t){var n=this,r=new u((function(t,r){n.ready().then((function(){var o=n._dbInfo;o.db.transaction((function(n){we(n,o,"SELECT * FROM "+o.storeName,[],(function(n,r){for(var i=r.rows,a=i.length,s=0;s<a;s++){var c=i.item(s),u=c.value;if(u&&(u=o.serializer.deserialize(u)),void 0!==(u=e(u,c.key,s+1)))return void t(u)}t()}),(function(e,t){r(t)}))}))})).catch(r)}));return f(r,t),r}function Ee(e,t,n,r){var o=this;e=d(e);var i=new u((function(i,a){o.ready().then((function(){void 0===t&&(t=null);var s=t,c=o._dbInfo;c.serializer.serialize(t,(function(t,u){u?a(u):c.db.transaction((function(n){we(n,c,"INSERT OR REPLACE INTO "+c.storeName+" (key, value) VALUES (?, ?)",[e,t],(function(){i(s)}),(function(e,t){a(t)}))}),(function(t){if(t.code===t.QUOTA_ERR){if(r>0)return void i(Ee.apply(o,[e,s,n,r-1]));a(t)}}))}))})).catch(a)}));return f(i,n),i}function Ie(e,t,n){return Ee.apply(this,[e,t,n,1])}function Ne(e,t){var n=this;e=d(e);var r=new u((function(t,r){n.ready().then((function(){var o=n._dbInfo;o.db.transaction((function(n){we(n,o,"DELETE FROM "+o.storeName+" WHERE key = ?",[e],(function(){t()}),(function(e,t){r(t)}))}))})).catch(r)}));return f(r,t),r}function ke(e){var t=this,n=new u((function(e,n){t.ready().then((function(){var r=t._dbInfo;r.db.transaction((function(t){we(t,r,"DELETE FROM "+r.storeName,[],(function(){e()}),(function(e,t){n(t)}))}))})).catch(n)}));return f(n,e),n}function je(e){var t=this,n=new u((function(e,n){t.ready().then((function(){var r=t._dbInfo;r.db.transaction((function(t){we(t,r,"SELECT COUNT(key) as c FROM "+r.storeName,[],(function(t,n){var r=n.rows.item(0).c;e(r)}),(function(e,t){n(t)}))}))})).catch(n)}));return f(n,e),n}function Te(e,t){var n=this,r=new u((function(t,r){n.ready().then((function(){var o=n._dbInfo;o.db.transaction((function(n){we(n,o,"SELECT key FROM "+o.storeName+" WHERE id = ? LIMIT 1",[e+1],(function(e,n){var r=n.rows.length?n.rows.item(0).key:null;t(r)}),(function(e,t){r(t)}))}))})).catch(r)}));return f(r,t),r}function Oe(e){var t=this,n=new u((function(e,n){t.ready().then((function(){var r=t._dbInfo;r.db.transaction((function(t){we(t,r,"SELECT key FROM "+r.storeName,[],(function(t,n){for(var r=[],o=0;o<n.rows.length;o++)r.push(n.rows.item(o).key);e(r)}),(function(e,t){n(t)}))}))})).catch(n)}));return f(n,e),n}function Ce(e){return new u((function(t,n){e.transaction((function(r){r.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],(function(n,r){for(var o=[],i=0;i<r.rows.length;i++)o.push(r.rows.item(i).name);t({db:e,storeNames:o})}),(function(e,t){n(t)}))}),(function(e){n(e)}))}))}function Re(e,t){t=h.apply(this,arguments);var n=this.config();(e="function"!=typeof e&&e||{}).name||(e.name=e.name||n.name,e.storeName=e.storeName||n.storeName);var r,o=this;return f(r=e.name?new u((function(t){var r;r=e.name===n.name?o._dbInfo.db:openDatabase(e.name,"","",0),e.storeName?t({db:r,storeNames:[e.storeName]}):t(Ce(r))})).then((function(e){return new u((function(t,n){e.db.transaction((function(r){function o(e){return new u((function(t,n){r.executeSql("DROP TABLE IF EXISTS "+e,[],(function(){t()}),(function(e,t){n(t)}))}))}for(var i=[],a=0,s=e.storeNames.length;a<s;a++)i.push(o(e.storeNames[a]));u.all(i).then((function(){t()})).catch((function(e){n(e)}))}),(function(e){n(e)}))}))})):u.reject("Invalid arguments"),t),r}var xe={_driver:"webSQLStorage",_initStorage:be,_support:V(),iterate:Se,getItem:_e,setItem:Ie,removeItem:Ne,clear:ke,length:je,key:Te,keys:Oe,dropInstance:Re};function De(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(e){return!1}}function Le(e,t){var n=e.name+"/";return e.storeName!==t.storeName&&(n+=e.storeName+"/"),n}function Ae(){var e="_localforage_support_test";try{return localStorage.setItem(e,!0),localStorage.removeItem(e),!1}catch(e){return!0}}function Be(){return!Ae()||localStorage.length>0}function Fe(e){var t=this,n={};if(e)for(var r in e)n[r]=e[r];return n.keyPrefix=Le(e,t._defaultConfig),Be()?(t._dbInfo=n,n.serializer=ye,u.resolve()):u.reject()}function Pe(e){var t=this,n=t.ready().then((function(){for(var e=t._dbInfo.keyPrefix,n=localStorage.length-1;n>=0;n--){var r=localStorage.key(n);0===r.indexOf(e)&&localStorage.removeItem(r)}}));return f(n,e),n}function $e(e,t){var n=this;e=d(e);var r=n.ready().then((function(){var t=n._dbInfo,r=localStorage.getItem(t.keyPrefix+e);return r&&(r=t.serializer.deserialize(r)),r}));return f(r,t),r}function Me(e,t){var n=this,r=n.ready().then((function(){for(var t=n._dbInfo,r=t.keyPrefix,o=r.length,i=localStorage.length,a=1,s=0;s<i;s++){var c=localStorage.key(s);if(0===c.indexOf(r)){var u=localStorage.getItem(c);if(u&&(u=t.serializer.deserialize(u)),void 0!==(u=e(u,c.substring(o),a++)))return u}}}));return f(r,t),r}function qe(e,t){var n=this,r=n.ready().then((function(){var t,r=n._dbInfo;try{t=localStorage.key(e)}catch(e){t=null}return t&&(t=t.substring(r.keyPrefix.length)),t}));return f(r,t),r}function ze(e){var t=this,n=t.ready().then((function(){for(var e=t._dbInfo,n=localStorage.length,r=[],o=0;o<n;o++){var i=localStorage.key(o);0===i.indexOf(e.keyPrefix)&&r.push(i.substring(e.keyPrefix.length))}return r}));return f(n,e),n}function Xe(e){var t=this.keys().then((function(e){return e.length}));return f(t,e),t}function We(e,t){var n=this;e=d(e);var r=n.ready().then((function(){var t=n._dbInfo;localStorage.removeItem(t.keyPrefix+e)}));return f(r,t),r}function Ue(e,t,n){var r=this;e=d(e);var o=r.ready().then((function(){void 0===t&&(t=null);var n=t;return new u((function(o,i){var a=r._dbInfo;a.serializer.serialize(t,(function(t,r){if(r)i(r);else try{localStorage.setItem(a.keyPrefix+e,t),o(n)}catch(e){"QuotaExceededError"!==e.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==e.name||i(e),i(e)}}))}))}));return f(o,n),o}function Je(e,t){if(t=h.apply(this,arguments),!(e="function"!=typeof e&&e||{}).name){var n=this.config();e.name=e.name||n.name,e.storeName=e.storeName||n.storeName}var r,o=this;return r=e.name?new u((function(t){e.storeName?t(Le(e,o._defaultConfig)):t(e.name+"/")})).then((function(e){for(var t=localStorage.length-1;t>=0;t--){var n=localStorage.key(t);0===n.indexOf(e)&&localStorage.removeItem(n)}})):u.reject("Invalid arguments"),f(r,t),r}var He={_driver:"localStorageWrapper",_initStorage:Fe,_support:De(),iterate:Me,getItem:$e,setItem:Ue,removeItem:We,clear:Pe,length:Xe,key:qe,keys:ze,dropInstance:Je},Ve=function(e,t){return e===t||"number"==typeof e&&"number"==typeof t&&isNaN(e)&&isNaN(t)},Ke=function(e,t){for(var n=e.length,r=0;r<n;){if(Ve(e[r],t))return!0;r++}return!1},Qe=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},Ge={},Ye={},Ze={INDEXEDDB:H,WEBSQL:xe,LOCALSTORAGE:He},et=[Ze.INDEXEDDB._driver,Ze.WEBSQL._driver,Ze.LOCALSTORAGE._driver],tt=["dropInstance"],nt=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(tt),rt={description:"",driver:et.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function ot(e,t){e[t]=function(){var n=arguments;return e.ready().then((function(){return e[t].apply(e,n)}))}}function it(){for(var e=1;e<arguments.length;e++){var t=arguments[e];if(t)for(var n in t)t.hasOwnProperty(n)&&(Qe(t[n])?arguments[0][n]=t[n].slice():arguments[0][n]=t[n])}return arguments[0]}var at=function(){function e(t){for(var n in o(this,e),Ze)if(Ze.hasOwnProperty(n)){var r=Ze[n],i=r._driver;this[n]=i,Ge[i]||this.defineDriver(r)}this._defaultConfig=it({},rt),this._config=it({},this._defaultConfig,t),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch((function(){}))}return e.prototype.config=function(e){if("object"===(void 0===e?"undefined":r(e))){if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var t in e){if("storeName"===t&&(e[t]=e[t].replace(/\W/g,"_")),"version"===t&&"number"!=typeof e[t])return new Error("Database version must be a number.");this._config[t]=e[t]}return!("driver"in e)||!e.driver||this.setDriver(this._config.driver)}return"string"==typeof e?this._config[e]:this._config},e.prototype.defineDriver=function(e,t,n){var r=new u((function(t,n){try{var r=e._driver,o=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(!e._driver)return void n(o);for(var i=nt.concat("_initStorage"),a=0,s=i.length;a<s;a++){var c=i[a];if((!Ke(tt,c)||e[c])&&"function"!=typeof e[c])return void n(o)}var l=function(){for(var t=function(e){return function(){var t=new Error("Method "+e+" is not implemented by the current driver"),n=u.reject(t);return f(n,arguments[arguments.length-1]),n}},n=0,r=tt.length;n<r;n++){var o=tt[n];e[o]||(e[o]=t(o))}};l();var d=function(n){Ge[r]&&console.info("Redefining LocalForage driver: "+r),Ge[r]=e,Ye[r]=n,t()};"_support"in e?e._support&&"function"==typeof e._support?e._support().then(d,n):d(!!e._support):d(!0)}catch(e){n(e)}}));return l(r,t,n),r},e.prototype.driver=function(){return this._driver||null},e.prototype.getDriver=function(e,t,n){var r=Ge[e]?u.resolve(Ge[e]):u.reject(new Error("Driver not found."));return l(r,t,n),r},e.prototype.getSerializer=function(e){var t=u.resolve(ye);return l(t,e),t},e.prototype.ready=function(e){var t=this,n=t._driverSet.then((function(){return null===t._ready&&(t._ready=t._initDriver()),t._ready}));return l(n,e,e),n},e.prototype.setDriver=function(e,t,n){var r=this;Qe(e)||(e=[e]);var o=this._getSupportedDrivers(e);function i(){r._config.driver=r.driver()}function a(e){return r._extend(e),i(),r._ready=r._initStorage(r._config),r._ready}function s(e){return function(){var t=0;function n(){for(;t<e.length;){var o=e[t];return t++,r._dbInfo=null,r._ready=null,r.getDriver(o).then(a).catch(n)}i();var s=new Error("No available storage method found.");return r._driverSet=u.reject(s),r._driverSet}return n()}}var c=null!==this._driverSet?this._driverSet.catch((function(){return u.resolve()})):u.resolve();return this._driverSet=c.then((function(){var e=o[0];return r._dbInfo=null,r._ready=null,r.getDriver(e).then((function(e){r._driver=e._driver,i(),r._wrapLibraryMethodsWithReady(),r._initDriver=s(o)}))})).catch((function(){i();var e=new Error("No available storage method found.");return r._driverSet=u.reject(e),r._driverSet})),l(this._driverSet,t,n),this._driverSet},e.prototype.supports=function(e){return!!Ye[e]},e.prototype._extend=function(e){it(this,e)},e.prototype._getSupportedDrivers=function(e){for(var t=[],n=0,r=e.length;n<r;n++){var o=e[n];this.supports(o)&&t.push(o)}return t},e.prototype._wrapLibraryMethodsWithReady=function(){for(var e=0,t=nt.length;e<t;e++)ot(this,nt[e])},e.prototype.createInstance=function(t){return new e(t)},e}(),st=new at;t.exports=st},{3:3}]},{},[4])(4)}({get exports(){return r},set exports(e){r=e}});var o=r;function i(e,t,n,r){return new(n||(n=Promise))((function(o,i){function a(e){try{c(r.next(e))}catch(e){i(e)}}function s(e){try{c(r.throw(e))}catch(e){i(e)}}function c(e){var t;e.done?o(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(a,s)}c((r=r.apply(e,t||[])).next())}))}"function"==typeof SuppressedError&&SuppressedError;let a=(e=21)=>{let t="",n=e;for(;n--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[64*Math.random()|0];return t};var s,c;!function(e){e[e.JsError=1e3]="JsError",e[e.PromiseError=1001]="PromiseError",e[e.ResourceError=1002]="ResourceError",e[e.eventCatch=1003]="eventCatch",e[e.RefreshOrLeave=1004]="RefreshOrLeave",e[e.VueError=1005]="VueError",e[e.xhrCatch=1006]="xhrCatch",e[e.ConsoleCatch=1007]="ConsoleCatch",e[e.Performance=1008]="Performance",e[e.FPS=1009]="FPS",e[e.NetWorkSpeed=1010]="NetWorkSpeed",e[e.unloadCatch=1011]="unloadCatch"}(s||(s={})),function(e){e.warn="warn",e.error="error",e.info="info",e.debug="debug"}(c||(c={}));let u,f=!0;class l{constructor(e){this.timer=null,this.idleCallbackId=null,this.traceId=a(32),this.monitorExpendObj={},this.url="https://192.168.130.41:9366/mtweb/data/log/log",this.url=e.url||this.url,this.baseObj={appName:e.appName,userName:e.userName||"",version:e.version||""},this.isRunning=!1,this.failureCount=0,this.maxRetries=5,this.currentXhr=null,this.isDestroyed=!1,this.instanceId=a(16),this.unloadHandler=null,window.requestIdleCallback=window.requestIdleCallback||function(e){let t=Date.now();return setTimeout((function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})}),1)},this.sendBeacon(),this.postStart()}destroy(){this.isDestroyed||(this.isDestroyed=!0,this.isRunning=!1,this.timer&&(window.clearTimeout(this.timer),this.timer=null),this.idleCallbackId&&window.cancelIdleCallback&&(window.cancelIdleCallback(this.idleCallbackId),this.idleCallbackId=null),this.currentXhr&&(this.currentXhr.abort(),this.currentXhr=null),this.unloadHandler&&(window.removeEventListener("unload",this.unloadHandler),this.unloadHandler=null),console.log(`ReportTracker实例${this.instanceId}已销毁`))}sendBeacon(){if(this.unloadHandler)return;if(!Object.keys(this.monitorExpendObj).length){let e=sessionStorage.getItem("monitorExpendObj");this.monitorExpendObj=e?JSON.parse(e):{}}let e=[Object.assign(Object.assign(Object.assign({code:s.unloadCatch,type:c.info},this.monitorExpendObj),{message:"用户已关闭当前窗口",timestamp:Date.now(),traceId:this.traceId}),this.baseObj)];this.unloadHandler=()=>{navigator.sendBeacon&&!this.isDestroyed&&navigator.sendBeacon(this.url,JSON.stringify({msg:e}))},window.addEventListener("unload",this.unloadHandler)}postStart(){if(this.isDestroyed||this.isRunning)return;if(this.failureCount>=this.maxRetries)return void console.warn(`ReportTracker实例${this.instanceId}：失败次数过多，停止重试`);const e=this.failureCount>0?Math.min(5e3*Math.pow(2,this.failureCount-1),3e4):5e3;this.timer=window.setTimeout((()=>{this.isDestroyed||(this.idleCallbackId=window.requestIdleCallback((e=>{this.isDestroyed||this.postTracker()}),{timeout:5e3}))}),e)}ajaxSend(e=[],t=[]){if(this.isDestroyed||this.isRunning)return;this.isRunning=!0,this.currentXhr&&this.currentXhr.abort();let n=new XMLHttpRequest;this.currentXhr=n,n.timeout=1e4,n.open("post",this.url),n.setRequestHeader("Content-Type","application/json"),n.onload=()=>{if(!this.isDestroyed)try{let e=JSON.parse(n.response);t.forEach((e=>{o.removeItem(e)})),this.isRunning=!1,this.currentXhr=null,-1===e.rate?(f=!1,console.warn(`ReportTracker实例${this.instanceId}：服务器拒绝接收日志`)):(this.failureCount=0,this.postStart())}catch(e){console.error(`ReportTracker实例${this.instanceId}：响应解析失败`,e),this.handleError()}},n.onerror=e=>{this.isDestroyed||(console.error(`ReportTracker实例${this.instanceId}：网络请求失败`,e),this.handleError())},n.ontimeout=()=>{this.isDestroyed||(console.warn(`ReportTracker实例${this.instanceId}：请求超时`),this.handleError())};try{n.send(JSON.stringify({msg:e}))}catch(e){console.error(`ReportTracker实例${this.instanceId}：发送请求失败`,e),this.handleError()}}handleError(){this.isDestroyed||(this.isRunning=!1,this.currentXhr=null,this.failureCount++,console.warn(`ReportTracker实例${this.instanceId}：第${this.failureCount}次失败`),this.timer&&(window.clearTimeout(this.timer),this.timer=null),this.postStart())}postTracker(){return i(this,void 0,void 0,(function*(){if(!this.isDestroyed&&!this.isRunning)try{let e=[],t=[],n=0;const r=50;yield o.iterate(((o,i)=>{if(n>=r)return!1;e.push(Object.assign(Object.assign({},o),{traceId:this.traceId})),t.push(i),n++})),e.length?(console.log(`ReportTracker实例${this.instanceId}：准备上报${e.length}条日志`),this.ajaxSend(e,t)):(this.isRunning=!1,this.timer&&(window.clearTimeout(this.timer),this.timer=null),this.postStart())}catch(e){console.error(`ReportTracker实例${this.instanceId}：日志处理失败`,e),this.handleError()}}))}}class d{constructor(e={appName:""}){this.baseObj={appName:e.appName,userName:e.userName||"",version:e.version||""},this.monitorExpendObj={}}SendTracker(e={}){let t=this.getExtraData(),n=this.handleTrackerItem(e),r=Object.assign(Object.assign(Object.assign({},t),n),this.baseObj);console.log(r,f,"日志log"),f&&this.storageTracker(r)}storageTracker(e){o.setItem((new Date).getTime().toString()+Math.random(),e)}handleTrackerItem(e={}){let t=[];for(let n in e)if(!["code","type"].includes(n)&&e[n]){let r="string"==typeof e[n]?e[n]:JSON.stringify(e[n]);t.push(`${n}:${r}`)}return{code:e.code,type:e.type,message:t.join("|")}}getExtraData(){if(!Object.keys(this.monitorExpendObj).length){let e=sessionStorage.getItem("monitorExpendObj");this.monitorExpendObj=e?JSON.parse(e):{}}return Object.assign({logVer:"1.0.193",timestamp:Date.now(),network:this.handleNetWork()},this.monitorExpendObj)}handleNetWork(){let e=navigator.connection;if(e){let{downlink:t,effectiveType:n,onchange:r,rtt:o}=e;return`downlink:${t};effectiveType:${n},onchange:${r},rtt:${o}`}return""}}function h(e){return e.reverse().filter((e=>e!==document&&e!==window)).map((e=>{let t="";return e.id?`${e.nodeName.toLowerCase()}#${e.id}`:e.className&&"string"==typeof e.className?`${e.nodeName.toLowerCase()}.${e.className}`:(t=e.nodeName.toLowerCase(),t)})).join(" ")}function p(e){if(Array.isArray(e))return h(e);{let t=[];for(;e;)t.push(e),e=e.parentNode;return h(t)}}const v=(e,t=4)=>{try{return parseFloat(e.toFixed(t))}catch(t){return e}},m=e=>e.split("\n").slice(1).map((e=>e.replace(/^\s+at\s+/g,""))).join("^");var y=Object.freeze({__proto__:null,jsError:class extends d{constructor(e){super(e),this.eventCatch=e.eventCatch,this.handleError()}demos(){return this.handleError()}handleError(){window.addEventListener("error",this.addJsErrorListerner.bind(this),!0)}addJsErrorListerner(e){let t;if(!1!==this.eventCatch&&(t=u),e.target&&(e.target.src||e.target.href)){let t={code:s.ResourceError,type:c.error,filename:e.target.src||e.target.href,tagName:e.target.tagName,selector:p(e.target)};this.SendTracker(t)}else{let n={code:s.JsError,type:c.error,message:e.message,filename:e.filename,position:`${e.lineno}:${e.colno}`,stack:m(e.error.stack),selector:t?p(t.path):""};this.SendTracker(n)}}},promiseError:class extends d{constructor(e){super(e),this.eventCatch=e.eventCatch,this.handleError()}handleError(){window.addEventListener("unhandledrejection",(e=>{let t;!1!==this.eventCatch&&(t=u);let n,r,o=0,i=0,a="",f=e.reason;if("string"==typeof f)n=f;else if("object"==typeof f){if(n=f.message,f.stack){let e=f.stack.match(/at\s+(.+):(\d+):(\d+)/);r=e[1],o=e[2],i=e[3]}a=m(f.stack)}let l={code:s.PromiseError,type:c.error,message:n,filename:r,position:`${o}:${i}`,stack:a,selector:t?p(t.path):""};this.SendTracker(l)}),!0)}},eventCatch:class extends d{constructor(e){super(e),this.handleEvent()}handleEvent(){document.addEventListener("click",(e=>{u=e;let t={code:s.eventCatch,type:c.info,nodeName:e.target.nodeName,innerText:e.target.innerText,selector:p(e.path)};this.SendTracker(t)}),{capture:!0,passive:!0})}},performanceNavigation:class extends d{constructor(e){var t;(super(e),this.isPerformanceSupported=()=>!!window.performance&&!!window.performance.getEntriesByType&&!!window.performance.mark,this.isPerformanceSupported())?(t=this.getPerformance.bind(this),"complete"===document.readyState?t():window.addEventListener("load",t)):console.warn("Your browser does not suppport performance api.")}resolveNavigationTiming(e){const{fetchStart:t,domainLookupStart:n,domainLookupEnd:r,connectStart:o,connectEnd:i,secureConnectionStart:a,requestStart:u,responseStart:f,responseEnd:l,domInteractive:d,domContentLoadedEventStart:h,domContentLoadedEventEnd:p,loadEventStart:m}=e;let y={code:s.Performance,type:c.info,dnsLookup:v(r-n),initialConnection:v(i-o),ssl:a?v(i-a):0,ttfb:v(f-u),contentDownload:v(l-f),domParse:v(d-l),deferExecuteDuration:v(h-d),domContentLoadedCallback:v(p-h),resourceLoad:v(m-p),domReady:v(p-t),pageLoad:v(m-t)};this.SendTracker(y)}getPerformance(){var e;if(window.PerformanceObserver&&(null===(e=PerformanceObserver.supportedEntryTypes)||void 0===e?void 0:e.includes("navigation"))){const e=((e,t)=>{var n;try{if(null===(n=PerformanceObserver.supportedEntryTypes)||void 0===n?void 0:n.includes(e)){const n=new PerformanceObserver((e=>{t(e.getEntries()[0])}));return n.observe({type:e,buffered:!0}),n}}catch(e){throw e}})("navigation",(t=>{"navigation"===t.entryType&&(e&&e.disconnect(),this.resolveNavigationTiming(t))}))}else{const e=performance.getEntriesByType("navigation").length>0?performance.getEntriesByType("navigation")[0]:performance.timing;this.resolveNavigationTiming(e)}}},getFps:class extends d{constructor(e){super(e),window.requestAnimationFrame||console.warn("Your browser does not suppport requestAnimationFrame api.")}getFpsFun(){let e=0,t=0,n=performance.now(),r=0,o=performance.now();const i=()=>{r+=1;const a=performance.now(),u=a-o;if(o=a,e+=u||0,e<2e3)return void window.requestAnimationFrame(i);e=0;let f=0;if(f=Math.round(1e3/u),a>1e3+n&&(f=Math.round(1e3*r/(a-n)),r=0,n=a),f<20){if(t+=1,t>=3){let e={code:s.FPS,type:c.info,fps:f,message:`连续${t}次FPS低于20，当前FPS为${f}`};this.SendTracker(e),console.warn("网页卡顿",`连续${t}次FPS低于20，当前FPS为${f}`)}}else t=0;window.requestAnimationFrame(i)};i()}},refreshOrLeave:class extends d{constructor(e){super(e),this.handleRefreshOrLeave()}handleRefreshOrLeave(){window.addEventListener("beforeunload",(e=>{let t={code:s.RefreshOrLeave,type:c.info,types:e.type};this.SendTracker(t)}))}},consoleCatch:class extends d{constructor(e){super(e),this.consoleTypeList=e.consoleTypeList||["error"],this.handleConsole()}handleConsole(){window&&window.console&&this.consoleTypeList.forEach((e=>{const t=window.console[e];window.console[e]=(...n)=>{const r=Array.from(n),o={code:s.ConsoleCatch,type:c.info,consoleType:e,message:JSON.stringify(r)};return this.SendTracker(o),"function"==typeof t&&t.call(null,...n)}}))}},xhrCatch:class extends d{constructor(e){super(e),this.ignoreXhr=e.ignoreXhr?e.ignoreXhr:[],this.handleXhrCatch()}handleXhrCatch(){const e=this;let t=window.XMLHttpRequest,n=t.prototype.open;t.prototype.open=function(t,r,o){let i=r;if(r.includes("http")||r.includes("https")){const e=new URL(r);i=e.pathname}else i=r;return i.match(/logcollect/)||i.match(/sockjs/)||e.ignoreXhr.some((e=>i.includes(e)))||(this.logData={method:t,url:r,async:o}),n.apply(this,arguments)};let r=t.prototype.send;t.prototype.send=function(t){if(this.logData){let n=Date.now(),r=r=>o=>{let i=Date.now()-n,a=this.status,u=this.statusText,f={code:s.xhrCatch,type:"load"===r?c.info:c.error,eventType:r,pathname:this.logData.url,status:a+"-"+u,duration:i,response:this.response?this.response:"",params:t||""};e.SendTracker(f)};this.addEventListener("load",r("load"),!1),this.addEventListener("error",r("error"),!1),this.addEventListener("abort",r("abort"),!1)}return r.apply(this,arguments)}}}});const g={install(e,t){e&&e.config?["注安","微信","银杏大模型"].includes(t.appName)?e.config.errorHandler=function(n,r,o){let i=null==e?void 0:e.version,a=2===Number(i.split(".")[0])?function(e){let t="";if(e.$root===e)t="root";else{const n=e._isVue?e.$options&&e.$options.name||e.$options&&e.$options._componentTag:e.name;t=(n?"component <"+n+">":"anonymous component")+(e._isVue&&e.$options&&e.$options.__file?" at "+(e.$options&&e.$options.__file):"")}return{componentName:t,propsData:e.$options&&e.$options.propsData}}(r):function(e){let t="";if(e.$root===e)t="root";else{const n=e.$options&&e.$options.name;t=n?"component <"+n+">":"anonymous component"}return{componentName:t,propsData:e.$props}}(r);const u=Object.assign({code:s.VueError,type:c.error,Vueversion:i,message:`${n.message}(${o})`,errorName:n.name,stack:n.stack?m(n.stack):""},a);new d(t).SendTracker(u)}:console.error("appName Should be 银杏大模型 注安 微信"):console.error("Vue is not defined!")}};e.MitoVue=g,e.WebMonitor=class extends d{constructor(e){if(super(e),["注安","微信","银杏大模型"].includes(e.appName)){this.setLocalforage();for(let t in y)!1!==e[t]&&new y[t](e);new l(e)}else console.error("appName Should be '注安', '微信', '银杏大模型'")}setLocalforage(){const e=[o.INDEXEDDB,o.WEBSQL,o.LOCALSTORAGE];o.config({name:"webMonitor",storeName:"webMonitorStore"}),o.setDriver(e)}init(e){sessionStorage.setItem("monitorExpendObj",JSON.stringify(e))}info(e={}){e.type=c.info,e.code=e.code?e.code:2e3,this.SendTracker(e)}debug(e={}){e.type=c.debug,e.code=e.code?e.code:2e3,this.SendTracker(e)}warn(e={}){e.type=c.warn,e.code=e.code?e.code:2e3,this.SendTracker(e)}error(e={}){e.type=c.error,e.code=e.code?e.code:2e3,this.SendTracker(e)}},Object.defineProperty(e,"__esModule",{value:!0})}));
