/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-05 10:58:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-23 11:44:51
 * @FilePath: src/views/mobile-questions-answer-view/index.module.scss
 * @Version: 1.0.0
 * @Description: 组件描述
 */
.mobile-questions-answer-view {
  // position: absolute;
  // top: 0;
  // bottom: 0;
  // left: 0;
  // right: 0;
  pointer-events: none;
  // 上阴影
  box-shadow: 0px 2px 16px 0px rgba(0, 0, 0, 0.08);

  .text-input-wrapper {
    width: 100%;
    pointer-events: auto;
    background-color: #FFFFFF;
    // box-shadow: 0px -2px 16px 0px rgba(0, 0, 0, 0.08);
    // 添加安全区域支持，避免被软键盘覆盖
    bottom: env(safe-area-inset-bottom, 0px);
    // 为软键盘预留空间
    padding-bottom: env(keyboard-inset-height, 0px);
    // 添加过渡动画
    transition: bottom 0.3s ease, padding-bottom 0.3s ease;

    .regenerate-btn {
      margin-bottom: 0.625rem;
      text-align: center;
    }
  }

  // .speech-wrapper {
  //   width: 100%;
  //   height: 100%;
  // }
}