/*
 * @Description: FormSubmission 隐患描述功能演示页面
 * @Author: AI Assistant
 * @Date: 2025-07-17
 */
import React from 'react';
import { NavBar } from 'antd-mobile';
import { LeftOutline } from 'antd-mobile-icons';
import { useNavigate } from 'react-router-dom';
import FormSubmissionTest from '../../components/FormSubmission/test';

const FormSubmissionDemo: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div style={{ height: '100vh', backgroundColor: '#f5f5f5' }}>
      <NavBar
        style={{ backgroundColor: 'white' }}
        onBack={handleBack}
        backIcon={<LeftOutline />}
      >
        表单填报隐患描述功能演示
      </NavBar>
      
      <FormSubmissionTest />
    </div>
  );
};

export default FormSubmissionDemo;
