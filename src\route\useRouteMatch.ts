import { useLocation } from 'react-router-dom';
import { routes, AppRoute } from './routes'; // 确保导入你的路由配置和类型定义

/**
 * 自定义 Hook，根据当前 URL 匹配路由并返回路由的 name
 * @returns 匹配的路由名称，如果未匹配到则返回空字符串
 */
const useRouteName = () => {
    const location = useLocation();

    /**
     * 将路径转换为正则表达式，支持动态参数
     * @param {string} path - 路由路径（可能包含动态参数，如 :id）
     * @returns {RegExp} 用于匹配路径的正则表达式
     */
    const pathToRegex = (path) => {
        // 替换动态参数 ":param" 为正则表达式 "([^/]+)"
        const regexPath = path.replace(/:\w+/g, '([^/]+)');
        return new RegExp(`^${regexPath.replace(/\/$/, '')}(\\/.*)?$`);
    };

    /**
     * 递归查找匹配的路由
     * @param {AppRoute[]} routes - 路由数组
     * @param {string} pathname - 当前路径
     * @param {string} parentPath - 父路径（用于拼接完整路径）
     * @returns {AppRoute | null} 匹配的路由对象，未找到则返回 null
     */
    const findRoute = (routes: AppRoute[], pathname: string, parentPath: string = ''): AppRoute | null => {
        for (const route of routes) {
            // 拼接完整路径：父路径 + 当前路由的路径
            const fullPath = `${parentPath}/${route.path}`.replace(/\/+/g, '/'); // 避免多余的斜杠

            // 将路径转换为正则表达式并匹配当前路径
            if (pathToRegex(fullPath).test(pathname)) {
                // 如果匹配成功，检查是否有子路由
                if (route.children) {
                    const childRoute = findRoute(route.children, pathname, fullPath);
                    if (childRoute) {
                        return childRoute; // 返回匹配的子路由
                    }
                }
                // 如果没有子路由，直接返回当前路由
                return route;
            }
        }
        // 没有匹配到路由
        return null;
    };

    // 从 location 中获取 pathname（去掉查询参数部分）
    const { pathname } = location;

    // 查找匹配的路由
    const matchedRoute = findRoute(routes, pathname);

    // 返回匹配路由的 name，没有匹配到则返回空字符串
    return matchedRoute?.name || '';
};

export default useRouteName;
