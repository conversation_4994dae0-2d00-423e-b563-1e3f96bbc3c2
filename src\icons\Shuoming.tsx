import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Shuoming(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 16 16"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aeeb__a"><rect width="16" height="16" rx="0"/></clipPath></defs><g clipPath="url(#14aeeb__a)" fill="#575B66"><path data-follow-fill="#575B66" d="M8 16c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8ZM8 1.333c-3.667 0-6.667 3-6.667 6.667s3 6.667 6.667 6.667 6.667-3 6.667-6.667-3-6.667-6.667-6.667Z" fill={_fill}/><path data-follow-fill="#575B66" d="M8 4.8a.605.605 0 0 1-.2-.467c0-.2.067-.334.2-.467a.605.605 0 0 1 .467-.2c.2 0 .333.067.466.***********.267.2.467s-.066.333-.2.466a.72.72 0 0 1-.466.2A.605.605 0 0 1 8 4.8Zm1.533 6.4q-.066 0-.133.066c-.533.867-1.733 1.667-1.533.533q0-.066 1.066-5.466c.067-.334-.2-.734-.533-.8-.333-.067-.6.133-.733.466l-1 5.267c-.334 2.4 2.066 2 3 .2.066-.067.066-.133.066-.2-.066 0-.133-.067-.2-.067Z" fill={_fill}/></g></g>
        </svg>
    )
}
