.new-task-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f0f2f5;

  .new-task-header {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 16px;
    background-color: #fff;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    .header-icon{
      width: 17px;
      height: 17px;
      margin-right: 16px;
      position: absolute;
      left: 16px;
    }
    .back-btn {
      background: none;
      border: 1px solid #4873FF;
      color: #4873FF;
      border-radius: 6px;
      margin-right: 16px;
    }

    .page-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .new-task-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    // 搜索框区域
    .search-section {
      padding: 16px;

      .adm-search-bar {
        background-color: #fff;
        border-radius: 8px;

        .adm-search-bar-input-box {
          background-color: transparent;

          .adm-search-bar-input {
            font-size: 14px;
            color: #333;

            &::placeholder {
              color: #999;
            }
          }
        }
      }
    }

    // 巡检对象列表
    .targets-list {
      flex: 1;
      padding: 0 16px 16px 16px;
      overflow-y: auto;

      .target-item {
        height: 65px;
        line-height: 32.5px;
        background: linear-gradient( 180deg, #FFFFFF 0%, #EBF3F9 100%);
        box-shadow: 2px 2px 7px 0px rgba(0,0,0,0.04);
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 16px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.selected {
          border-color: #4873FF;
          background-color: #f6f9ff;
        }

        &:last-child {
          margin-bottom: 0;
        }

        .target-info {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .target-name {
            font-size: 16px;
            font-weight: 500;
            color: rgba(0, 0, 0, 0.80);
            flex: 1;
            margin-right: 12px;
          }
          .target-distance-icon{
            width: 9px;
            height: 9px;
            background: url('../../assets/taskList/icon_gis.png') no-repeat center center;
            background-size: 100% 100%;
            margin-right: 4px;
          }
          .target-distance {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.60);
            font-weight: 500;
          }
        }
      }
    }
  }
}
