// {
//   "compilerOptions": {
    
//     "noUncheckedIndexedAccess": false,
//     "allowUnusedLabels": true,
//     "allowJs": true, // 允许编译 JavaScript 文件
//     "checkJs": true, // 启用对 JavaScript 文件的类型检查
//     "target": "ES2020",
//     "useDefineForClassFields": true,
//     "lib": ["ES2020", "DOM", "DOM.Iterable"],
//     "module": "ESNext",

//     /* Bundler mode */
//     "moduleResolution": "bundler",
//     "allowImportingTsExtensions": true,
//     "resolveJsonModule": true,
//     "isolatedModules": true,
//     "noEmit": true,
//     "jsx": "react-jsx",

//     /* Linting */
//     "noImplicitAny": false,
//     "strict": false,
//     "noUnusedLocals": false,
//     "noUnusedParameters": false,
//     "noFallthroughCasesInSwitch": true,
//     "skipLibCheck": true,

//     "baseUrl": "./",
//     "paths": {
//       "@/*": ["src/*"]
//     },
//   },
//   "exclude": [
//     "src/icons"  // 排除 icons 文件夹
//   ],
//   "include": ["src"],
//   "references": [{ "path": "./tsconfig.node.json" }]
// }
{
  "compilerOptions": {
    "noUncheckedIndexedAccess": false,
    "allowUnusedLabels": true,
    "allowJs": true,
    "checkJs": true,
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "noImplicitAny": false,
    "strict": false,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "skipLibCheck": true,
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    }
  },
  "exclude": [
    "node_modules",
    "src/icons"
  ],
  "include": ["src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
