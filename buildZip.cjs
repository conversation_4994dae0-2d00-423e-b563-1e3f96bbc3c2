/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-05 20:02:51
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-16 11:40:38
 * @FilePath: \react-h5-template\buildZip.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { name: packageName, version: packageVersion } = require('./package.json');
const dayjs = require('dayjs');

// 生成时间
const time = dayjs().format('YYYYMMDDHH');

const getZipFileName = () => {
    return `${packageName}_r${packageVersion}_${time}`;
};

// 压缩包的输出路径
const output = fs.createWriteStream(path.join(__dirname, `${getZipFileName()}.zip`));
const archive = archiver('zip', {
  zlib: { level: 9 } // 设置压缩级别
});

console.log('开始创建压缩包...');

output.on('close', function() {
    console.log(`压缩包已创建，共 ${archive.pointer()} 总字节`);
});

archive.on('error', function(err) {
    throw err;
});

archive.pipe(output);

// 添加 'dist' 目录到压缩包中，并重命名为 `packageName`
archive.directory('dist/', packageName);

archive.finalize();
