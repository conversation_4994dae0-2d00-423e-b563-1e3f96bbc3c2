/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-12 11:56:43
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-23 14:45:05
 * @FilePath: src/components/speech-recognition/index.module.scss
 * @Version: 1.0.0
 * @Description: 组件描述
 */
.speech-recognition {
  position: absolute;
  display: inline-block;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  pointer-events: auto;

  &.full-screen {

    & > .bottom-wrapper {
      height: 90%;

      .input-wrapper {
        height: 12.5rem;
      }
    }
  }

  .bottom-wrapper {
    position: absolute;
    width: 100%;
    height: 19.375rem;
    bottom: 0;
    background: #F2F5F7;
    border-radius: 10px 10px 0 0;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;

    .close-button-wrapper {
      position: relative;
      width: 100%;
      display: flex;
      height: 1.5rem;
      align-items: center;
      justify-content: center;
      z-index: 1;

      .close-button {
        width: 5.625rem;
        height: 100%;
        background: url('./images/icon_close.svg') no-repeat center center;
        background-size: 100% 100%;
      }
    }

    .input-wrapper {
      position: relative;
      margin: 0.75rem 0.75rem 0 0.75rem;
      height: 46px;
      border-radius: 10px;
      background: #FFFFFF;
      border: 1px solid rgba(0, 0, 0, 0.06);
      box-sizing: border-box;
      z-index: 1;

      .text-area {
        position: absolute;
        padding: 12px 0 0 12px;
        left: 0;
        right: 40px;
        height: 100%;
        width: auto;
        line-height: 1.5;
        font-size: 15px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.8);
        text-align: left;
        border: none;
        caret-color: #4873FF;
        resize: none;
        white-space: pre-wrap;
        word-break: break-all;
        overflow-wrap: break-word;
        box-sizing: border-box;
        box-shadow: none;
      }

      .full-screen-btn {
        position: absolute;
        top: 0.75rem;
        right: 0.75rem;
        width: 1.25rem;
        height: 1.25rem;
        background: url('./images/icon_arrow_alt.svg') center center no-repeat;
      }
    }

    .action-wrapper {
      position: absolute;
      display: flex;
      bottom: 0;
      width: 100%;
      height: 100%;
      align-items: flex-end;

      .wait-wrapper {
        position: fixed;
        display: flex;
        padding-bottom: 2rem;
        bottom: 0;
        width: 100%;
        gap: 3.5rem;
        align-items: center;
        justify-content: center;

        .clear-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.25rem;
          user-select: auto;

          .clear-button {
            width: 1.125rem;
            height: 1.125rem;
            background: url('./images/icon_clear.svg') no-repeat center center;
            background-size: 100% 100%;
          }

          .clear-text {
            color: rgba(0, 0, 0, 0.6);
            font-size: 13px;
          }
        }

        .speak-wrapper {
          position: relative;

          .speak-text {
            position: absolute;
            margin: auto;
            top: -1.25rem;
            left: 0;
            right: 0;
            color: rgba(0, 0, 0, 0.4);
            font-size: 12px;
            text-align: center;
            // 一行显示,但不隐藏
            display: -webkit-box;
            -webkit-line-clamp: 1; // 显示一行
            -webkit-box-orient: vertical; // 垂直显示
          }

          .speak-button {
            display: flex;
            width: 80px;
            height: 80px;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            background: linear-gradient(315deg, rgba(35, 60, 138, 0.9) 0%, rgba(72, 115, 255, 0.9) 100%);
            transition: all 0.2s ease;
            transform: scale(1);

            // 防止移动端长按弹出菜单
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -khtml-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-tap-highlight-color: transparent;

            // 防止拖拽
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;

            // 按压状态样式
            &.pressed {
              transform: scale(0.95);
              background: linear-gradient(315deg, rgba(35, 60, 138, 1) 0%, rgba(72, 115, 255, 1) 100%);
              box-shadow: 0 2px 8px rgba(72, 115, 255, 0.3);
            }

            // 录音状态样式
            &.recording {
              animation: pulse 1.5s infinite;
              background: linear-gradient(315deg, rgba(255, 59, 48, 0.9) 0%, rgba(255, 149, 0, 0.9) 100%);
            }

            & > img {
              pointer-events: none !important;
              user-select: none !important;
              -webkit-touch-callout: none !important;
              -webkit-user-select: none !important;
              -moz-user-select: none !important;
              -ms-user-select: none !important;
              -webkit-user-drag: none !important;
              -khtml-user-drag: none !important;
              -moz-user-drag: none !important;
              -o-user-drag: none !important;
              -webkit-tap-highlight-color: transparent !important;

              // 防止图片被选中和保存
              -webkit-touch-callout: none !important;
              -webkit-user-select: none !important;
              -khtml-user-select: none !important;
              -moz-user-select: none !important;
              -ms-user-select: none !important;
              user-select: none !important;
            }
          }
        }

        .send-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 0.25rem;

          &.disabled {
            pointer-events: none;
          }

          &.active {
            pointer-events: auto;

            .send-button {
              background: url('./images/icon_send_active.svg') no-repeat center center;
            }

            .send-text {
              color: #4873FF;
            }
          }

          .send-button {
            width: 1.125rem;
            height: 1.125rem;
            background: url('./images/icon_send.svg') no-repeat center center;
            background-size: 100% 100%;
          }

          .send-text {
            color: rgba(0, 0, 0, 0.3);
            font-size: 13px;
          }
        }
      }

      .speech-wrapper {
        display: flex;
        width: 100%;
        height: 100%;
        flex-direction: column;
        align-items: center;
        justify-content: flex-end;
        gap: 0.5rem;

        // 添加触摸交互样式
        cursor: pointer;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -khtml-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        -webkit-tap-highlight-color: transparent;

        .text {
          font-weight: 400;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.4);
        }

        .animation-wrapper {
          display: flex;
          padding: 0 1.875rem;
          width: 100%;
          height: 5rem;
          background: #DDEDF7;
          border-radius: 56px 56px 0 0;
          align-items: center;

          .animation {
            width: 100%;
            height: 30px;
            background: url('./images/speech-animation.gif') no-repeat center center;
            background-size: 370px 30px;
          }
        }
      }

      .hide {
        display: none;
      }
    }
  }
}

// 脉冲动画
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(255, 59, 48, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 59, 48, 0);
  }
}