<!--
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-09 09:36:31
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-01-23 17:10:18
 * @FilePath: \fSafetyh5\README.md
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<div align="center">

  <h1>ai巡检项目</h1>

  <p>
    Vite + React + Antd Mobile 
  </p>

</div>

## 功能
1. 使用了`postcss`实现`px to rem`
2. 使用`zustand`作为全局缓存库
3. 配置了`icon-park`，即字节旗下`ico`库
4. 对`react-router-dom`进行了功能封装
5. 自定义`hooks`: `useLocation`、`useI18n`、`useSessionStorage`
6. 基于`js-qr`实现了浏览器扫码功能组件`QRScanner`

## 说明
1. 全局根字体大小断点（`src/index.css`）

```html
   html {
      -webkit-text-size-adjust: 100%;
      -ms-text-size-adjust: 100%;
      font-size: 100%;
    }

    /* 小屏幕设备（如手机）*/
    @media (max-width: 600px) {
        html {
            font-size: 90%; /* 字体稍微小一点 */
        }
    }

    /* 中等屏幕设备（如平板）*/
    @media (min-width: 601px) and (max-width: 1024px) {
        html {
            font-size: 100%; /* 标准大小 */
        }
    }

    /* 大屏幕设备（如桌面）*/
    @media (min-width: 1025px) {
        html {
            font-size: 110%; /* 字体稍微大一点 */
        }
    }
```

2. 组件库全局配色（`src/index.css`）

```html
  :root {
    --primary-color: #FFC300;
  }

  :root:root {
      --adm-color-primary: #FFC300;
      --adm-color-success: #00b578;
      --adm-color-warning: #ff8f1f;
      --adm-color-danger: #ff3141;

      --adm-color-white: #ffffff;
      --adm-color-text: #333333;
      --adm-color-text-secondary: #666666;
      --adm-color-weak: #999999;
      --adm-color-light: #cccccc;
      --adm-color-border: #eeeeee;
      --adm-color-box: #f5f5f5;
      --adm-color-background: #ffffff;

      --adm-font-size-main: var(--adm-font-size-5);

      --adm-font-family: -apple-system, blinkmacsystemfont, 'Helvetica Neue',
      helvetica, segoe ui, arial, roboto, 'PingFang SC', 'miui',
      'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  }
```
```shell
pnpm i

pnpm run dev
```