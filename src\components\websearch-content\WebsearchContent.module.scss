.websearch-content {
    margin-bottom: 0.5rem;
    margin-top: 0.75rem;

    .websearch {
        font-size: 14px;
        .websearch-title {
            color: #8b8b8b
        }
        .websearch-list {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            .websearch-list-item {
                display: flex;
                flex-direction: row;
                color: #4873FF;
                align-items: start;
                cursor: pointer;
                img {
                    width: 16px;
                    height: 16px;
                    margin: 0.2rem 0.25rem;
                }
            }
        }
    }
    :global {
        .ant-collapse {
            border: none;
            border-radius: 8px;
        }
        .ant-collapse-header-text {
            -webkit-touch-callout: none; /* iOS Safari */
            -webkit-user-select: none;   /* Chrome/Safari/Opera */
            -khtml-user-select: none;    /* Konqueror */
            -moz-user-select: none;      /* Firefox */
            -ms-user-select: none;       /* Internet Explorer/Edge */
            user-select: none;
        }
        .ant-collapse-header {
            padding: 0.5rem 1rem !important;
            background-color: #FFF;
            border: none !important;
            border-radius: 8px !important;
        }
        .ant-collapse-content {
            margin-top: -0.5rem;
            padding: 0.5rem;
            border: none !important;
        }
        .ant-collapse-content-box {
            padding: 0.5rem !important;
            color: rgba(0, 0, 0, 0.6);
            background-color:#edf2fa7d;
            border-radius: 8px;
        }
    }
}