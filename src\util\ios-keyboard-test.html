<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>iOS 键盘测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow-x: hidden;
        }
        
        .header {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #eee;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 100;
        }
        
        .content {
            margin-top: 60px;
            padding: 16px;
            padding-bottom: 120px;
        }
        
        .message {
            background: white;
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .input-container {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 16px;
            border-top: 1px solid #eee;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .input-wrapper {
            display: flex;
            gap: 8px;
            align-items: flex-end;
        }
        
        .input-field {
            flex: 1;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 16px;
            resize: none;
            outline: none;
            min-height: 40px;
            max-height: 120px;
        }
        
        .send-btn {
            background: #007AFF;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            min-width: 60px;
        }
        
        .send-btn:disabled {
            background: #ccc;
        }
        
        .status {
            position: fixed;
            top: 70px;
            right: 16px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            z-index: 200;
        }
        
        /* iOS 键盘适配 */
        body.keyboard-visible .input-container {
            transform: translateY(calc(-1 * var(--keyboard-height, 0px) * 0.2));
        }
        
        body.keyboard-visible .content {
            padding-bottom: calc(120px + var(--keyboard-height, 0px) * 0.3);
        }
        
        /* iOS 特殊处理 */
        @supports (-webkit-touch-callout: none) {
            body.keyboard-visible .input-container {
                transform: translateY(calc(-1 * var(--keyboard-height, 0px) * 0.1));
            }
            
            body.keyboard-visible .content {
                padding-bottom: calc(120px + var(--keyboard-height, 0px) * 0.2);
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h3>iOS 键盘适配测试</h3>
    </div>
    
    <div class="status" id="status">
        键盘状态: 隐藏
    </div>
    
    <div class="content" id="content">
        <!-- 生成测试消息 -->
        <script>
            for (let i = 1; i <= 30; i++) {
                document.write(`<div class="message">测试消息 ${i} - 这是一条用于测试滚动和键盘适配的消息内容。</div>`);
            }
        </script>
    </div>
    
    <div class="input-container">
        <div class="input-wrapper">
            <textarea 
                class="input-field" 
                id="messageInput" 
                placeholder="请输入消息..."
                rows="1"
            ></textarea>
            <button class="send-btn" id="sendBtn" disabled>发送</button>
        </div>
    </div>
    
    <script>
        class IOSKeyboardTest {
            constructor() {
                this.originalHeight = window.innerHeight;
                this.isKeyboardVisible = false;
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.status = document.getElementById('status');
                
                this.init();
            }
            
            init() {
                // 监听输入框事件
                this.messageInput.addEventListener('focus', this.handleFocus.bind(this));
                this.messageInput.addEventListener('blur', this.handleBlur.bind(this));
                this.messageInput.addEventListener('input', this.handleInput.bind(this));
                
                // 监听窗口大小变化
                window.addEventListener('resize', this.handleResize.bind(this));
                
                // 发送按钮事件
                this.sendBtn.addEventListener('click', this.handleSend.bind(this));
                
                this.updateStatus();
            }
            
            handleFocus() {
                console.log('输入框获得焦点');
                
                // iOS 延迟检测
                setTimeout(() => {
                    this.checkKeyboardStatus();
                }, 500);
            }
            
            handleBlur() {
                console.log('输入框失去焦点');
                
                setTimeout(() => {
                    if (document.activeElement !== this.messageInput) {
                        this.hideKeyboard();
                    }
                }, 100);
            }
            
            handleResize() {
                this.checkKeyboardStatus();
            }
            
            handleInput() {
                this.sendBtn.disabled = !this.messageInput.value.trim();
                
                // 自动调整高度
                this.messageInput.style.height = 'auto';
                this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
            }
            
            handleSend() {
                if (this.messageInput.value.trim()) {
                    console.log('发送消息:', this.messageInput.value);
                    this.messageInput.value = '';
                    this.sendBtn.disabled = true;
                    this.messageInput.style.height = 'auto';
                }
            }
            
            checkKeyboardStatus() {
                const currentHeight = window.innerHeight;
                const heightDiff = this.originalHeight - currentHeight;
                
                console.log('高度变化:', { original: this.originalHeight, current: currentHeight, diff: heightDiff });
                
                if (heightDiff > 100 && !this.isKeyboardVisible) {
                    this.showKeyboard(heightDiff);
                } else if (heightDiff < 50 && this.isKeyboardVisible) {
                    this.hideKeyboard();
                }
            }
            
            showKeyboard(height) {
                console.log('键盘弹起，高度:', height);
                this.isKeyboardVisible = true;
                
                // iOS 使用保守的键盘高度估算
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
                let keyboardHeight = height;
                
                if (isIOS) {
                    const estimatedHeight = this.originalHeight * 0.35;
                    if (height > estimatedHeight * 1.5) {
                        keyboardHeight = estimatedHeight;
                        console.log('iOS 使用估算键盘高度:', keyboardHeight);
                    }
                }
                
                document.body.classList.add('keyboard-visible');
                document.documentElement.style.setProperty('--keyboard-height', `${keyboardHeight}px`);
                
                this.updateStatus();
                this.scrollToInput();
            }
            
            hideKeyboard() {
                console.log('键盘收起');
                this.isKeyboardVisible = false;
                
                document.body.classList.remove('keyboard-visible');
                document.documentElement.style.removeProperty('--keyboard-height');
                
                this.updateStatus();
            }
            
            scrollToInput() {
                // iOS 保守的滚动策略
                const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
                
                setTimeout(() => {
                    const inputRect = this.messageInput.getBoundingClientRect();
                    const keyboardHeight = this.getKeyboardHeight();
                    const availableHeight = window.innerHeight - keyboardHeight;
                    
                    if (inputRect.bottom > availableHeight) {
                        let scrollAmount = inputRect.bottom - availableHeight + 50;
                        
                        // iOS 减少滚动量
                        if (isIOS) {
                            scrollAmount *= 0.2;
                        }
                        
                        console.log('滚动到输入框:', scrollAmount);
                        window.scrollBy({
                            top: scrollAmount,
                            behavior: 'smooth'
                        });
                    }
                }, 100);
            }
            
            getKeyboardHeight() {
                const currentHeight = window.innerHeight;
                return Math.max(0, this.originalHeight - currentHeight);
            }
            
            updateStatus() {
                const keyboardHeight = this.getKeyboardHeight();
                this.status.textContent = `键盘: ${this.isKeyboardVisible ? '可见' : '隐藏'} | 高度: ${keyboardHeight}px | 视口: ${window.innerHeight}px`;
            }
        }
        
        // 初始化
        new IOSKeyboardTest();
    </script>
</body>
</html>
