/*
 * @Author: wangliang
 * @Date: 2023-04-21 09:35:04
 * @LastEditTime: 2025-06-12 15:14:02
 * @LastEditors: wl
 * @Description: 常量配置
 */

// 翻页组件每页显示条数
export const PAGE_SIZE_OPTIONS = [10, 20, 50, 100];
// 筛选日期范围格式
export const START_TIME_FORMAT = 'YYYY-MM-DD 00:00:00';
export const END_TIME_FORMAT = 'YYYY-MM-DD 23:59:59';
// 时间格式
export const TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';
// 手机号正则
export const PHONE_REG = /^1[3456789]\d{9}$/;
// 邮箱正则
export const EMAIL_REG = /^[a-z0-9]+([._\\-]*[a-z0-9])*@([a-z0-9]+[-a-z0-9]*[a-z0-9]+.){1,63}[a-z0-9]+$/;
// 数字字母
export const NUMBER_LETTER_REG = /^[a-zA-Z0-9]*$/;
// 数字、字母、点、@、_
export const NUMBER_LETTER_EMAIL_REG = /^[a-zA-Z0-9.\\@_-]*$/;
// 数字
export const NUMBER_REG = /^[0-9]*$/;
// 邀请码状态
export const INVITATION_CODE_STATUS = [
	{ label: '未使用', value: 0, color: '#1890FF' },
	{ label: '已发送', value: 1, color: '#FAAD14' },
	{ label: '已使用', value: 2, color: '#D9D9D9' },
];
//账号是否停用
export const USER_ACCOUNT_STATUS = [
	{ label: '正常', value: 0, color: '#1890FF', bgColor: '#E6F4FF' },
	{ label: '停用', value: 1, color: '#FF4D4F', bgColor: '#FFF2F0' },
];
//角色状态
export const ROLE_STATUS = [
	{ label: '启用', value: 0, color: '#1890FF', bgColor: '#E6F4FF' },
	{ label: '停用', value: 1, color: '#FF4D4F', bgColor: '#FFF2F0' },
];
//角色类型
export const ROLE_TYPE = [
	{ label: '自定义', value: 2, color: '#1890FF', bgColor: '#E6F4FF' },
	{ label: '内置', value: 1, color: '#FF4D4F', bgColor: '#FFF2F0' },
];
//审批状态
export const APPROVAL_STATUS = [
	{ label: '未审批', value: 0, color: '#ff842c', bgColor: '#fff9f0' },
	{ label: '通过', value: 1, color: '#52C41A', bgColor: '#f6ffed' },
	{ label: '不通过', value: 2, color: '#FF4D4F', bgColor: '#FFF2F0' },
];
// 上传配置
export const UPLOAD_CONFIG = {
	'set_exams': {
		onlyFirst: true, // 仅新对话可以上传图片
		uploadList: [
			{
				accept: '.doc, .docx, .pdf',
				popoverContent: '支持上传2M以内Word、PDF文件',
				uploadText: '上传文件',
				filtType: 'kit',
				fileParam: 'fileIds',
				fileSize: 2,
				fileKey: 'chatFiles',
			},

		],

	},
	'image_analysis': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],

	},
	'risk_analysis': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],

	},
	'': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],

	},
	'default': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],

	},
	'fire_operation': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
			{
				accept: '.mp4',
				popoverContent: '支持上传30M以内视频',
				uploadText: '上传视频',
				filtType: 'chat_video',
				fileParam: 'videoIds',
				fileSize: 30,
				fileKey: 'chatVideos',
			},
		],
	},
	'fire_pump': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],
	},
	'campus_inspection': {
		onlyFirst: false,
		uploadList: [
			{
				accept: '.jpg, .jpeg, .png',
				popoverContent: '支持上传10M以内图片',
				uploadText: '上传图片',
				filtType: 'image',
				fileParam: 'imageIds',
				fileSize: 10,
				fileKey: 'chatImages',
			},
		],
	},

};

const BASE_IMAGE_CONFIG = {
	type: 'file',
	accept: '.jpg, .jpeg, .png',
	popoverContent: '支持上传10M以内图片',
	uploadText: '上传图片',
	filtType: 'image',
	fileParam: 'imageIds',
	fileSize: 10,
	fileKey: 'chatImages',
};

const BASE_DOC_CONFIG = {
	accept: '.doc, .docx, .pdf',
	popoverContent: '支持上传2M以内Word、PDF文件',
	uploadText: '上传文件',
	filtType: 'kit',
	fileParam: 'fileIds',
	fileSize: 2,
	fileKey: 'chatFiles',
};

const BASE_VIDEO_CONFIG = {
	accept: '.mp4',
	popoverContent: '支持上传30M以内视频',
	uploadText: '上传视频',
	filtType: 'chat_video',
	fileParam: 'videoIds',
	fileSize: 30,
	fileKey: 'chatVideos',
};

// 导出配置
export const UPLOAD_CONFIG_NEW = {
	'': { ...BASE_IMAGE_CONFIG },
	'default': { ...BASE_IMAGE_CONFIG },
	'risk_analysis': { ...BASE_IMAGE_CONFIG },
	'set_exams': { ...BASE_DOC_CONFIG },
	'image_analysis': { ...BASE_IMAGE_CONFIG },
	'fire_operation': { ...BASE_VIDEO_CONFIG },
	'fire_pump': { ...BASE_IMAGE_CONFIG },
	'check_list': { ...BASE_IMAGE_CONFIG },
};

export const UPLOAD_CONFIG_IMAGE = {
	'': { ...BASE_IMAGE_CONFIG },
	'default': { ...BASE_IMAGE_CONFIG },
	'risk_analysis': { ...BASE_IMAGE_CONFIG },
	'set_exams': { ...BASE_IMAGE_CONFIG },
	'image_analysis': { ...BASE_IMAGE_CONFIG },
	'fire_operation': { ...BASE_IMAGE_CONFIG },
	'fire_pump': { ...BASE_IMAGE_CONFIG },
};