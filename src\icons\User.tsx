import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function User(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 36 36"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aee9__a"><rect width="36" height="36" rx="0"/></clipPath><clipPath id="14aee9__b"><rect x="7" y="7" width="22" height="22" rx="0"/></clipPath><clipPath id="14aee9__c"><rect x="8" y="7" width="28" height="28" rx="0"/></clipPath></defs><g clipPath="url(#14aee9__a)"><circle r="18" cx="18" cy="18" fill="#70B5FF"/><g clipPath="url(#14aee9__b)"><g clipPath="url(#14aee9__c)"><path d="m28.267 21.308-.492.45-1.935-2.009.491-.45c.628-.586 1.622-.617 2.146-.083.523.544.429 1.486-.21 2.092ZM17.196 24.28c-.23 0-.722.701-.984 1.078-.533.754-1.025 1.455-1.737 1.455-.711-.021-1.193-.764-1.643-1.476-.21-.324-.617-.973-.837-1.046-.293.157-.92 1.004-2.24 2.417a.511.511 0 0 1-.89-.335L8.867 8.27c0-.628.513-1.13 1.13-1.13h14.42c.628 0 1.13.502 1.13 1.109V18.2l-7.492 7.053c-.251-.367-.66-.974-.858-.974Zm4.74-12.086h-9.45a.708.708 0 0 0-.71.69c0 .388.313.691.71.691h9.45a.708.708 0 0 0 .712-.69.715.715 0 0 0-.712-.691Zm0 3.118h-9.45a.708.708 0 0 0-.71.691c0 .387.313.69.71.69h9.45a.708.708 0 0 0 .712-.69.715.715 0 0 0-.712-.69Zm.701 3.81a.695.695 0 0 0-.711-.691h-9.44a.708.708 0 0 0-.71.69c0 .388.313.691.71.691h9.45a.692.692 0 0 0 .701-.69Zm-2.762 6.226a.366.366 0 0 1 .125-.21l5.044-4.646 1.905 1.988-5.023 4.678a.619.619 0 0 1-.22.115l-2.061.565c-.032.032-.084.032-.105.032a.456.456 0 0 1-.304-.115.356.356 0 0 1-.062-.409l.7-1.998Z" fill="#FFF" /></g></g></g></g>
        </svg>
    )
}
