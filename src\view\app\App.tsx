/*
 * @Description: 
 * @Author: cl
 * @Date: 2024-12-09 09:54:07
 * @LastEditTime: 2025-02-21 09:57:07
 * @LastEditors: 杨越 <EMAIL>
 */
import './App.css'
import {RenderRoutes} from "@/route/render-routes.tsx";
import {axiosInstance} from "@/http/axios-instance.ts";
import {configure} from 'axios-hooks'
import enUS from "antd-mobile/es/locales/en-US";
import {ConfigProvider} from "antd-mobile";
import {useI18nStore} from "@/store/i18n.ts";
import zhCN from "antd-mobile/es/locales/zh-CN";
import { useEffect } from 'react';

declare global {
    interface Window {
        MonitorJS: any;
        monitor: any;
        hostConfig?: {
            basename?: string;
        };
        // 安卓端语音识别相关
        android?: {
            startAsr: () => void;
            stopAsr: () => void;
            // 安卓端相机相关
            startCamera: () => void;
            getCameraResult?: (callback: (result: string) => void) => void;
        };
        startAsrSuccessCallback?: () => void;
        getAsrResultCallback?: (result: string) => void;
        stopAsrSuccessCallback?: () => void;
        // 录音管理器
        RecorderManager?: any;
        // 页面body属性
        body?: {
            clientHeight: number;
        };
    }

    interface Navigator {
        // IE浏览器的保存文件方法
        msSaveBlob?: (blob: Blob, filename: string) => void;
    }
}
import { useNavigate } from 'react-router-dom';
import { getUrlParmse } from '@/util/method.ts';
import { apis } from '@/api/api';
import { configWeChat } from '@/tools/speech-recognition-wechat';

// 检测微信环境
const getWeChatEnvironment = () => {
    const ua = navigator.userAgent.toLowerCase();
    if (/micromessenger/i.test(ua)) {
        return 'wechat';
    }
    return 'other';
};

function App() {
    configure({
        axios: axiosInstance
    })
    const i18nStore = useI18nStore()
    const navigate = useNavigate();
    useEffect(() => {
        // 获取用户信息
        const { token,openId,refreshToken } = getUrlParmse();
        if (token) {
            sessionStorage.setItem('token', token);
        }
        if(refreshToken){
            sessionStorage.setItem('refreshToken', refreshToken);
        }
        sessionStorage.setItem('openId', decodeURIComponent(openId));
        removeIndexDB();

        // 微信环境配置
        if (getWeChatEnvironment() !== 'other') {
            apis.wxMp.getSign({
                appId: 'wx97f7c37249a7c83f',
                url: window.location.href,
            }).then(res => {
                if (res.code === 0) {
                    const { appId, timestamp, nonceStr, signature } = res.data;
                    configWeChat({
                        appId,
                        timestamp,
                        nonceStr,
                        signature,
                    },
                    (res) => {
                        console.log('微信JSSDK配置成功', res);
                    },
                    (error) => {
                        console.error('微信JSSDK配置失败', error);
                    });
                } else {
                    console.warn('微信认证失败:', res);
                }
            }).catch(error => {
                console.error('获取微信签名失败:', error);
            });
        }
        // apis.ginkgo.getUserDetail().then((res) => {
        //     console.log('用户信息',res);
        //     const userInfo = {
        //         cadetName: res.nickname,
        //         gingkoId: res.id,
        //         openId: decodeURIComponent(openId),
        //         teacherUniqueId: tid||'',
        //     }
            
        //     apis.mtweb_user_info.getCurrentUser(userInfo).then((res) => {
        //         console.log('登录信息',res);
        //         sessionStorage.setItem('userInfo', JSON.stringify(res));
        //         sessionStorage.setItem('identity', res.uniqueId);
                
        //         // window.monitor = new window.MonitorJS.WebMonitor({
        //         //     appName: '注安',
        //         //     userName: userInfo.cadetName,
        //         //     version: '1.0.1',
        //         //     jsError: false, //是否捕捉js错误和资源加载错误,默认true
        //         //     eventCatch: false,  //js错误是否捕捉最后一次点击事件,默认true
        //         //     promiseError: false,  //是否捕捉promise错误,默认true
        //         //     performanceNavigation: false,  //是否捕捉页面性能,默认true
        //         //     getFps: false,  //是否捕捉fps,默认true
        //         //     refreshOrLeave: false, //是否捕捉beforeunload事件,默认true
        //         //     consoleCatch: false,  //是否捕捉console,默认true，默认只捕捉error,可配置consoleTypeList
        //         //     consoleTypeList: ['error'],  //需要捕捉的consoleType ['error','warn','info','log',...]
        //         //     xhrCatch: false,  //是否捕捉xhr请求
        //         //     ignoreXhr: ["/scooper-safety-exam/mtweb/data/log/log",
        //         //         '/scooper-safety-exam/mtweb/data/exam/getQuestionByExamResultCollectService',
        //         //         '/scooper-safety-exam/mtweb/data/exam/submitAnswer',
        //         //         '/scooper-safety-exam/mtweb/data/practice/getQuestionByPracticeQuestionUniqueId',
        //         //         '/scooper-safety-exam/mtweb/data/practice/submitAnswer',
        //         //         '/scooper-safety-exam/mtweb/data/exam/getExamResult'
        //         //     ],
        //         //     url: 'https://www.gingpt.cn/scooper-safety-exam/mtweb/data/log/log'
        //         // })
        //         // window.monitor.init({
        //         //     webcastId: res.mobile,
        //         //     uid: res.uniqueId,
        //         //     cid: res.uniqueId,
        //         // })
        //         // window.monitor.info({
        //         //     code:200,
        //         //     message: "开始捕获",
        //         // })
        //         // setTimeout(() => {
        //         //     if (tid !== '') {
        //         //         apis.mtweb_user_info.concatenate({tid}).then((res) => {
        //         //             console.log('老师信息',res);
        //         //         });
        //         //     }
        //         // }, 1000);
        //     });
        // });
    }, [])

    const removeIndexDB = () => {
        const dbName = 'webMonitor';

        const request = indexedDB.deleteDatabase(dbName);

        request.onsuccess = function(event) {
            console.log(`数据库 ${dbName} 已成功删除。`);
        };

        request.onerror = function(event) {
            console.error(`删除数据库 ${dbName} 失败：`, event);
        };

        request.onblocked = function(event) {
            console.warn(`删除数据库 ${dbName} 被阻塞。`);
        };
    }
    return (
        <>
            <ConfigProvider locale={zhCN}>
                <RenderRoutes/>
            </ConfigProvider>
        </>
    )
}

export default App
