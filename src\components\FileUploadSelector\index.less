.file-upload-selector {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fff;

  .selector-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    position: sticky;
    top: 0;
    background: #fff;
    z-index: 10;

    .selector-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }

    .close-btn {
      font-size: 24px;
      color: #999;
      cursor: pointer;
      
      &:hover {
        color: #666;
      }
    }
  }

  .selector-content {
    // flex: 1;
    padding: 20px;
    overflow-y: auto;
    min-height: 400px; // 确保flex子元素可以收缩
    // 优化滚动体验
    -webkit-overflow-scrolling: touch; // iOS平滑滚动
    scrollbar-width: thin; // Firefox细滚动条

    // 自定义滚动条样式 (Webkit)
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;

      &:hover {
        background: #a8a8a8;
      }
    }

    .uploaded-files-section {
      display: flex;
      align-items: center;
      gap: 12px;
      height: 200px;
      overflow: auto;
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      border: 2px dashed #e0e0e0;
      flex-wrap: wrap;

      .uploaded-file-item {
        position: relative;
        width: 70px;
        height: 70px;
        border-radius: 8px;

        .uploaded-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .delete-file-btn {
          position: absolute;
          top: -8px;
          right: -8px;
          width: 20px;
          height: 20px;
          background: #ff4d4f;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          cursor: pointer;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover {
            background: #ff7875;
          }
        }
      }

      .more-files {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        background: #f0f0f0;
        border-radius: 8px;
        color: #666;
        font-size: 14px;
        font-weight: 500;
      }
    }

    .add-file-section {
      margin-bottom: 20px;

      .add-file-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        height: 80px;
        border: 1px dashed #4873FF;
        border-radius: 12px;
        background: rgba(72, 115, 255, 0.05);
        color: #4873FF;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(72, 115, 255, 0.1);
          border-color: #6C5CE7;
        }

        &:active {
          transform: scale(0.95);
        }

        svg {
          font-size: 32px;
        }
      }
    }

    .check-items-section {
      height: 240px;
      margin-bottom: 70px;
      overflow-y: auto;
      .check-items-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
        // 确保网格内容不会超出容器
        width: 100%;
        box-sizing: border-box;

        .check-item-card {
          padding: 16px;
          border: 2px solid #e0e0e0;
          border-radius: 12px;
          background: #fff;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;

          &:hover {
            border-color: #4873FF;
            background: rgba(72, 115, 255, 0.16);
            box-shadow: 0 2px 8px rgba(72, 115, 255, 0.1);
          }

          &.selected {
            border-color: #4873FF;
            background: rgba(72, 115, 255, 0.16);
            box-shadow: 0 2px 8px rgba(72, 115, 255, 0.2);

            .check-item-name {
              color: #4873FF;
              font-weight: 600;
            }
          }

          .check-item-name {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            line-height: 1.4;
          }

          .check-item-info {
            .requirements-count {
              font-size: 14px;
              color: #999;
              background: #f5f5f5;
              padding: 2px 8px;
              border-radius: 12px;
            }
          }
        }
        .active{
          border-color: #4873FF;
          background: rgba(72, 115, 255, 0.05);
          box-shadow: 0 2px 8px rgba(72, 115, 255, 0.2);

          .check-item-name {
            color: #4873FF;
            font-weight: 600;
          }
        }
      }
    }

    .selected-info {
      margin-top: 20px;
      padding: 16px;
      background: #f0f9ff;
      border-radius: 12px;
      border-left: 4px solid #4873FF;

      p {
        margin: 0;
        font-size: 14px;
        color: #666;
        line-height: 1.5;

        &:not(:last-child) {
          margin-bottom: 8px;
        }

        .selected-name {
          color: #4873FF;
          font-weight: 600;
        }
      }

      .tip-text {
        color: #999;
        font-size: 12px;
      }
    }
  }

  .selector-footer {
    display: flex;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #f0f0f0;
    background: #fff;
    position: fixed;
    bottom: 0;
    width: 100%;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 44px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 500;
      background: linear-gradient(315deg, rgba(35, 60, 138, 0.9) 0%, rgba(72, 115, 255, 0.9) 100%);
      border: none;
      border-radius: 1.375rem;
      font-size: 1rem;
      font-weight: 500;
      color: white;
      box-shadow: 0 0.25rem 0.75rem rgba(102, 126, 234, 0.3);
      transition: all 0.3s ease;
    }

    .cancel-btn {
      background: #f5f5f5;
      color: #666;
      border: none;

      &:hover {
        background: #e8e8e8;
      }
    }

    .confirm-btn {
      background: #4873FF;
      border: none;

      &:disabled {
        background: #d9d9d9;
        color: #999;
      }

      &:not(:disabled):hover {
        background: #6C5CE7;
      }
    }
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  .file-upload-selector {
    .selector-content {
      padding: 16px;

      .check-items-section {
        .check-items-grid {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;

          .check-item-card {
            padding: 12px;

            .check-item-name {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}
