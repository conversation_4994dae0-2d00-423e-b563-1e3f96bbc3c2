/*
 * @Description: 新增巡检弹框组件
 * @Author: AI Assistant
 * @Date: 2025-06-27
 */
import React, { useEffect, useState, useRef } from 'react';
import { Button, Popup, Toast, Input } from 'antd-mobile';
import { CameraOutline, SoundOutline, CloseOutline, EditSOutline, CheckOutline } from 'antd-mobile-icons';
import PhotoCapture from '@/components/PhotoCapture';
import { getReqId, createEventSourceHandler, beforeUpload } from '@/util/method';
import { SpeechRecognitionContainer } from '@/containers';
import FormSubmission from '@/components/FormSubmission';
import './NewInspectionModal.less';
import { apis } from '@/api/api';
interface NewInspectionModalProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
  taskData?: any; // 任务数据
  onDataUpdate?: () => void; // 数据更新回调
}

// 检查要求接口
interface CheckRequirement {
  id: string;
  content: string;
  completed: boolean;
  prompt?: string;
  outputType?: string;
  outputContent?: string;
  checkWay?: number;
}

// AI分析结果接口
interface AIAnalysisResult {
  id: string;
  requirement: string;
  result: string;
  count?: number;
}

// 显示模式类型
type DisplayMode = 'requirements' | 'analysis' | 'form';

const NewInspectionModal: React.FC<NewInspectionModalProps> = ({
  visible,
  onClose,
  onSubmit,
  taskData,
  onDataUpdate
}) => {
  // 添加调试信息
  console.log('NewInspectionModal 渲染, visible:', visible, 'taskData:', taskData);

  // 显示模式状态
  const [displayMode, setDisplayMode] = useState<DisplayMode>('analysis');
  const [ImageIds, setImageIds] = useState<string>('');

  // 检查处所名称状态
  const [locationName, setLocationName] = useState<string>(taskData?.versions[0]?.checkName || '');
  const [isEditingLocation, setIsEditingLocation] = useState<boolean>(false);

  // PhotoCapture相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false); // 是否显示拍照上传
  const [capturedImages, setCapturedImages] = useState<{url: string, file: File, ids?: string[]}[]>([]); // 拍摄的图片
  const [showVoiceInput, setShowVoiceInput] = useState(false); // 是否显示语音输入

  const forwardRef = useRef<any>(null);
  // 从taskData中提取检查要求
  const getRequirements = () => {
    console.log('getRequirements taskData:', taskData);
    if (!taskData?.versions?.[0]?.results) {
      console.log('taskData.versions[0].results 为空');
      return [];
    }

    const requirements = taskData.versions[0].results.map((result: any) => ({
      id: result.id.toString(),
      content: result.config.reqName,
      completed: false,
      prompt: result.config.prompt,
      outputType: result.config.outputType,
      outputContent: result.config.outputContent,
      checkWay: result.config.checkWay
    }));

    console.log('生成的requirements:', requirements);
    return requirements;
  };

  // 使用useState管理requirements，并在taskData变化时更新
  const [requirements, setRequirements] = useState<CheckRequirement[]>([]);

  // 语音AI分析结果数据
  const [voiceAnalysisResults, setVoiceAnalysisResults] = useState<AIAnalysisResult[]>([]);

  // AI分析结果数据
  const [analysisResults, setAnalysisResults] = useState<AIAnalysisResult[]>([]);

  // 当taskData变化时，重新计算requirements
  useEffect(() => {
    const newRequirements = getRequirements();
    setRequirements(newRequirements);
    console.log('taskData变化，更新requirements:', newRequirements);
  }, [taskData]);

  useEffect(() => {
    setVoiceAnalysisResults([]);
    setAnalysisResults([]);
    setCapturedImages([]);
    setDisplayMode('analysis'); // 重置为智能分析模式
    setImageIds('');
    setLocationName(taskData?.versions[0]?.checkName || ''); // 重置检查处所名称
    setIsEditingLocation(false); // 重置编辑状态
  }, [visible]);

  // 模式切换处理函数
  const handleSmartAnalysis = () => {
    setDisplayMode(displayMode === 'analysis' ? 'requirements' : 'analysis');
  };

  const handleFormSubmission = () => {
    setDisplayMode(displayMode === 'form' ? 'requirements' : 'form');
  };

  // 表单提交处理
  const handleFormSubmit = (data: any) => {
    console.log('表单提交数据:', data);
    onSubmit({
      type: 'form',
      formData: data,
      capturedImages
    });
    onClose();
  };

  // 根据checkWay判断是否显示拍照上传按钮
  const shouldShowPhotoUpload = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(0);
  };

  // 根据checkWay判断是否显示语音识别按钮
  const shouldShowVoiceRecognition = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(1);
  };

  // 处理检查处所名称编辑
  const handleLocationEdit = () => {
    setIsEditingLocation(true);
  };

  // 处理检查处所名称保存
  const handleLocationSave = () => {
    setIsEditingLocation(false);
    apis.ginkgoSystem.updateCheckName({
      checkName: locationName,
      id: taskData.versions[0].id
    }).then(res => {
      console.log('更新检查处所名称 res:', res);
      Toast.show('更新成功');
    });
  };

  // 处理检查处所名称变化
  const handleLocationChange = (value: string) => {
    setLocationName(value);
  };



  // 处理确认按钮
  const handleConfirm = () => {
    // 根据当前模式执行不同的确认逻辑
    if (displayMode === 'form') {
      // 表单填报模式：触发表单提交
      // handleFormSubmit(taskData.versions[0].results);
      forwardRef.current.handleSubmit();
      Toast.show('提交成功');
      onClose();
      // 刷新详情
      onDataUpdate?.();
    } else {
      // 智能分析模式：提交分析结果
      const submitData = {
        locationName,
        analysisResults,
        voiceAnalysisResults,
        capturedImages: capturedImages.map(img => img.file),
        mode: displayMode
      };
      onSubmit(submitData);
      Toast.show('提交成功');
      onClose();
      // 刷新详情
      onDataUpdate?.();
    }
  };

  // 处理拍照上传
  const handlePhotoUpload = () => {
    console.log('拍照上传');
    setShowPhotoCapture(true);
    // 这里可以添加拍照上传逻辑
  };

  // 处理语音识别
  const handleVoiceRecognition = () => {
    console.log('语音识别');
    // 这里可以添加语音识别逻辑
    setShowVoiceInput(true);
  };


  // 处理关闭拍照上传
    // 删除图片
    const handleDeleteImage = (index: number) => {
      // 获取要删除的图片的ID
      const imageToDelete = capturedImages[index];
      if (imageToDelete && imageToDelete.ids) {
        // 从ImageIds中移除对应的ID
        const currentImageIds = ImageIds.split(',').filter(id => id.trim() !== '');
        const idsToRemove = imageToDelete.ids;
        const updatedImageIds = currentImageIds.filter(id => !idsToRemove.includes(id));
        setImageIds(updatedImageIds.join(','));
      }

      // 从图片列表中移除
      setCapturedImages(prev => prev.filter((_, i) => i !== index));

      // 如果没有图片了，重置状态
      if (capturedImages.length === 1) {
        setAnalysisResults([]);
      }
    };



  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理语音输入发送
  const handleVoiceSend = (speechText: string) => {
    console.log('语音识别文本:', speechText);
    const origin = window.location.origin;
    // 生成reqId 时间戳+随机4位数字
    const reqId = getReqId();
    const params = {
      type: 1,
      query: taskData.versions[0].id,
      executionId: JSON.parse(sessionStorage.getItem('prameData')).newId,
      curentPlaceId: sessionStorage.getItem('stageId'),
      curentPlaceName: sessionStorage.getItem('stageName'),
    }
    let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&question=${encodeURIComponent(speechText)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
    console.log('url', url);
    // 更新语音分析结果
    const eventSourceHandler = createEventSourceHandler({
      url,
      qaList: [],
      onMessage: (qaList: any[], answer: string) => {
        console.log('qaList', qaList);
        console.log('answer', answer);
      },
      onComplete: (qaList: any[], answerId: string, questionId: string) => {
        console.log('qaList', qaList);
        console.log('answerId', answerId);
        console.log('questionId', questionId);
        Toast.show('语音识别完成');
        onDataUpdate?.();
      },
      onError: (error: any) => {
        console.log('error', error);
      }
    })
    eventSourceHandler.start();
    setShowVoiceInput(false);
    Toast.show('语音识别完成');
  };

  const handleVoiceClose = () => {
    setShowVoiceInput(false);
  };

  // 处理拍照成功
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    Toast.show('正在分析图片...');
    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const ids = res.map((item: any) => item.id).join(',');
      const newImageIds = ImageIds?ImageIds+','+ids:ids;
      setImageIds(newImageIds);

      // 添加到图片列表，包含ID信息
      const newImage = { url: dataUrl, file, ids: res.map((item: any) => item.id) };
      setCapturedImages(prev => [...prev, newImage]);

      // 更新上传状态
      const origin = window.location.origin;
      // 生成reqId 时间戳+随机4位数字
      const reqId = getReqId();
      const params = {
        type: 0,
        query: taskData.versions[0].id,
        executionId: JSON.parse(sessionStorage.getItem('prameData')).newId,
        curentPlaceId: sessionStorage.getItem('stageId'),
        curentPlaceName: sessionStorage.getItem('stageName'),
      }
      let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&imageIds=${encodeURIComponent(newImageIds)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
      console.log('url', url);
      const eventSourceHandler = createEventSourceHandler({
        url,
        qaList: [],
        onMessage: (qaList: any[], answer: string) => {
          console.log('qaList', qaList);
          console.log('answer', answer);
        },
        onComplete: (qaList: any[], answerId: string, questionId: string) => {
          console.log('qaList', qaList);
          console.log('answerId', answerId);
          console.log('questionId', questionId);
          Toast.show('图片识别完成');
          onDataUpdate?.();
        },
        onError: (error: any) => {
          console.log('error', error);
        }
      })
      eventSourceHandler.start();
    });
  };

  return (
    <Popup
      visible={visible}
      onClose={onClose}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
        minHeight: '60vh',
        maxHeight: '80vh',
        padding: 0
      }}
      closeOnMaskClick={true}
      destroyOnClose={true}
    >
      <div className="new-inspection-modal">
        {/* 头部 */}
        <div className="modal-header">
          <h3 className="modal-title">新增巡检</h3>
          <div className="header-actions">
            <div className="header-tabs">
              <div
                className={`header-tab-btn header-tab-btn-left ${displayMode === 'analysis' ? 'active' : ''}`}
                onClick={handleSmartAnalysis}
              >
                智能分析
              </div>
              <div
                className={`header-tab-btn header-tab-btn-right ${displayMode === 'form' ? 'active' : ''}`}
                onClick={handleFormSubmission}
              >
                表单填报
              </div>
            </div>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="modal-content">
          {/* 检查处所名称 */}
          <div className="location-section">
            <div className="location-input">
              <span className="location-label">检查处所名称</span>
              {isEditingLocation ? (
                <div className="location-edit">
                  <Input
                    value={locationName}
                    onChange={handleLocationChange}
                    placeholder="请输入检查处所名称"
                    className="location-input-field"
                  />
                  <CheckOutline
                    className="location-save-icon"
                    onClick={handleLocationSave}
                  />
                </div>
              ) : (
                <div className="location-display">
                  <span className="location-value">{locationName || '未知检查项目'}</span>
                  <EditSOutline
                    className="location-edit-icon"
                    onClick={handleLocationEdit}
                  />
                </div>
              )}
            </div>
          </div>



          {/* 智能分析模式 */}
          {displayMode === 'analysis' && (
            <div className="analysis-mode">
              {/* 拍照上传检查项 (checkWay: 0) */}
              {requirements.filter(req => req.checkWay === 0).length > 0 && (
                <div className="check-section photo-section">
                  {/* <div className="section-header">
                    <span className="section-title">拍照上传检查项</span>
                  </div> */}

                  <div className="requirements-list">
                    {requirements.filter(req => req.checkWay === 0).map((req, index) => (
                      <div key={req.id} className="requirement-item">
                        <span className="requirement-number">{index + 1}.</span>
                        <span className="requirement-content">{req.content}</span>
                      </div>
                    ))}
                  </div>

                  {/* 显示已有的检查结果 */}
                  {(() => {
                    const photoResults = taskData?.versions?.[0]?.results?.filter((result: any) => result.checkWay === 0 && result.value) || [];
                    return photoResults.length > 0 && (
                      <div className="analysis-results">
                        <div className="results-header">检查结果</div>
                        {photoResults.map((result: any, index: number) => (
                          <div key={result.id} className="analysis-item">
                            <div className="analysis-header">
                              <div className="analysis-title">
                                <span className="analysis-number">{index + 1}.</span>
                                <span className="analysis-requirement">{result.config.reqName}</span>
                              </div>
                              <span className={`analysis-status ${result.isHazard === 1 ? 'hazard' : 'normal'}`}>
                                {result.isHazard === 1 ? '隐患' : '正常'}
                              </span>
                            </div>
                            <div className="analysis-result">{result.value}</div>
                          </div>
                        ))}
                      </div>
                    );
                  })()}

                  {/* 拍照分析结果 */}
                  {analysisResults.length > 0 && (
                    <div className="analysis-results">
                      <div className="results-header">AI分析结果</div>
                      {analysisResults.map((result, index) => (
                        <div key={result.id} className="analysis-item">
                          <div className="analysis-header">
                            <div className="analysis-title">
                              <span className="analysis-number">{index + 1}.</span>
                              <span className="analysis-requirement">{result.requirement}</span>
                            </div>
                          </div>
                          <div className="analysis-result">{result.result}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 显示拍摄的图片 */}
                  {capturedImages.length > 0 && (
                    <div className="uploaded-images">
                      {capturedImages.map((item, index) => (
                        <div key={index} className="image-item">
                          <img src={item.url} alt={`拍摄图片${index + 1}`} />
                          <div className="image-actions">
                            <div className="delete-overlay" onClick={() => handleDeleteImage(index)}>
                              <CloseOutline />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 拍照上传按钮 */}
                  {shouldShowPhotoUpload() && (
                    <div className="section-actions">
                      <Button
                        className="action-btn photo-btn"
                        block
                        onClick={handlePhotoUpload}
                      >
                        <div className="photo-btn-icon">
                        </div> 拍照上传
                      </Button>
                    </div>
                  )}
                </div>
              )}

              {/* 语音识别检查项 (checkWay: 1) */}
              {requirements.filter(req => req.checkWay === 1).length > 0 && (
                <div className="check-section voice-section">
                  {/* <div className="section-header">
                    <span className="section-title">语音识别检查项</span>
                  </div> */}

                  <div className="requirements-list">
                    {requirements.filter(req => req.checkWay === 1).map((req, index) => (
                      <div key={req.id} className="requirement-item">
                        <span className="requirement-number">{index + 1}.</span>
                        <span className="requirement-content">{req.content}</span>
                      </div>
                    ))}
                  </div>

                  {/* 显示已有的检查结果 */}
                  {(() => {
                    const voiceResults = taskData?.versions?.[0]?.results?.filter((result: any) => result.checkWay === 1 && result.value) || [];
                    return voiceResults.length > 0 && (
                      <div className="analysis-results">
                        <div className="results-header">检查结果</div>
                        {voiceResults.map((result: any, index: number) => (
                          <div key={result.id} className="analysis-item">
                            <div className="analysis-header">
                              <div className="analysis-title">
                                <span className="analysis-number">{index + 1}.</span>
                                <span className="analysis-requirement">{result.config.reqName}</span>
                              </div>
                              <span className={`analysis-status ${result.isHazard === 1 ? 'hazard' : 'normal'}`}>
                                {result.isHazard === 1 ? '隐患' : '正常'}
                              </span>
                            </div>
                            <div className="analysis-result">{result.value}</div>
                          </div>
                        ))}
                      </div>
                    );
                  })()}

                  {/* 语音分析结果 */}
                  {voiceAnalysisResults.length > 0 && (
                    <div className="analysis-results">
                      <div className="results-header">AI语音分析结果</div>
                      {voiceAnalysisResults.map((result, index) => (
                        <div key={result.id} className="analysis-item">
                          <div className="analysis-header">
                            <div className="analysis-title">
                              <span className="analysis-number">{index + 1}.</span>
                              <span className="analysis-requirement">{result.requirement}</span>
                            </div>
                          </div>
                          <div className="analysis-result">{result.result}</div>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* 语音识别按钮 */}
                  {shouldShowVoiceRecognition() && (
                    <div className="section-actions">
                      <Button
                        className="action-btn voice-btn"
                        block
                        onClick={handleVoiceRecognition}
                      >
                        <div className="voice-btn-icon">
                        </div> 语音识别
                      </Button>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* 表单填报模式 */}
          {displayMode === 'form' && (
            <FormSubmission
              formItems={taskData.versions[0].results}
              onSubmit={handleFormSubmit}
              ref={forwardRef}
            />
            )}

          {/* 底部按钮 */}
          <div className="modal-footer">
            <Button
              className="cancel-btn"
              onClick={onClose}
              fill="outline"
            >
              取消
            </Button>
            <Button
              className="confirm-btn"
              onClick={handleConfirm}
              color="primary"
            >
              确认
            </Button>
          </div>
        </div>
      </div>

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />
      {/* 语音输入组件 */}
      {showVoiceInput && (
        <div className="voice-input-overlay">
          <SpeechRecognitionContainer
            onSend={handleVoiceSend}
            onClose={handleVoiceClose}
          />
        </div>
      )}
      
    </Popup>
  );
};

export default NewInspectionModal;
