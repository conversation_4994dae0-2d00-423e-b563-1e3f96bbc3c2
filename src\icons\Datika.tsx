import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Datika(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        > 
          <g><defs><clipPath id="14b120__a"><rect x="3" y="3" width="12" height="12" rx="0"/></clipPath></defs><rect width="18" height="18" rx="4" fill="#E4940A"/><g clipPath="url(#14b120__a)"><path d="M12.678 3.78h-7.35c-.858 0-1.548.69-1.548 1.547v7.35c0 .852.69 1.548 1.548 1.548h7.35c.852 0 1.548-.69 1.548-1.548v-7.35a1.552 1.552 0 0 0-1.548-1.548Zm-5.982 8.645h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.558.558 0 0 1-.558.564Zm0-2.862h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.558.558 0 0 1-.558.564Zm0-2.856h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.555.555 0 0 1-.558.564Zm2.73 5.718h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.558.558 0 0 1-.558.564Zm0-2.862h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.558.558 0 0 1-.558.564Zm0-2.856h-.852a.563.563 0 1 1 0-1.128h.846c.312 0 .564.252.564.564a.555.555 0 0 1-.558.564Zm2.73 5.718h-.846a.563.563 0 1 1 0-1.128h.846a.563.563 0 1 1 0 1.128Zm0-2.862h-.846a.563.563 0 1 1 0-1.128h.846a.563.563 0 1 1 0 1.128Zm0-2.856h-.846a.563.563 0 1 1 0-1.128h.846a.563.563 0 1 1 0 1.128Z" fill="#FFF" /></g></g>
        </svg>
    )
}
