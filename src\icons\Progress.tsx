/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2025-01-06 04:04:36
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-01-17 16:05:28
 * @FilePath: \note-exam\src\icons\Progress.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Progress(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aeec__a"><rect width="18" height="18" rx="0"/></clipPath></defs><g clipPath="url(#14aeec__a)"><path data-follow-fill="#575B66" d="M2.32 2.847v2.52c0 .212.174.382.39.382h12.364a.386.386 0 0 0 .388-.382v-2.52a.386.386 0 0 0-.388-.383H2.71a.385.385 0 0 0-.39.383Zm15.662 4.846a.386.386 0 0 0-.39-.382H2.71a.388.388 0 0 0-.39.382v2.52c0 .211.174.383.39.383h14.884c.216 0 .39-.172.39-.383v-2.52h-.002Zm-5.468 4.85a.386.386 0 0 0-.388-.384H2.71a.388.388 0 0 0-.39.383v2.52c0 .21.174.383.39.383h9.416a.386.386 0 0 0 .388-.383v-2.52Zm-11.218 4.81V.647A.643.643 0 0 0 .648.01.643.643 0 0 0 0 .646v16.708c0 .353.29.637.648.637a.642.642 0 0 0 .648-.637Z" fill={_fill}/></g></g>
        </svg>
    )
}
