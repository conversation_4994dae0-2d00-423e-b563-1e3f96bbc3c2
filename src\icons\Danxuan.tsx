import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Danxuan(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 17 17"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="150ff4__a"><rect rx="0" height="17" width="17"/></clipPath></defs><g clipPath="url(#150ff4__a)"><path d="M8.485 1.058a7.438 7.438 0 1 0 .001 14.876 7.438 7.438 0 0 0 0-14.876Zm4.669 6.217-4.262 4.262a.532.532 0 0 1-.752 0l-4.247-4.25a.532.532 0 0 1 .752-.753l3.498 3.498a.532.532 0 0 0 .752 0l3.51-3.51a.532.532 0 0 1 .752 0 .535.535 0 0 1-.003.753Z" data-follow-fill="#70A4FF" fill={_fill}/></g></g>
        </svg>
    )
}
