/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2024-12-05 10:01:21
 * @FilePath: \react-h5-template\src\store\user.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {create} from 'zustand'
import {persist, createJSONStorage} from 'zustand/middleware'

export const useUserStore = create<UserState>()(
    persist(
        (set) => ({
            user: {
                name: '',
                avatar: '',
                email: ''
            },
            setUser: (user: User) => set({user}),
            clearUser: () => set({
                user: null
            }),
        }),
        {
            name: 'user-storage',
            storage: createJSONStorage(() => localStorage)
        },
    ),
)