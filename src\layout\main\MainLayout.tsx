/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-13 18:06:53
 * @FilePath: \react-h5-template\src\layout\main\MainLayout.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React from 'react';
import {TabBar} from 'antd-mobile';
import {useNavigate, useLocation} from 'react-router-dom';
import useRouteName from '@/route/useRouteMatch';
import Home from "@/icons/Home";
import Progress from '@/icons/Progress';
import Subjects from '@/icons/Subjects';
import Error from '@/icons/Error';
import {urlName} from '@/util/method.ts';
import './index.less';

interface MainLayoutProps {
    children: React.ReactNode;
    goBack?: () => void; // 添加 goBack 方法的类型
    titleName?: string; // 添加 titleName 的类型
    noHeader?: boolean; // 添加 noHeader 的类型
    noBank?: boolean; // 添加 noBank 的类型
}


const MainLayout: React.FC<MainLayoutProps> = ({children,goBack,noHeader=true,noBank=false}) => {
    const routeName = useRouteName(); // 获取当前路由名称
    const handleGoBack = goBack || (() => navigate(urlName,{ replace: true }));
    const name = '注安考试'; // 如果没有传入 titleName 则使用当前路由名称
    const navigate = useNavigate();
    const location = useLocation();
    const {pathname} = location;
    const [activeKey, setActiveKey] = React.useState(pathname);


    const getTabProps = (key:string, title:string, IconComponent:any) => {
        const isActive = activeKey === key;
        const color = isActive ? '#4B75FC' : '#333';
        const icon = <IconComponent size="21" fill={color} />;
        
        return { key, title, color, icon };
    };
    
    const tabs = [
        getTabProps(urlName, '首页', Home),
        getTabProps(urlName+'/subjects', '学习科目', Subjects),
        getTabProps(urlName+'/progress', '学习进度', Progress),
        getTabProps(urlName+'/mistakes', '错题巩固', Error),
    ];

    const setRouteActive = (value: string) => {
        setActiveKey(value);
        navigate(value);
    };

    const jumpUser = () => {
        console.log('跳转到用户中心');  // 跳转到用户中心
    }
    const goBackList = () => {
        console.log('返回上一页');  // 返回
        handleGoBack();
    }

    return (
        <div className={ `${noHeader?'main-layout':'main-layout-noHeader'}` }>
            <div style={{display:noHeader?'flex':'none'}} className="header layout-header">
                <span className='goBackBox' onClick={goBackList} style={{display:noBank?'none':'flex'}}>
                    {/* <ListTwo theme="outline" size="21" fill="#333"/> */}
                    <i className='ListTwo'></i>
                </span>
                <div className='titleBox'>
                    <p className='titleCC' >{name}</p>
                     {/* 先注释等待后续开发 */}
                    {/* <Shuoming size="18" style={{marginLeft:'10px'}}></Shuoming> */}
                </div>
                {/* 先注释等待后续开发 */}
                {/* <UserConf size="24" className='icon-mine' onClick={jumpUser}></UserConf> */}
            </div>
            <div className="content layout-content">
                {children}
            </div>
            <div className="footer layout-tab">
                <TabBar activeKey={activeKey} onChange={setRouteActive}>
                    {tabs.map(item => (
                        <TabBar.Item key={item.key} icon={item.icon} title={item.title} style={
                            {
                                color: item.color,
                            }
                        }/>
                    ))}
                </TabBar>
            </div>
        </div>
    );
};

export default MainLayout;
