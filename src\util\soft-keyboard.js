/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-23 20:47:35
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-23 21:07:14
 * @FilePath: src/util/soft-keyboard.js
 * @Version: 1.0.0
 * @Description: 组件描述 软键盘弹起和收起判断工具
 */
const differenceRange = 20;

const originalHeight = document.documentElement.clientHeight || document.body.clientHeight;
const onResizeHandler = (callback) => {
	// message.info(`onResizeHandler called: ${callback}`);
	//键盘弹起与隐藏都会引起窗口的高度发生变化
	const resizeHeight = document.documentElement.clientHeight || document.body.clientHeight;
	// alert(`判断: ${originalHeight} - ${resizeHeight}`);
	/*if (resizeHeight < originalHeight) {
		//当软键盘弹起，在此处操作
		callback && callback(true);
	} else {
		//当软键盘收起，在此处操作
		callback && callback(false);
	}*/
	if (Math.abs(originalHeight - resizeHeight) < differenceRange) {
		//当软键盘收起，在此处操作
		callback && callback(false);
	} else {
		//当软键盘弹起，在此处操作
		callback && callback(true);
	}
};

const onFocusInHandler = (callback) => {
	callback && callback(true);
};

const onFocusOutHandler = (callback) => {
	callback && callback(false);
};

const monitorSoftKeyboard = (callback) => {
	// Android系统
	window.removeEventListener('resize', onResizeHandler.bind(this, callback));
	window.addEventListener('resize', onResizeHandler.bind(this, callback));
	// IOS系统
	//软键盘弹出的事件处理
	document.body.removeEventListener('focusin', onFocusInHandler.bind(this, callback));
	document.body.addEventListener('focusin', onFocusInHandler.bind(this, callback));
	//软键盘收起的事件处理
	document.body.removeEventListener('focusout', onFocusOutHandler.bind(this, callback));
	document.body.addEventListener('focusout', onFocusOutHandler.bind(this, callback));
};

export {
	monitorSoftKeyboard,
};
