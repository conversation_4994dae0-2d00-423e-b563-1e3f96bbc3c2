.supplement-attachment-popup {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;

  .popup-header {
    padding: 20px 20px 16px;
    text-align: center;
    position: relative;
    background: #fff;

    h3 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1a1a1a;
      text-align: left;
    }
  }

  .popup-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    max-height: calc(80vh - 120px);

    .record-item {
      margin-bottom: 16px;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      background: linear-gradient( 90deg, #EBF3F9 0%,#EBF3F9 20%, #fff 100%);
      border: 1px solid rgba(72, 115, 255, 0.1);
      box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
      border-radius: 9px;

      &:last-child {
        margin-bottom: 0;
      }

      .record-info {
        margin-bottom: 16px;

        .record-title {
          font-size: 15px;
          font-weight: 600;
          color: #1a1a1a;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .record-requirement {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          // background: #f8f9fa;
          border-radius: 8px;
        }
      }

      .upload-section {
        .upload-btn {
          width: 88px;
          height: 68px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          margin-bottom: 16px;
          background: linear-gradient(135deg, #f0f4ff 0%, #e8f0ff 100%);
          border-radius: 12px;
          border: 1px dashed #4873ff;
          position: relative;

          &:active {
            transform: scale(0.98);
            border-color: #3461e6;
            background: linear-gradient(135deg, #e8f0ff 0%, #dde8ff 100%);
          }
          
          .upload-btn-icon {
            width: 24px;
            height: 24px;
            background: url('../../assets/images/icon_camera.png') no-repeat center center;
            background-size: 100% 100%;
          }

          .antd-mobile-icon {
            font-size: 24px;
            color: #4873ff;
            margin-bottom: 4px;
          }

          span {
            font-size: 12px;
            color: #4873ff;
            font-weight: 500;
          }
        }

        .image-preview-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .image-preview-item {
            position: relative;
            width: 60px;
            height: 60px;
            border-radius: 4px;
            border: 1px solid #d9d9d9;

            .preview-image {
              width: 100%;
              height: 100%;
              object-fit: cover;
              cursor: pointer;
            }

            .delete-btn {
              position: absolute;
              top: -4px;
              right: -4px;
              width: 16px;
              height: 16px;
              background: #ff4d4f;
              color: white;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              cursor: pointer;
              line-height: 1;
              border: 1px solid white;
            }
          }
        }
      }
    }
  }

  .popup-footer {
    padding: 20px;
    display: flex;
    gap: 16px;
    position: sticky;
    bottom: 0;
    background: #fff;
    border-top: 1px solid #f0f0f0;

    .cancel-btn,
    .confirm-btn {
      flex: 1;
      height: 48px;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 600;
      transition: all 0.3s ease;
      border: none;
    }

    .cancel-btn {
      background: rgba(217, 225, 248, 1);
      color: rgba(72, 115, 255, 1);

      &:active {
        background: rgba(217, 225, 248, 1);
        transform: scale(0.98);
      }
    }

    .confirm-btn {
      background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
      border-radius: 22px 22px 22px 22px;

      &:active {
        background: linear-gradient(135deg, #3461e6 0%, #4a6bff 100%);
        transform: scale(0.98);
        box-shadow: 0 2px 8px rgba(72, 115, 255, 0.4);
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .supplement-attachment-popup {
    .popup-content {
      padding: 12px 16px;

      .record-item {
        padding: 12px;
        margin-bottom: 16px;

        .upload-section {
          .upload-btn {
            width: 70px;
            height: 50px;
          }

          .image-preview-list {
            .image-preview-item {
              width: 50px;
              height: 50px;
            }
          }
        }
      }
    }

    .popup-footer {
      padding: 12px 16px;
    }
  }
}
