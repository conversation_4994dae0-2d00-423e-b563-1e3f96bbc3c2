<!--
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-19 10:54:25
 * @FilePath: \react-h5-template\index.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no,viewport-fit=cover">
    <title>AI巡检</title>
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
    <script src="/monitor.umd.js"></script>
    <script src="/dist/index.umd.js"></script>
    <script>
        const convertStyle = () => {
            document.body.style.setProperty('height', `${window.innerHeight}px`);
        }
        window.addEventListener("resize", convertStyle);
        window.addEventListener("DOMContentLoaded", convertStyle);
    </script>
  </body>
</html>
