/*
 * @Description: 任务详情页面 - 巡检执行界面
 * @Author: AI Assistant
 * @Date: 2025-06-26
 */
import React, { useState,useEffect,useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Popup, Input } from 'antd-mobile';
import { LeftOutline, SoundOutline, CameraOutline, CloseOutline } from 'antd-mobile-icons';
import './TaskDetail.less';
import PhotoCapture from '@/components/PhotoCapture';
import { SpeechRecognitionContainer } from '@/containers';
import InlineChat from '@/components/ChatDialog/InlineChat';
import { apis } from '@/api/api';
import { Toast } from 'antd-mobile';
import { getReqId, createEventSourceHandler, beforeUpload } from '@/util/method';
import { urlName,getUrlParameter } from '@/util/method';
// 检查项目接口
interface CheckItem {
  id: string;
  name: string;
  requirements: number;
  checked: number;
  issues: number;
  status: 'pending' | 'completed';
}

// 巡检阶段接口
interface InspectionStage {
  id: string;
  name: string;
  status: 'pending' | 'current' | 'completed';
}

// 对话消息接口
interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
}

const TaskDetail: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const stageIdOld = getUrlParameter('stageId');
  const [taskData, setTaskData] = useState<any>({});
  // 巡检阶段数据
  const [stages,setStages] = useState<InspectionStage[]>([
  ]);

  const [currentStage, setCurrentStage] = useState<InspectionStage | null>(null);

  // 当前模式
  const [currentMode, setCurrentMode] = useState<'fill' | 'chat'>('fill');

  // 对话模式相关状态
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatLoading, setChatLoading] = useState(false);
  const [chatCurrentStep, setChatCurrentStep] = useState(0);
  const inlineChatRef = useRef<any>(null); // 对话模式组件
  // 检查项目数据
  const [checkItems, setCheckItems] = useState<any[]>([
  ]); // 检查项目列表

  // 语音输入相关状态
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [voiceAnalysisResults, setVoiceAnalysisResults] = useState<string[]>([]);

  // 拍照相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [capturedImages, setCapturedImages] = useState<Array<{url: string, file: File}>>([]);
  const [analysisResults, setAnalysisResults] = useState<string[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [clickItem, setClickItem] = useState<any>(null);

  // 编辑任务名称相关状态
  const [showEditTaskNameModal, setShowEditTaskNameModal] = useState(false);
  const [editingTaskName, setEditingTaskName] = useState('');

  // 图片上传相关状态
  useEffect(() => {
    const callBack = getUrlParameter('callBack');
    if(callBack) {
      sessionStorage.setItem('callBack', callBack);
    }
  }, [capturedImages]);

  // useEffect(() => {
  //   if(stageId&&taskData?.placeList) {
  //     handleStageClick(stageId,taskData);
  //   }
  // }, [stageId,taskData]);

  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理拍照成功
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    console.log('拍照结果:', file, dataUrl);

    // 添加到图片列表
    const newImage = { url: dataUrl, file };
    setCapturedImages(prev => [...prev, newImage]);

    // 关闭拍照界面
    setShowPhotoCapture(false);

    // 开始分析
    setIsAnalyzing(true);
    Toast.show('正在分析图片...');

    // 模拟AI分析
    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const ids = res.map((item: any) => item.id).join(',');

      // 更新上传状态
      const origin = window.location.origin;
      // 生成reqId 时间戳+随机4位数字
      const reqId = getReqId();
      const params = {
        type: 0,
        query: clickItem.versions[0].id,
        executionId: JSON.parse(sessionStorage.getItem('prameData')).newId,
        curentPlaceId: sessionStorage.getItem('stageId'),
        curentPlaceName: sessionStorage.getItem('stageName'),
      }
      let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${clickItem.versions[0].results[0].businessId}&imageIds=${encodeURIComponent(ids)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
      console.log('url', url);
      // 更新语音分析结果
      const eventSourceHandler = createEventSourceHandler({
        url,
        qaList: [],
        onMessage: (qaList: any[], answer: string) => {
          console.log('qaList', qaList);
          console.log('answer', answer);
        },
        onComplete: (qaList: any[], answerId: string, questionId: string) => {
          console.log('qaList', qaList);
          console.log('answerId', answerId);
          console.log('questionId', questionId);
          Toast.show('图片识别完成');
          updateTaskData(false);
          setTimeout(() => {
            handleCheckItemClick(clickItem);
          }, 300);
        },
        onError: (error: any) => {
          console.log('error', error);
        }
      })
      eventSourceHandler.start();
    });
  };

  // 处理语音输入发送
  const handleVoiceSend = (speechText: string) => {
    console.log('语音识别文本:', speechText);

    // 关闭语音输入界面
    setShowVoiceInput(false);

    if (!speechText.trim()) {
      Toast.show('请说话后再发送');
      return;
    }

    // 开始分析
    setIsAnalyzing(true);
    Toast.show('正在分析语音内容...');
    // 如果有当前阶段和任务数据，进行AI分析
    if (currentStage && clickItem) {
      const origin = window.location.origin;
      const reqId = getReqId();

      
      // 构建分析URL - 使用当前阶段的第一个检查项目的businessId
      const firstCheckItem = clickItem;
      if (firstCheckItem && firstCheckItem.versions[0].results[0].businessId) {
        const params = {
          type: 1,
          query: firstCheckItem.versions[0].id,
          executionId: JSON.parse(sessionStorage.getItem('prameData')).newId,
          curentPlaceId: sessionStorage.getItem('stageId'),
          curentPlaceName: sessionStorage.getItem('stageName'),
        }
        const url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${firstCheckItem.versions[0].results[0].businessId}&question=${encodeURIComponent(speechText)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;

        // 使用EventSource处理实时响应
        const eventSourceHandler = createEventSourceHandler({
          url,
          qaList: [],
          onMessage: (_list: any[], answer: string) => {
            // 处理接收到的数据
            if (answer && answer.trim()) {
              setVoiceAnalysisResults(prev => [...prev, answer]);
            }
          },
          onComplete: (qaList: any[], answerId: string, questionId: string) => {
            // 完成回调
            updateTaskData(false);
              Toast.show('语音分析完成');
            setTimeout(() => {
              handleCheckItemClick(clickItem);
            }, 300);
            // setIsAnalyzing(false);
           
          },
          onError: (error: any) => {
            // 错误回调
            // console.error('语音分析错误:', error);
            // Toast.show('语音分析失败，请重试');
          }
        });

        // 启动EventSource
        eventSourceHandler.start();
      } else {
        // 如果没有检查项目数据，显示模拟分析结果
        setTimeout(() => {
          const mockAnalysis = [
            `语音内容："${speechText}"`,
            '分析结果：根据描述，建议重点检查相关设备状态',
            '请配合拍照记录现场情况'
          ];
          setVoiceAnalysisResults(prev => [...prev, ...mockAnalysis]);
          setIsAnalyzing(false);
          Toast.show('语音分析完成');
        }, 1500);
      }
    } else {
      // 没有任务数据时的处理
      setTimeout(() => {
        const mockAnalysis = [
          `语音内容："${speechText}"`,
          '请先选择检查阶段后再进行语音分析'
        ];
        setVoiceAnalysisResults(prev => [...prev, ...mockAnalysis]);
        setIsAnalyzing(false);
      }, 1000);
    }
  };

  const handleVoiceClose = () => {
    setShowVoiceInput(false);
  };


  // 清除所有分析结果
  const handleClearResults = () => {
    setAnalysisResults([]);
    setVoiceAnalysisResults([]);
    setCapturedImages([]);
    Toast.show('已清除所有分析结果');
  };
  

  const handleBack = (isCompleted:boolean = false) => {
    const callBack = sessionStorage.getItem('callBack');
    if(callBack) {
      window.location.href = decodeURIComponent(callBack)+'?type='+(isCompleted?'completed':'return');
      sessionStorage.removeItem('callBack');
    }else{
      navigate(`${urlName}/task-list/${JSON.parse(sessionStorage.getItem('prameData') || '{}').taskCode}`);
    }
  };

  const handleCheckItemClick = (item: any) => {
    console.log('点击检查项目:', item,checkItems);
    // 跳转到检查项目详情页
    sessionStorage.setItem('checkItem', JSON.stringify(item));

    if(item.checkCount === 0) {
      navigate(`${urlName}/check-item/${item.configItemId}`);
    } else {
      navigate(`${urlName}/inspection-record/${item.configItemId}`);
    }
  };

  const handleStageClick = (stageId: string,newTaskData:any) => {
    console.log('点击阶段:', stageId);
    // 从taskData中查找对应的place数据
    sessionStorage.setItem('stageId', stageId);
    const placeData = newTaskData.placeList?.find((item: any) => item.id == stageId);
    sessionStorage.setItem('prameData', JSON.stringify({newId:id,taskCode:newTaskData.taskCode}));
    sessionStorage.setItem('placeData', JSON.stringify(placeData));
    sessionStorage.setItem('stageName', placeData.cateName);

    if (placeData) {
      // 创建stage对象
      const selectedStage: InspectionStage = {
        id: placeData.id,
        name: placeData.cateName,
        status: placeData.status ? 'completed' as const : 'pending' as const,
      };
      setCurrentStage(selectedStage);
      let checkWay = [];
      placeData.itemRespVOList.forEach((item:any) => {
        if(item.status === 0) {
          checkWay.push(item);
        }
      });
      placeData.itemRespVOList.forEach((item:any) => {
        if(item.status === 1) {
          checkWay.push(item);
        }
      });
      
      setCheckItems(checkWay);
      console.log('checkItems', placeData.itemRespVOList);

      // 如果对话模式开启，自动发送选中cateName的询问
      if (currentMode === 'chat' && inlineChatRef.current && placeData.cateName) {
        const initMessage = `${placeData.cateName}`;
        inlineChatRef.current.sendToAI(initMessage, 4); // type=4 表示初始化询问
      }
    }
  };

  const handleVoiceClick = (event: React.MouseEvent<HTMLButtonElement>,item:any) => {
    // 阻止默认行为
    event.stopPropagation();
    console.log('点击语音');
    setShowVoiceInput(true);
    setClickItem(item);
  };

  const handleCameraClick = (event: React.MouseEvent<HTMLButtonElement>,item:any) => {
    // 阻止默认行为
    event.stopPropagation();
    console.log('点击相机');
    setShowPhotoCapture(true);
    setClickItem(item);
  };

  const handlePatrolBtnClick = () => {
    if(taskData.status === 1) {
      Toast.show('任务已完成');
      return;
    }
    console.log('完成');
    apis.ginkgoSystem.completeTask({ taskCode: taskData.taskCode,id:taskData.id }).then((res) => {
      console.log('res', res);
      if(res.code === 0) {
        Toast.show('巡查完成');
        setTimeout(() => {
          handleBack(true);
        }, 1000);
      }
    });
  };


  useEffect(() => {
    updateTaskData(true);
  }, []);

  const updateTaskData = (first:boolean = false) => {
    const taskCode = getUrlParameter('taskCode');
    console.log('taskCode', taskCode);
    apis.ginkgoSystem.getDetail({ taskCode,id }).then((res) => {
      console.log('res', res);
      // 使用函数形式确保taskData已更新
      if(res.code === 0) {
        setTaskData((_prevTaskData: any) => {
          const newTaskData = res.data;
          const newStages = newTaskData?.placeList.map((item:any) => {
            return {
              id: item.id,
              name: item.cateName,
              status: item.status?'completed':'pending'
            }
          });
          setStages(newStages);
          sessionStorage.setItem('taskData', JSON.stringify(newTaskData?.placeList));
          // 在taskData更新后，使用新数据调用handleStageClick
          if (newTaskData?.placeList && newTaskData?.placeList.length > 0 && first) {
            // 直接处理第一个stage的逻辑
            const firstPlace = newTaskData.placeList[0];
            const selectedStage: InspectionStage = {
              id: firstPlace.id,
              name: firstPlace.cateName,
              status: firstPlace.status ? 'completed' as const : 'pending' as const
            };
            setCurrentStage(selectedStage);

            setTimeout(() => {
              if(!stageIdOld){
                handleStageClick(firstPlace.id,newTaskData);

              }else{
                handleStageClick(stageIdOld,newTaskData);
              }
            }, 0);
          }
          

          return newTaskData;
        })
      }else{
        setTimeout(() => {
          handleBack(true);
        }, 1000);
      }
    });
  }

  const handleProgressInfoClick = () => {
    console.log('点击完成度');
    navigate(`${urlName}/progress-info`);
  }

  // 处理点击任务标题
  const handleTaskTitleClick = () => {
    setEditingTaskName(taskData.executeTaskName || '');
    setShowEditTaskNameModal(true);
  };

  // 处理任务名称编辑确认
  const handleTaskNameConfirm = () => {
    if (!editingTaskName.trim()) {
      Toast.show('任务名称不能为空');
      return;
    }

    apis.ginkgoSystem.updateTaskName({
      executeTaskName: editingTaskName.trim(),
      id: taskData.id
    }).then(res => {
      console.log('更新任务名称 res:', res);
      if (res.code === 0) {
        Toast.show('任务名称更新成功');
        setShowEditTaskNameModal(false);
        // 更新本地数据
        setTaskData(prev => ({
          ...prev,
          executeTaskName: editingTaskName.trim()
        }));
      } else {
        Toast.show(res.message || '更新失败');
      }
    }).catch(error => {
      console.error('更新任务名称失败:', error);
      Toast.show('更新失败，请重试');
    });
  };

  // 处理任务名称编辑取消
  const handleTaskNameCancel = () => {
    setShowEditTaskNameModal(false);
    setEditingTaskName('');
  };



  return (
    <div className="task-detail-container">
      {/* 头部 */}
      <header className="task-detail-header">
        <LeftOutline
          className="back-icon"
          onClick={() => handleBack(false)}
        />
        <div className="header-content">
          <h1 className="page-title" onClick={handleTaskTitleClick}>{taskData.executeTaskName}</h1>
        </div>
        <Button className={`patrol-btn ${taskData.status === 1 ? 'disabled' : ''}`} size="small" onClick={handlePatrolBtnClick}>
          完成
        </Button>
      </header>

      {/* 模式切换 */}
      <div className="mode-tabs">
        <div
          className={`tab-item ${currentMode === 'fill' ? 'active' : ''}`}
          onClick={() => setCurrentMode('fill')}
        >
          填报模式
        </div>
        <div
          className={`tab-item ${currentMode === 'chat' ? 'active' : ''}`}
          onClick={() => {
            setCurrentMode('chat');
            // 初始化对话
            if (chatMessages.length === 0) {
              const initialMessage: ChatMessage = {
                id: '1',
                type: 'assistant' as const,
                content: `您好！我是智能巡检助手。当前正在进行${taskData?.cateName || '巡检'}任务，请告诉我您遇到的问题或需要帮助的地方。`,
                timestamp: Date.now()
              };
              setChatMessages([initialMessage]);
            }
            const placeData = taskData.placeList?.find((item: any) => item.id === currentStage?.id);
            const initMessage = `${placeData.cateName}`;
            setTimeout(() => {
              inlineChatRef.current.sendToAI(initMessage, 4); // type=4 表示初始化询问
            }, 300);
          }}
        >
          对话模式
        </div>
      </div>

      <div className="progress-info" onClick={() => handleProgressInfoClick()}>
          <span className="progress-text">完成度</span>
          <span className="progress-percent">{taskData.completion}%</span>
          <span className="progress-look">查看巡查表</span>
      </div>
      {/* 进度条和阶段 */}
      <div className="progress-section">
        <div className="stages-container">
          {stages.map((stage) => (
            <div
              key={stage.id}
              className={`stage-item ${stage.status} ${currentStage?.id === stage.id ? 'active' : ''}`}
              onClick={() => handleStageClick(stage.id,taskData)}
            >
              <div className="stage-dot">
                {stage.status === 'completed' && <span className="check-mark"></span>}
                {stage.status === 'current' && <span className="current-dot"></span>}
                {stage.status === 'pending' && <span className="pending-dot"></span>}
              </div>
              <span className="stage-name">{stage.name}</span>
            </div>
          ))}
        </div>
        
      </div>

      {/* 检查项目列表 */}
      {currentMode === 'fill' && <div className="check-items-container">
        {checkItems?.map((item) => (
          <div
            key={item.configItemId}
            className="check-item"
            onClick={() => handleCheckItemClick(item)}
          >
            <div className="item-header">
              <div className={`status-badge ${item.status===0?'pending':'completed'}`}>
                {item.status === 0 ? '未完成' : '已完成'}
              </div>
              <span className="item-name">{item.cateName}</span>
              <span className="requirements-count">{item.requirementCount}条检查要求</span>
              <span className="arrow">›</span>
            </div>
            <div className="item-stats">
              <span className="stats-text">
                已检查 {item.checkCount} 处，发现隐患 {item.hazardCount} 个
              </span>
              <div className="action-buttons">
                {item.checkWay?.includes(1) && <Button
                  className="voice-btn"
                  size="mini"
                  fill="outline"
                  onClick={(event) => handleVoiceClick(event,item)}
                >
                    <span className="voice-btn-icon"></span>
                </Button>}
                {item.checkWay?.includes(0) && <Button
                  className="camera-btn"
                  size="mini"
                  fill="outline"
                  onClick={(event) => handleCameraClick(event,item)}
                >
                    <span className="camera-btn-icon"></span>
                </Button>}
              </div>
            </div>
          </div>
        ))}
      </div>}

      {/* 对话模式组件 */}
      {currentMode === 'chat' && (
        <div className="chat-container">
          <InlineChat
            ref={inlineChatRef}
            currentStep={chatCurrentStep}
            messages={chatMessages}
            loading={chatLoading}
            taskData={taskData.placeList?.find((item: any) => item.id === currentStage?.id)}
            onLoad={updateTaskData}
          />
        </div>
      )}
      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 语音输入组件 */}
      {showVoiceInput && (
        <div className="voice-input-overlay">
          <SpeechRecognitionContainer
            onSend={handleVoiceSend}
            onClose={handleVoiceClose}
          />
        </div>
      )}

      {/* 编辑任务名称弹框 */}
      <Popup
        visible={showEditTaskNameModal}
        onMaskClick={handleTaskNameCancel}
        bodyStyle={{
          padding: 0,
          background: 'transparent',
          borderRadius: 0,
        }}
        className="edit-task-name-popup-body"
      >
        <div
          className="edit-task-name-modal"
          style={{
            background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
            borderRadius: '24px 24px 0 0',
            width: '100%',
            maxWidth: '440px',
            boxShadow: '0 -12px 40px rgba(0, 0, 0, 0.15), 0 -4px 16px rgba(0, 0, 0, 0.08), 0 0 0 1px rgba(255, 255, 255, 0.1)',
            position: 'relative',
            backdropFilter: 'blur(20px)',
            overflow: 'hidden',
            minHeight: '280px',
            margin: '0 auto'
          }}
        >
          {/* 顶部装饰指示条 */}
          <div
            style={{
              position: 'absolute',
              top: '16px',
              left: '50%',
              transform: 'translateX(-50%)',
              width: '48px',
              height: '5px',
              background: 'linear-gradient(90deg, #e2e8f0 0%, #cbd5e1 50%, #94a3b8 100%)',
              borderRadius: '3px',
              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
              zIndex: 10
            }}
          />
          <div
            className="modal-header"
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '32px 32px 24px',
              borderBottom: '2px solid transparent',
              background: 'linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.9) 100%)',
              position: 'relative',
              zIndex: 2,
              backdropFilter: 'blur(10px)'
            }}
          >
            <span
              className="modal-title"
              style={{
                fontSize: '22px',
                fontWeight: '800',
                color: 'transparent',
                letterSpacing: '-0.04em',
                background: 'linear-gradient(135deg, #1e293b 0%, #475569 30%, #4f46e5 70%, #7c3aed 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                backgroundClip: 'text',
                position: 'relative',
                zIndex: 1
              }}
            >
              编辑任务名称
            </span>
            {/* <CloseOutline
              className="close-icon"
              onClick={handleTaskNameCancel}
              style={{
                fontSize: '26px',
                color: '#64748b',
                cursor: 'pointer',
                padding: '12px',
                borderRadius: '50%',
                transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.8) 100%)',
                border: '2px solid rgba(226, 232, 240, 0.6)',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                backdropFilter: 'blur(8px)',
                position: 'relative',
                overflow: 'hidden'
              }}
            /> */}
          </div>
          <div
            className="modal-content"
            style={{
              padding: '32px 32px 28px',
              background: 'linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%)',
              position: 'relative',
              zIndex: 1
            }}
          >
            <Input
              placeholder="请输入任务名称"
              value={editingTaskName}
              onChange={setEditingTaskName}
              clearable
              maxLength={50}
              style={{
                border: '3px solid transparent',
                borderRadius: '16px',
                padding: '20px 24px',
                fontSize: '17px',
                lineHeight: '1.6',
                background: 'linear-gradient(145deg, #ffffff 0%, #f8fafc 100%)',
                transition: 'all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1)',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.04), inset 0 1px 0 rgba(255, 255, 255, 0.8)',
                fontWeight: '600',
                color: '#1e293b',
                backdropFilter: 'blur(8px)'
              }}
            />
          </div>
          <div
            className="modal-footer"
            style={{
              display: 'flex',
              gap: '24px',
              padding: '28px 32px 40px',
              borderTop: '2px solid transparent',
              background: 'linear-gradient(145deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.95) 100%)',
              position: 'relative',
              zIndex: 2,
              backdropFilter: 'blur(10px)'
            }}
          >
            <Button
              className="cancel-btn"
              fill="outline"
              onClick={handleTaskNameCancel}
              style={{
                flex: 1,
                height: '48px',
                border: '2px solid #e8eaed',
                color: '#6b7280',
                background: 'linear-gradient(135deg, #ffffff 0%, #f9fafb 100%)',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '600',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.06)'
              }}
            >
              取消
            </Button>
            <Button
              className="confirm-btn"
              color="primary"
              onClick={handleTaskNameConfirm}
              disabled={!editingTaskName.trim()}
              style={{
                flex: 1,
                height: '48px',
                background: 'linear-gradient(135deg, #4873FF 0%, #3461E6 100%)',
                border: '2px solid transparent',
                color: 'white',
                borderRadius: '12px',
                fontSize: '16px',
                fontWeight: '700',
                transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                boxShadow: '0 4px 16px rgba(72, 115, 255, 0.35), 0 2px 8px rgba(72, 115, 255, 0.2)',
                textShadow: '0 1px 2px rgba(0, 0, 0, 0.1)'
              }}
            >
              确认
            </Button>
          </div>
        </div>
      </Popup>


    </div>
  );
};

export default TaskDetail;
