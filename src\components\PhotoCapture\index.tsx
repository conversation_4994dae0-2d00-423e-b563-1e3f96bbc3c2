import React, { useState, useRef, useCallback, useEffect } from 'react';
import './index.less';
import { PhotoCaptureProps, PhotoCaptureState } from './types';
import axios from 'axios';
import { Popup, Toast } from 'antd-mobile';
import { startAndroidCamera, isAndroidCameraSupported } from '@/utils/androidCamera';

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  onImageCaptured,
  onUploadSuccess,
  onUploadError,
  quality = 0.7,
  uploadUrl,
  maxWidth = 800,
  maxHeight = 800,
  visible = false,
  onClose,
  title = '拍照上传'
}) => {
  const [state, setState] = useState<PhotoCaptureState>({
    originalImage: null,
    croppedImage: null,
    currentStep: 'select',
    uploading: false
  });

  const fileInputRef = useRef<HTMLInputElement>(null);

  // 设置安卓相机回调
  useEffect(() => {
    // 安卓相机成功回调
    const handleAndroidCameraSuccess = (imageData: string) => {
      try {
        // imageData 可能是 base64 或者文件路径
        const dataUrl = imageData.startsWith('data:') ? imageData : `data:image/jpeg;base64,${imageData}`;
        setState(prev => ({
          ...prev,
          originalImage: dataUrl,
          currentStep: 'preview'
        }));
      } catch (error) {
        console.error('处理安卓相机返回数据失败:', error);
        Toast.show('图片处理失败');
      }
    };

    // 将回调函数挂载到全局
    (window as any).handleAndroidCameraSuccess = handleAndroidCameraSuccess;

    return () => {
      // 清理全局回调
      delete (window as any).handleAndroidCameraSuccess;
    };
  }, []);

  // 检测是否为安卓设备
  const isAndroidDevice = useCallback(() => {
    return /Android/i.test(navigator.userAgent);
  }, []);

  // 安卓设备文件验证和修复
  const validateAndFixAndroidFile = useCallback(async (file: File): Promise<string | null> => {
    return new Promise((resolve) => {
      // 检查文件基本信息
      if (!file || file.size === 0) {
        console.error('文件无效或大小为0');
        resolve(null);
        return;
      }

      // 尝试多种方式读取文件
      const tryMethods = [
        // 方法1: 标准 FileReader
        () => {
          return new Promise<string>((resolveMethod, rejectMethod) => {
            const reader = new FileReader();
            reader.onload = () => {
              const result = reader.result as string;
              if (result && result.startsWith('data:image/') && result.length > 100) {
                resolveMethod(result);
              } else {
                rejectMethod(new Error('FileReader 结果无效'));
              }
            };
            reader.onerror = () => rejectMethod(new Error('FileReader 读取失败'));
            reader.readAsDataURL(file);
          });
        },

        // 方法2: 使用 ArrayBuffer 然后转换
        () => {
          return new Promise<string>((resolveMethod, rejectMethod) => {
            const reader = new FileReader();
            reader.onload = () => {
              try {
                const arrayBuffer = reader.result as ArrayBuffer;
                const uint8Array = new Uint8Array(arrayBuffer);
                const blob = new Blob([uint8Array], { type: file.type || 'image/jpeg' });

                const blobReader = new FileReader();
                blobReader.onload = () => {
                  const result = blobReader.result as string;
                  if (result && result.startsWith('data:image/')) {
                    resolveMethod(result);
                  } else {
                    rejectMethod(new Error('Blob 转换失败'));
                  }
                };
                blobReader.onerror = () => rejectMethod(new Error('Blob 读取失败'));
                blobReader.readAsDataURL(blob);
              } catch (error) {
                rejectMethod(error as Error);
              }
            };
            reader.onerror = () => rejectMethod(new Error('ArrayBuffer 读取失败'));
            reader.readAsArrayBuffer(file);
          });
        }
      ];

      // 依次尝试各种方法
      const tryNextMethod = async (methodIndex: number) => {
        if (methodIndex >= tryMethods.length) {
          console.error('所有读取方法都失败了');
          resolve(null);
          return;
        }

        try {
          const result = await tryMethods[methodIndex]();
          resolve(result);
        } catch (error) {
          console.warn(`方法 ${methodIndex + 1} 失败:`, error);
          tryNextMethod(methodIndex + 1);
        }
      };

      tryNextMethod(0);
    });
  }, []);

  // 处理微信返回的图片
  const handleWeChatImage = useCallback((localId: string) => {
    if (typeof window.WeixinJSBridge === 'object') {
      window.WeixinJSBridge.invoke('getLocalImgData', {
        localId: localId
      }, (res: any) => {
        if (res.err_msg === 'get_local_img_data:ok') {
          const dataUrl = res.localData;
          setState(prev => ({
            ...prev,
            originalImage: dataUrl,
            currentStep: 'preview'
          }));
        } else {
          Toast.show('获取图片失败');
        }
      });
    }
  }, []);

  // 处理文件选择
  const handleFileChange = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      console.log('选择的文件:', file);
      console.log('文件类型:', file.type);
      console.log('文件大小:', file.size);

      // 检查文件是否有效
      if (file.size === 0) {
        console.error('文件大小为0，可能是安卓 file:// 路径问题');
        Toast.show('图片读取失败，请重试');
        return;
      }

      // 检查文件类型
      if (file.type && !file.type.startsWith('image/')) {
        console.error('不是有效的图片文件:', file.type);
        Toast.show('请选择有效的图片文件');
        return;
      }

      // 对于安卓设备，使用特殊的验证和修复方法
      if (isAndroidDevice()) {
        console.log('安卓设备，使用特殊处理方式');
        try {
          const dataUrl = await validateAndFixAndroidFile(file);
          if (dataUrl) {
            setState(prev => ({
              ...prev,
              originalImage: dataUrl,
              currentStep: 'preview'
            }));
          } else {
            Toast.show('图片读取失败，请重试');
          }
        } catch (error) {
          console.error('安卓文件处理失败:', error);
          Toast.show('图片读取失败，请重试');
        }
        return;
      }

      // 非安卓设备使用标准方式
      const reader = new FileReader();

      reader.onload = () => {
        const result = reader.result as string;
        if (result && result.length > 100) { // 确保读取到了有效数据
          setState(prev => ({
            ...prev,
            originalImage: result,
            currentStep: 'preview'
          }));
        } else {
          console.error('FileReader 读取结果无效:', result?.length);
          Toast.show('图片读取失败，请重试');
        }
      };

      reader.onerror = (error) => {
        console.error('FileReader 读取失败:', error);
        Toast.show('图片读取失败，请重试');
      };

      reader.onabort = () => {
        console.error('FileReader 读取被中断');
        Toast.show('图片读取被中断');
      };

      // 尝试读取文件
      try {
        reader.readAsDataURL(file);
      } catch (error) {
        console.error('启动 FileReader 失败:', error);
        Toast.show('图片读取失败，请重试');
      }
    } else {
      console.log('没有选择文件');
    }

    // 清空 input 值，允许重复选择同一文件
    if (e.target) {
      e.target.value = '';
    }
  }, [isAndroidDevice, validateAndFixAndroidFile]);

  // 检测是否为微信环境
  const isWeChatEnv = useCallback(() => {
    return typeof window.WeixinJSBridge === 'object' && typeof window.WeixinJSBridge.invoke === 'function';
  }, []);

  // 安卓设备的特殊处理
  const handleAndroidFileInput = useCallback(() => {
    if (fileInputRef.current) {
      // 为安卓设备设置特殊属性
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.setAttribute('accept', 'image/*');

      // 添加额外的事件监听器来处理安卓的特殊情况
      const handleAndroidChange = (e: Event) => {
        const target = e.target as HTMLInputElement;
        const file = target.files?.[0];

        if (file) {
          console.log('安卓设备文件选择:', file);

          // 对于安卓设备，尝试多种方式读取文件
          const tryReadFile = async () => {
            try {
              // 方法1: 直接使用 FileReader
              const reader = new FileReader();
              reader.onload = (event) => {
                const result = event.target?.result as string;
                if (result && result.startsWith('data:image/')) {
                  setState(prev => ({
                    ...prev,
                    originalImage: result,
                    currentStep: 'preview'
                  }));
                } else {
                  throw new Error('FileReader 结果无效');
                }
              };
              reader.onerror = () => {
                throw new Error('FileReader 读取失败');
              };
              reader.readAsDataURL(file);

            } catch (error) {
              console.error('安卓文件读取失败:', error);

              // 方法2: 尝试使用 URL.createObjectURL (备用方案)
              try {
                const objectUrl = URL.createObjectURL(file);
                const img = new Image();
                img.onload = () => {
                  const canvas = document.createElement('canvas');
                  canvas.width = img.width;
                  canvas.height = img.height;
                  const ctx = canvas.getContext('2d');
                  if (ctx) {
                    ctx.drawImage(img, 0, 0);
                    const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                    setState(prev => ({
                      ...prev,
                      originalImage: dataUrl,
                      currentStep: 'preview'
                    }));
                  }
                  URL.revokeObjectURL(objectUrl);
                };
                img.onerror = () => {
                  URL.revokeObjectURL(objectUrl);
                  Toast.show('图片加载失败，请重试');
                };
                img.src = objectUrl;

              } catch (urlError) {
                console.error('URL.createObjectURL 也失败了:', urlError);
                Toast.show('图片读取失败，请尝试其他方式');
              }
            }
          };

          tryReadFile();
        }
      };

      fileInputRef.current.addEventListener('change', handleAndroidChange, { once: true });
      fileInputRef.current.click();
    }
  }, []);

  // 触发拍照
  const handleTakePhoto = useCallback(async () => {
    // 微信环境使用微信相机
    if (isWeChatEnv()) {
      try {
        window.WeixinJSBridge.invoke('chooseImage', {
          count: 1,
          sizeType: ['original', 'compressed'],
          sourceType: ['camera']
        }, (res: any) => {
          if (res.err_msg === 'choose_image:ok') {
            // 处理微信返回的图片
            handleWeChatImage(res.localIds[0]);
          }
        });
        return;
      } catch (error) {
        console.error('调用微信相机失败:', error);
      }
    }

    // 安卓设备使用特殊处理
    if (isAndroidDevice()) {
      console.log('检测到安卓设备，使用特殊处理方式');
      handleAndroidFileInput();
      return;
    }

    // 其他设备使用标准HTML5方式
    if (fileInputRef.current) {
      fileInputRef.current.setAttribute('capture', 'environment');
      fileInputRef.current.click();
    }
  }, [isWeChatEnv, isAndroidDevice, handleAndroidFileInput, handleWeChatImage]);

  // 图片压缩
  const compressImage = useCallback((dataUrl: string, targetQuality: number = quality): Promise<string> => {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const img = new Image();
      img.src = dataUrl;
      img.onload = () => {
        // 计算压缩后的尺寸
        let { width, height } = img;
        
        if (width > maxWidth || height > maxHeight) {
          const ratio = Math.min(maxWidth / width, maxHeight / height);
          width = width * ratio;
          height = height * ratio;
        }

        canvas.width = width;
        canvas.height = height;
        const ctx = canvas.getContext('2d');
        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);
          const compressedDataUrl = canvas.toDataURL('image/jpeg', targetQuality);
          resolve(compressedDataUrl);
        }
      };
    });
  }, [quality, maxWidth, maxHeight]);

  // 将 DataURL 转换为 File 对象
  const dataURLtoFile = useCallback((dataUrl: string, filename: string): File => {
    const arr = dataUrl.split(',');
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }, []);



  // 上传图片
  const uploadImage = useCallback(async (compressedDataUrl: string) => {
    if (!uploadUrl) {
      console.warn('未提供上传地址');
      return;
    }

    setState(prev => ({ ...prev, uploading: true }));

    try {
      const file = dataURLtoFile(compressedDataUrl, 'photo.jpg');
      const formData = new FormData();
      formData.append('file', file);

      const response = await axios.post(uploadUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      onUploadSuccess?.(response.data);
      onImageCaptured?.(file, compressedDataUrl);
    } catch (error) {
      console.error('上传失败:', error);
      onUploadError?.(error);
    } finally {
      setState(prev => ({ ...prev, uploading: false }));
    }
  }, [uploadUrl, dataURLtoFile, onUploadSuccess, onUploadError, onImageCaptured]);

  // 确认使用图片
  const handleConfirm = useCallback(async () => {
    if (state.originalImage) {
      // const compressedImage = await compressImage(state.originalImage); // 压缩图片
      const compressedImage = state.originalImage; // 不压缩图片
      if (uploadUrl) {
        await uploadImage(compressedImage);
      } else {
        // 如果没有上传地址，直接调用回调
        const file = dataURLtoFile(compressedImage, 'photo.jpg');
        onImageCaptured?.(file, compressedImage);
      }
      
      handleClose();
    }
  }, [state.originalImage, compressImage, uploadImage, uploadUrl, dataURLtoFile, onImageCaptured]);

  // 重新选择
  const handleReselect = useCallback(() => {
    setState({
      originalImage: null,
      croppedImage: null,
      currentStep: 'select',
      uploading: false
    });
  }, []);

  // 关闭组件
  const handleClose = useCallback(() => {
    handleReselect();
    onClose?.();
  }, [handleReselect, onClose]);

  if (!visible) {
    return null;
  }

  return (
    <Popup
      visible={visible}
      onClose={handleClose}
      position="bottom"
      bodyStyle={{
        borderTopLeftRadius: '12px',
        borderTopRightRadius: '12px',
        // minHeight: '60vh',
        maxHeight: '80vh',
        padding: 0
      }}
      closeOnMaskClick={true}
      destroyOnClose={true}
    >
      <div className="photo-capture-overlay">
        <div className="photo-capture-container">
          <div className="photo-capture-header">
          {/* <button className="close-btn" onClick={handleClose}>×</button> */}
          <h3 className="title">{title}</h3>
          <div className="placeholder"></div>
        </div>

        <div className="photo-capture-content">
          {state.currentStep === 'select' && (
            <div className="select-step">
              <div className="camera-placeholder">
                <div className="camera-icon">📷</div>
                <p>点击拍照或选择图片</p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                style={{ display: 'none' }}
              />
              <div className="action-buttons">
                <button className="photo-btn primary" onClick={handleTakePhoto} >
                  开始拍照
                </button>
                <button
                  className="photo-btn secondary"
                  onClick={() => {
                    if (isAndroidDevice()) {
                      // 安卓设备选择图片时不使用 capture 属性
                      if (fileInputRef.current) {
                        fileInputRef.current.removeAttribute('capture');
                        fileInputRef.current.setAttribute('accept', 'image/*');

                        const handleChange = (e: Event) => {
                          const target = e.target as HTMLInputElement;
                          const file = target.files?.[0];
                          if (file) {
                            handleFileChange({ target } as React.ChangeEvent<HTMLInputElement>);
                          }
                        };

                        fileInputRef.current.addEventListener('change', handleChange, { once: true });
                        fileInputRef.current.click();
                      }
                    } else {
                      // 非安卓设备使用标准方式
                      if (fileInputRef.current) {
                        fileInputRef.current.removeAttribute('capture');
                        fileInputRef.current.click();
                      }
                    }
                  }}
                >
                  选择图片
                </button>
              </div>
            </div>
          )}

          {state.currentStep === 'preview' && state.originalImage && (
            <div className="preview-step">
              <div className="preview-container">
                <img src={state.originalImage} alt="预览图片" className="preview-image" />
              </div>
              <div className="action-buttons">
                <button className="photo-btn secondary" onClick={handleReselect}>
                  重新选择
                </button>
                <button 
                  className="photo-btn primary" 
                  onClick={handleConfirm}
                  disabled={state.uploading}
                >
                  {state.uploading ? '上传中...' : '确认使用'}
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    </Popup>
  );
};

export default PhotoCapture;