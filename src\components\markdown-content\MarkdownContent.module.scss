.markdown-content {
    word-break: break-all;
    th {
        border: 1px solid rgba(0, 0, 0, 0.3);
    }

    td {
        border: 1px solid rgba(0, 0, 0, 0.3);
    }

    ol {
        list-style: decimal;
        list-style-position: inside;

        // margin-left: 1rem;
        li {
            p {
                display: inline;
            }
            &::marker {
                margin-right: 0.3em;
            }

            ul {
                list-style: circle;
                list-style-position: inside;
                // margin-left: 1rem;
            }
        }
    }

    ul {
        list-style: disc;
        margin-left: 1rem;
    }


    .markdown-table {
        width: 100%;
        margin-bottom: 0.5rem;
        padding: 1rem;
        background-color: #FFFFFF;
        border-radius: 10px;
        overflow: scroll;
    }
    
    table {
        display: table-cell;
        width: fit-content;
        thead {
            th {
                font-weight: 500;
                font-size: 14px;
                background-color: #D7D7D7;
                padding: 0.5rem;
                color: rgba(0, 0, 0, 0.88);
                white-space: nowrap;
            }
        }

        tbody {
            td {
                font-size: 14px;
                padding: 0.5rem;
                background-color: #FFFFFF;
                max-width: 500px;
                word-wrap: break-word; /* 允许长单词或 URL 地址换行到下一行 */
                overflow-wrap: break-word; /* 现代版本的 word-wrap */
                white-space: normal;
            }
        }
        & > tbody tr:first-child td{
            // white-space: nowrap !important;
        }
    }
    blockquote {
        color: #999;
        padding-left: 0.25rem;
        margin: 0.25rem 0;
        border-left: 1px solid #999;
        border-radius: 2px;
    }
    // br {
    //     content: "";
    //     display: block;
    //     margin-top: 0.5em; /* 可选，根据需要调整 */
    // }
    .wait-content {
        color: #4873FF;
        :global {
            .anticon-loading {
                color: #4873FF;
            }
        }
    }
}
@media screen and (max-width: 768px) {
    .markdown-content {
        .markdown-table {
            padding: 0.5rem;
        }
    }
}