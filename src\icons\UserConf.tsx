import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function UserConf(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 26 26"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><linearGradient x1=".5" y1="1" x2=".5" y2="0" id="14aeea__b"><stop offset="0%" stopColor="#55B1FF"/><stop offset="100%" stopColor="#5354FF"/></linearGradient><clipPath id="14aeea__a"><rect width="26" height="26" rx="0"/></clipPath></defs><g clipPath="url(#14aeea__a)"><path d="M0 13C0 5.82 5.82 0 13 0s13 5.82 13 13-5.82 13-13 13S0 20.18 0 13Z" fill="#FFF" fillOpacity=".5"/><path d="M.75 13C.75 6.235 6.235.75 13 .75S25.25 6.235 25.25 13 19.765 25.25 13 25.25.75 19.765.75 13Z" stroke="url(#14aeea__b)" strokeWidth="1.5"/><path d="M18.668 18.042c.**************.026.136l.005.052a.546.546 0 0 1-1.092 0v-.007h-.016a4.36 4.36 0 0 0-4.201-3.802c-.047.001-.094.006-.14.006-.047 0-.09-.006-.136-.006a4.356 4.356 0 0 0-4.201 3.802h-.016v.007a.546.546 0 0 1-1.092 0l-.005-.052a.541.541 0 0 1 .052-.225 5.43 5.43 0 0 1 3.24-4.19 3.834 3.834 0 1 1 4.32 0 5.434 5.434 0 0 1 3.256 4.28Zm-8.113-7.43a2.715 2.715 0 0 0 2.694 2.729 2.723 2.723 0 0 0 2.735-2.715 2.715 2.715 0 0 0-5.43-.014Z" fillRule="evenodd" fill="#3B8CFF"/></g></g>
        </svg>
    )
}
