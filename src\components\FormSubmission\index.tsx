/*
 * @Description: 表单填报组件
 * @Author: AI Assistant
 * @Date: 2025-07-08
 */
import React, { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { But<PERSON>, Stepper, Selector, Input, ImageViewer, TextArea } from 'antd-mobile';
import { Select } from 'antd';
import { CameraOutline, CloseOutline } from 'antd-mobile-icons';
import PhotoCapture from '../PhotoCapture';
import { beforeUpload } from '../../util/method';
import { apis } from '../../api/api';
import './index.less';

// 表单项类型定义
interface FormItem {
  id: string;
  title: string;
  type: 'SINGLE_CHOICE' | 'MULTIPLE_CHOICE' | 'TEXT';
  options?: string[];
  value?: any;
  required?: boolean;
  index?: number;
}

// 表单数据接口
interface FormData {
  [key: string]: any;
}

interface SubmissionItem {
  id: string;
  images: File[];
  value: any;
  isHazard: number;
  material: string;
  hazardDesc?: string;
}

type SubmissionData = SubmissionItem[];

interface FormSubmissionProps {
  formItems?: any[];
  onSubmit?: (data: SubmissionData) => void;
  onUpload?: (itemId: string) => void;
}

// ref 接口定义
export interface FormSubmissionRef {
  handleSubmit: () => void;
}

const FormSubmission = forwardRef<FormSubmissionRef, FormSubmissionProps>(({
  formItems = [],
  onSubmit,
  onUpload
}, ref) => {
  const [formData, setFormData] = useState<FormData>({});
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [currentItemId, setCurrentItemId] = useState<string>('');
  const [imageData, setImageData] = useState<{[key: string]: {file: File, dataUrl: string,res: string}[]}>({});
  const [statusData, setStatusData] = useState<{[key: string]: number}>({});
  const [hazardDescData, setHazardDescData] = useState<{[key: string]: string}>({});
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [currentImages, setCurrentImages] = useState<string[]>([]);

  // 数据回填 useEffect
  useEffect(() => {
    if (formItems && formItems.length > 0) {
      console.log('FormSubmission 开始数据回填:', formItems);

      // 回填表单数据
      const initialFormData: FormData = {};
      const initialStatusData: {[key: string]: number} = {};
      const initialHazardDescData: {[key: string]: string} = {};
      const initialImageData: {[key: string]: {file: File, dataUrl: string, res: string}[]} = {};

      formItems.forEach((item) => {
        // 回填表单值
        if (item.value) {
          initialFormData[item.id] = item.value;
        }

        // 回填状态数据
        if (item.isHazard !== undefined) {
          initialStatusData[item.id] = item.isHazard;
        }

        // 回填隐患描述数据
        if (item.hazardDesc) {
          initialHazardDescData[item.id] = item.hazardDesc;
        }

        // 回填图片数据
        if (item.material && item.material !== '') {
          const materialIds = typeof item.material === 'string' ? item.material : item.material.join(',');
          if (materialIds) {
            // 获取图片信息
            apis.ginkgoUpload.getFileInfos({ ids: materialIds }).then(res => {
              console.log('回填图片数据:', res);
              if (res.code === 0 && res.data && res.data.length > 0) {
                const imageItems = res.data.map((fileItem: any) => ({
                  file: null as any, // 已上传的图片没有 File 对象
                  dataUrl: fileItem.fileUrl,
                  res: fileItem.id
                }));

                setImageData(prev => ({
                  ...prev,
                  [item.id]: imageItems
                }));
              }
            }).catch(error => {
              console.error('获取图片信息失败:', error);
            });
          }
        }
      });

      // 设置回填的数据
      setFormData(initialFormData);
      setStatusData(initialStatusData);
      setHazardDescData(initialHazardDescData);

      console.log('FormSubmission 回填完成:', {
        formData: initialFormData,
        statusData: initialStatusData,
        hazardDescData: initialHazardDescData
      });
    }
  }, [formItems]);

  // 默认表单项数据（模拟接口返

  const items = formItems.map((item, index) => ({
    ...item,
    // "\"是\"；\"否\"" 去掉引号
    options: (item.config.outputContent).split('；').map(option => option.replace(/"/g, '')),
    title: item.config.reqName,
    index,

  }));
  const dropdownOptions = [
    { label: '正常', value: 0 },
    { label: '隐患', value: 1 },
  ]

  // 处理选择项变化
  const handleSelectChange = (itemId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理数量变化
  const handleNumberChange = (itemId: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理状态选择变化
  const handleStatusChange = (itemId: string, value: number) => {
    setStatusData(prev => ({
      ...prev,
      [itemId]: value
    }));

    // 当状态切换到正常时，清空隐患描述
    if (value === 0) {
      setHazardDescData(prev => ({
        ...prev,
        [itemId]: ''
      }));
    }
  };

  // 处理隐患描述变化
  const handleHazardDescChange = (itemId: string, value: string) => {
    setHazardDescData(prev => ({
      ...prev,
      [itemId]: value
    }));
  };

  // 处理上传
  const handleUpload = (itemId: string) => {
    setCurrentItemId(itemId);
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
    setCurrentItemId('');
  };

  // 处理拍照完成
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    beforeUpload(file, (res) => {
      setImageData(prev => ({
        ...prev,
        [currentItemId]: [...(prev[currentItemId] || []), {file, dataUrl,res: res[0].id}]
      }));
      setShowPhotoCapture(false);
      setCurrentItemId('');
    })
  };

  // 预览图片
  const handleImagePreview = (itemId: string, imageIndex: number) => {
    const images = imageData[itemId] || [];
    const imageUrls = images.map(img => img.dataUrl);
    setCurrentImages(imageUrls);
    setCurrentImageIndex(imageIndex);
    setImageViewerVisible(true);
  };

  // 删除图片
  const handleDeleteImage = (itemId: string, imageIndex: number) => {
    setImageData(prev => ({
      ...prev,
      [itemId]: (prev[itemId] || []).filter((_, index) => index !== imageIndex)
    }));
  };

  // 处理提交
  const handleSubmit = () => {
    if (onSubmit) {
      const submissionData: SubmissionData = items.map(item => ({
        id: item.id,
        images: (imageData[item.id] || []).map(imageItem => imageItem.file),
        material: (imageData[item.id] || []).map(imageItem => imageItem.res).join(','),
        value: formData[item.id] || "",
        isHazard: statusData[item.id] || 0,
        hazardDesc: hazardDescData[item.id] || ""
      }));
      onSubmit(submissionData);
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    handleSubmit
  }), [formData, imageData, statusData, hazardDescData, onSubmit]);

  // 渲染多选项目
  const renderSelectorItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="select-buttons">
        <Selector
            columns={3}
            multiple
            value={formData[item.id] ? (Array.isArray(formData[item.id]) ? formData[item.id] : [formData[item.id]]) : []}
            onChange={(value) => handleSelectChange(item.id, value)}
            options={item.options?.map((option: string) => ({ label: option, value: option }))}
          />
        </div>
        <div className="upload-section">
          <div
            className="upload-btn"
            onClick={() => handleUpload(item.id)}
          >
            <CameraOutline />
            <span>点击上传</span>
          </div>
          {/* 图片预览区域 */}
          {imageData[item.id] && imageData[item.id].length > 0 && (
            <div className="image-preview-list">
              {imageData[item.id].map((imageItem, index) => (
                <div key={index} className="image-preview-item">
                  <img
                    src={imageItem.dataUrl}
                    alt={`上传图片${index + 1}`}
                    onClick={() => handleImagePreview(item.id, index)}
                    className="preview-image"
                  />
                  <div
                    className="delete-btn"
                    onClick={() => handleDeleteImage(item.id, index)}
                  >
                    <CloseOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 隐患描述显示和编辑 */}
        {statusData[item.id] === 1 && (
          <div className="hazard-description">
            <div className="hazard-label">隐患描述：</div>
            <TextArea
              placeholder="请输入隐患描述..."
              value={hazardDescData[item.id] || ''}
              onChange={(value) => handleHazardDescChange(item.id, value)}
              rows={2}
              className="hazard-input"
            />
          </div>
        )}
      </div>
    </div>
  );
  // 渲染选择项
  const renderSelectItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="select-buttons">
          {item.options.map(option => (
            <Button
              key={option}
              className={`select-btn ${formData[item.id] === option ? 'active' : ''}`}
              onClick={() => handleSelectChange(item.id, option)}
            >
              {option}
            </Button>
          ))}
        </div>
        <div className="upload-section">
          <div
            className="upload-btn"
            onClick={() => handleUpload(item.id)}
          >
            <CameraOutline />
            <span>点击上传</span>
          </div>
          {/* 图片预览区域 */}
          {imageData[item.id] && imageData[item.id].length > 0 && (
            <div className="image-preview-list">
              {imageData[item.id].map((imageItem, index) => (
                <div key={index} className="image-preview-item">
                  <img
                    src={imageItem.dataUrl}
                    alt={`上传图片${index + 1}`}
                    onClick={() => handleImagePreview(item.id, index)}
                    className="preview-image"
                  />
                  <div
                    className="delete-btn"
                    onClick={() => handleDeleteImage(item.id, index)}
                  >
                    <CloseOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 隐患描述显示和编辑 */}
        {statusData[item.id] === 1 && (
          <div className="hazard-description">
            <div className="hazard-label">隐患描述：</div>
            <TextArea
              placeholder="请输入隐患描述..."
              value={hazardDescData[item.id] || ''}
              onChange={(value) => handleHazardDescChange(item.id, value)}
              rows={2}
              className="hazard-input"
            />
          </div>
        )}
      </div>
    </div>
  );

  // 渲染数量项
  const renderNumberItem = (item: any) => (
    <div key={item.id} className="form-item">
      <div className="form-item-header">
        <span className="form-item-title">{`${item.index + 1}.${item.title}`}</span>
        <div className="status-selector">
          <Select
            placeholder="请选择"
            style={{ width: 120 }}
            options={dropdownOptions?.map(option => ({ label: option.label, value: option.value }))}
            value={statusData[item.id]}
            onChange={(value) => {
              handleStatusChange(item.id, value)
            }}
          />
        </div>
      </div>
      <div className="form-item-content">
        <div className="number-input">
          <Input
            value={formData[item.id] || ''}
            onChange={(value) => handleNumberChange(item.id, value)}
            className="form-stepper"
            placeholder="请输入"
          />
        </div>
        <div className="upload-section">
          <div
            className="upload-btn"
            onClick={() => handleUpload(item.id)}
          >
            <CameraOutline />
            <span>点击上传</span>
          </div>
          {/* 图片预览区域 */}
          {imageData[item.id] && imageData[item.id].length > 0 && (
            <div className="image-preview-list">
              {imageData[item.id].map((imageItem, index) => (
                <div key={index} className="image-preview-item">
                  <img
                    src={imageItem.dataUrl}
                    alt={`上传图片${index + 1}`}
                    onClick={() => handleImagePreview(item.id, index)}
                    className="preview-image"
                  />
                  <div
                    className="delete-btn"
                    onClick={() => handleDeleteImage(item.id, index)}
                  >
                    <CloseOutline />
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* 隐患描述显示和编辑 */}
        {statusData[item.id] === 1 && (
          <div className="hazard-description">
            <div className="hazard-label">隐患描述：</div>
            <TextArea
              placeholder="请输入隐患描述..."
              value={hazardDescData[item.id] || ''}
              onChange={(value) => handleHazardDescChange(item.id, value)}
              rows={2}
              className="hazard-input"
            />
          </div>
        )}
      </div>
    </div>
  );

  return (
    // TEXT、SINGLE_CHOICE、MULTIPLE_CHOICE
    <div className="form-submission">
      <div className="form-items">
        {items.map(item => {
          switch (item.config.outputType) {
            case 'SINGLE_CHOICE': // 单选
              return renderSelectItem(item);
            case 'TEXT': // 文本
              return renderNumberItem(item);
            case 'MULTIPLE_CHOICE': // 多选
              return renderSelectorItem(item);
            
            default:
              return null;
          }
        })}
      </div>
      
      <div className="form-footer">
        <Button 
          className="submit-btn"
          onClick={handleSubmit}
          block
        >
          提交
        </Button>
      </div>
      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 图片查看器 */}
      <ImageViewer
        image={currentImages[currentImageIndex]}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
        getContainer={null}
      />
    </div>
  );
});

export default FormSubmission;
