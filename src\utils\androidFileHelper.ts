/*
 * @Description: 安卓设备文件处理工具
 * @Author: AI Assistant
 * @Date: 2025-07-22
 */

/**
 * 检测是否为安卓设备
 */
export const isAndroidDevice = (): boolean => {
  return /Android/i.test(navigator.userAgent);
};

/**
 * 检测是否为安卓 WebView 环境
 */
export const isAndroidWebView = (): boolean => {
  const ua = navigator.userAgent;
  return /Android/i.test(ua) && /wv/i.test(ua);
};

/**
 * 安卓设备文件读取修复工具
 * 解决安卓设备上 file:/// 路径无法读取的问题
 */
export const fixAndroidFileReading = async (file: File): Promise<string | null> => {
  if (!file) {
    console.error('文件对象为空');
    return null;
  }

  console.log('开始处理安卓文件:', {
    name: file.name,
    size: file.size,
    type: file.type,
    lastModified: file.lastModified
  });

  // 检查文件基本信息
  if (file.size === 0) {
    console.error('文件大小为0，可能是 file:// 路径问题');
    return null;
  }

  // 定义多种读取方法
  const readMethods = [
    // 方法1: 标准 FileReader.readAsDataURL
    {
      name: 'FileReader.readAsDataURL',
      execute: (): Promise<string> => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            const result = reader.result as string;
            if (result && result.startsWith('data:image/') && result.length > 100) {
              resolve(result);
            } else {
              reject(new Error('读取结果无效'));
            }
          };
          reader.onerror = () => reject(new Error('FileReader 读取失败'));
          reader.onabort = () => reject(new Error('FileReader 读取被中断'));
          reader.readAsDataURL(file);
        });
      }
    },

    // 方法2: ArrayBuffer 转 Blob 再读取
    {
      name: 'ArrayBuffer to Blob',
      execute: (): Promise<string> => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            try {
              const arrayBuffer = reader.result as ArrayBuffer;
              const uint8Array = new Uint8Array(arrayBuffer);
              const blob = new Blob([uint8Array], { type: file.type || 'image/jpeg' });
              
              const blobReader = new FileReader();
              blobReader.onload = () => {
                const result = blobReader.result as string;
                if (result && result.startsWith('data:image/')) {
                  resolve(result);
                } else {
                  reject(new Error('Blob 转换结果无效'));
                }
              };
              blobReader.onerror = () => reject(new Error('Blob 读取失败'));
              blobReader.readAsDataURL(blob);
            } catch (error) {
              reject(error);
            }
          };
          reader.onerror = () => reject(new Error('ArrayBuffer 读取失败'));
          reader.readAsArrayBuffer(file);
        });
      }
    },

    // 方法3: 使用 URL.createObjectURL 配合 Canvas
    {
      name: 'URL.createObjectURL with Canvas',
      execute: (): Promise<string> => {
        return new Promise((resolve, reject) => {
          try {
            const objectUrl = URL.createObjectURL(file);
            const img = new Image();
            
            img.onload = () => {
              try {
                const canvas = document.createElement('canvas');
                canvas.width = img.naturalWidth;
                canvas.height = img.naturalHeight;
                
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                  throw new Error('无法获取 Canvas 上下文');
                }
                
                ctx.drawImage(img, 0, 0);
                const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
                
                URL.revokeObjectURL(objectUrl);
                resolve(dataUrl);
              } catch (error) {
                URL.revokeObjectURL(objectUrl);
                reject(error);
              }
            };
            
            img.onerror = () => {
              URL.revokeObjectURL(objectUrl);
              reject(new Error('图片加载失败'));
            };
            
            img.src = objectUrl;
          } catch (error) {
            reject(error);
          }
        });
      }
    }
  ];

  // 依次尝试各种方法
  for (let i = 0; i < readMethods.length; i++) {
    const method = readMethods[i];
    try {
      console.log(`尝试方法 ${i + 1}: ${method.name}`);
      const result = await method.execute();
      console.log(`方法 ${i + 1} 成功，数据长度:`, result.length);
      return result;
    } catch (error) {
      console.warn(`方法 ${i + 1} (${method.name}) 失败:`, error);
      if (i === readMethods.length - 1) {
        console.error('所有读取方法都失败了');
        return null;
      }
    }
  }

  return null;
};

/**
 * 验证 DataURL 是否有效
 */
export const validateDataURL = (dataUrl: string): boolean => {
  if (!dataUrl || typeof dataUrl !== 'string') {
    return false;
  }

  // 检查是否是有效的 data URL 格式
  if (!dataUrl.startsWith('data:image/')) {
    return false;
  }

  // 检查是否有足够的数据
  if (dataUrl.length < 100) {
    return false;
  }

  // 检查是否包含 base64 数据
  const base64Part = dataUrl.split(',')[1];
  if (!base64Part || base64Part.length < 50) {
    return false;
  }

  return true;
};

/**
 * 获取设备信息用于调试
 */
export const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    isAndroid: isAndroidDevice(),
    isWebView: isAndroidWebView(),
    platform: navigator.platform,
    vendor: navigator.vendor,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine
  };
};
