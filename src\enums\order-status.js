/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-09 15:07:30
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-11 09:59:48
 * @FilePath: src/enums/order-status.js
 * @Version: 1.0.0
 * @Description: 组件描述
 */
const STATUS = {
	NOT_START: 0,
	NOT_ISSUED: 10,
	ISSUING: 20,
	ISSUED: 30,
	CANCELLED: 40
};

// 开票状态
const STATUS_LABEL = {
	0: '未开票',
	10: '开票中',  // 后端对应 未开具
	20: '开票中',
	30: '已开票',
	40: '已开票'   // 后端对应 已废弃
};

const STATUS_BTN_LABEL = {
	0: '申请开票',
	// 1: '开票中',
	30: '重新发送'
};

export {
	STATUS,
	STATUS_LABEL,
	STATUS_BTN_LABEL,
};