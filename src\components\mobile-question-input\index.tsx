/**
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-05-28 15:45:18
 * @LastEditors: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @LastEditTime: 2025-06-24 20:37:05
 * @FilePath: src/components/mobile-question-input/index.tsx
 * @Version: 1.0.0
 * @Description: 组件描述   问答输入组件
 */
import React, { useEffect, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { apis, projectPath } from '@/api/api';
import delFileSvg from '../../assets/images/chat/icon_del_file.svg';
import pdfSvg from '../../assets/images/chat/pdf.svg';
import wordSvg from '../../assets/images/chat/word.svg';
import SceneType from '../../enums/scene-type';
import { UPLOAD_CONFIG, UPLOAD_CONFIG_IMAGE, UPLOAD_CONFIG_NEW } from '@/util/const';
import { monitorSoftKeyboard } from '@/util/soft-keyboard';
import { getGin } from '@/util/util';
import { Image, message, Popover, Upload } from 'antd';
import { TextArea, ImageViewer } from 'antd-mobile';
import classNames from 'classnames';
import { LoadingOutlined } from '@ant-design/icons';

import styles from './index.module.scss';

// 定义组件 Props 接口
interface MobileQuestionInputProps {
	sceneType: string;
	showStop?: boolean;
	onUpdateData: (data: any) => void;
	onSend: () => void;
	onChange: () => void;
	setOpenUpload?: () => void;
	taskData?: any;
	hasPendingImages?: boolean;
}

// 定义组件 Ref 接口
interface MobileQuestionInputRef {
	focusInput: () => void;
}

const MobileQuestionInput = forwardRef<MobileQuestionInputRef, MobileQuestionInputProps>(({ 
	sceneType, 
	showStop, 
	onUpdateData, 
	onSend, 
	onChange, 
	setOpenUpload, 
	taskData, 
	hasPendingImages = false 
}, ref) => {
	const [bFocused, setBFocused] = useState(false);    // 文本是否获取焦点
	const [deepThinkingSelected, setDeepThinkingSelected] = useState(false); // 是否深度思考
	const [onlineSearchSelected, setOnlineSearchSelected] = useState(false);  // 是否联网搜索
	const [bVoice, setBVoice] = useState(false);    // 是否语音输入状态
	const [bUploaded, setBUploaded] = useState(false);  // 是否是文件上传状态
	const [disabled, setDisabled] = useState(false);
	const [questions, setQuestions] = useState('');
	const [fileList, setFileList] = useState<any[]>([]);
	const [placeholder, setPlaceholder] = useState('请输入巡检要求相关的问题');
	// 图片预览相关状态（用于显示通过FileUploadSelector上传的图片）
	const [imageViewerVisible, setImageViewerVisible] = useState(false);
	const [currentImages, setCurrentImages] = useState<string[]>([]);
	const [currentImageIndex, setCurrentImageIndex] = useState(0);
	const inputRef = useRef<any>(null);
	const deepBtnRef = useRef<HTMLDivElement>(null);
	const onlineBtnRef = useRef<HTMLDivElement>(null);
	const sendBtnRef = useRef<HTMLDivElement>(null);
	const uploadBtnRef = useRef<HTMLDivElement>(null);
	const voiceButtonRef = useRef<HTMLDivElement>(null);
	const albumBtnRef = useRef<HTMLDivElement>(null);
	const fileBtnRef = useRef<HTMLDivElement>(null);
	const uploadFileRef = useRef<HTMLDivElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const fileListRef = useRef<any[]>([]);
	const takePicRef = useRef<HTMLDivElement>(null);
	const lastHeight = useRef<number>(window.innerHeight);

	// 暴露聚焦方法给父组件
	useImperativeHandle(ref, () => ({
		focusInput: () => {
			if (inputRef.current) {
				inputRef.current.focus();
				setBFocused(true);
			}
		}
	}));

	const onPressEnter = () => {
		if (questions.length > 0 || fileList.length > 0 || hasPendingImages) {
			onSend();
			setQuestions('');
			setBFocused(false);
			setFileList([]);
		}
	};

	const inputChange = (value: string) => {
		setQuestions(value);
	};

	const onFocus = () => {
		setBFocused(true);
	};

	// 上传前检查
	const beforeUpload = (file: File, uploadItem: any) => {
		const fileExtension = file.name.slice((file.name.lastIndexOf('.') - 1 >>> 0) + 2).toLowerCase();
		console.log('fileExtension:', fileExtension, uploadItem);
		if (!uploadItem.accept.includes(fileExtension)) {
			message.error(`仅支持${uploadItem.accept}文件`);
			return false;
		}
		const size = uploadItem.fileSize || 5;
		if (file.size > size * 1024 * 1024) {
			message.error(`文件上限为${size}M`);
			return false;
		}
		if (sceneType === 'risk_analysis') {
			setPlaceholder('您可以补充照片信息，如：医院消控室、医院工程维修等，我可以为您提供更精准的识别结果');
		}
		setFileList([{
			fileExtension,
			fileName: file.name,
			isUpload: true,
			size: file.size,
			fileParam: uploadItem.fileParam,
			filtType: uploadItem.filtType,
			fileKey: uploadItem.fileKey,
		}]);
		setDisabled(true);
		const formData = new FormData();
		formData.append('file', file);
		formData.append('type', uploadItem.filtType);
		apis.uploadFile.uploadFile(formData).then((res: any) => {
			if (res.code === 0) {
				setFileList([{
					fileExtension,
					...res.data,
					fileId: res.data.id,
					fileParam: uploadItem.fileParam,
					filtType: uploadItem.filtType,
					fileKey: uploadItem.fileKey,
				}]);
				setDisabled(false);
				setBUploaded(false);
				setBFocused(true);
			} else {
				message.error(res.message);
				setFileList([]);
			}
		}).catch((err: any) => {
			message.error(err.message);
			setFileList([]);
		});
		return false;
	};

	// 渲染上传文件
	const renderUploadFile = (item: any, index: number) => {
		switch (item.filtType) {
			case 'kit': {
				return (
					<div
						key={index}
						className={styles['file-item']}
						title={item.fileName}
					>
						<div className={styles['file-icon']}>
							<img src={item.fileExtension.includes('pdf') ? pdfSvg : wordSvg} alt=""/>
						</div>
						<div className={styles['file-info']}>
							<div className={styles['file-name']}>{item.fileName}</div>
						</div>
						<div className={styles['file-del']}>
							{item.isUpload ? <LoadingOutlined/> : <img src={delFileSvg} onClick={() => setFileList([])} alt="删除"/>}
						</div>
					</div>
				);
			}
			case 'image': {
				return (
					<div
						key={index}
						className={styles['file-item']}
						title={item.fileName}
					>
						{
							item.isUpload ? <LoadingOutlined/> :
								<Image
									src={`${projectPath}/upload/downLoadByUrl?url=${item.fileUrl}`}
									alt=""
								/>
						}
						{
							!item.isUpload &&
							<div className={styles['img-del']}>
								<img
									src={delFileSvg}
									onClick={() => setFileList([])}
									alt={'删除'}
								/>
							</div>
						}
					</div>
				);
			}
			case 'chat_video':
				return (
					<div key={index} className={styles['file-item']} title={item.fileName}>
						{
							item.isUpload ? <LoadingOutlined/> :
								<video
									src={item.isUpload ? '' : `${projectPath}/upload/downLoadByUrl?url=${item.fileUrl}`}
								/>
						}
						{
							!item.isUpload &&
							<div className={styles['file-del']}>
								<img src={delFileSvg} onClick={() => setFileList([])} alt="删除"/>
							</div>
						}
					</div>
				);
			default:
				return null;
		}
	};

	// 图片预览函数
	const handleImagePreview = (images: string[], imageIndex: number) => {
		setCurrentImages(images);
		setCurrentImageIndex(imageIndex);
		setImageViewerVisible(true);
	};

	// 上传按钮点击事件 - 打开FileUploadSelector
	const handleUploadClick = () => {
		if (setOpenUpload) {
			setOpenUpload();
		}
	};

	useEffect(() => {
		const onTouchEnd = (e: TouchEvent) => {
			if (e.target !== deepBtnRef.current
				&& e.target !== containerRef.current
				&& e.target !== onlineBtnRef.current
				&& e.target !== sendBtnRef.current
				&& e.target !== uploadBtnRef.current
				&& e.target !== albumBtnRef.current
				&& e.target !== fileBtnRef.current
				&& e.target !== takePicRef.current
				&& e.target !== uploadFileRef.current
				&& (inputRef.current && e.target !== inputRef.current.nativeElement)
				&& fileListRef.current.length === 0) {
				setBFocused(false);
				setBUploaded(false);
			}
			// console.log(e.target, e.target.style, takePicRef.current);
		};
		document.addEventListener('touchend', onTouchEnd);

		monitorSoftKeyboard(isUp => {
			// alert(isUp);
			// message.info(`键盘是否弹起:${isUp}`);
		});

		return () => {
			document.removeEventListener('touchend', onTouchEnd);
		};
	}, []);

	useEffect(() => {
		fileListRef.current = fileList;
		onUpdateData({
			questions,
			fileList,
			deepThinkingSelected,
			onlineSearchSelected,
			bUploaded,
		});
	}, [fileList, questions, deepThinkingSelected, onlineSearchSelected, bUploaded]);

	return (
		<div
			ref={containerRef}
			className={classNames(styles['mobile-question-input-container'], {
				[styles['default-scene']]: bFocused,
				[styles['upload-btn-active']]: bUploaded,
				[styles['disabled']]: showStop,
			})}
		>
			<div
				className={classNames(styles['capsule-tab-wrapper'], {
					[styles['hide']]: fileList.length === 0,
				})}
			>
				{
					fileList.length > 0 && (
						<div
							ref={uploadFileRef}
							className={styles['upload-file']}
						>
							{
								fileList.map((item, index) => renderUploadFile(item, index))
							}
						</div>
					)
				}
			</div>
			<div
				className={styles['mobile-question-input-wrapper']}
			>
				<div
					ref={voiceButtonRef}
					className={classNames(styles['voice-button'], {
						[styles['voice-active']]: bVoice,
					})}
					onClick={() => {
						setBVoice(!bVoice);
					}}
					onContextMenu={(e) => {
						e.preventDefault();
						e.stopPropagation();
						return false;
					}}
					onTouchStart={(e) => {
						e.preventDefault();
						e.stopPropagation();
					}}
					onDragStart={(e) => {
						e.preventDefault();
						e.stopPropagation();
						return false;
					}}
				/>
				{
					bVoice ? (
						<div
							className={classNames(styles['voice-label'], {
								[styles['active']]: bFocused && (sceneType === SceneType.DEFAULT || sceneType === ''),
							})}
							onClick={() => {
								onChange();
							}}
						>
							按住说话
						</div>
					) : (
						<TextArea
							ref={inputRef}
							rows={1}
							onPressEnter={onPressEnter}
							// onKeyDown={onPressEnter}
							onFocus={onFocus}
							placeholder={placeholder}
							value={questions}
							// bordered={false}
							showCount={false}
							onChange={inputChange}
							autoSize={{ minRows: 1, maxRows: 2 }}
							maxLength={300}
						/>
					)
				}
				{
					UPLOAD_CONFIG_NEW[sceneType] && (
						<div
							ref={uploadBtnRef}
							className={classNames(styles['upload'], {
								[styles['upload-active']]: bUploaded,
							})}
							onClick={handleUploadClick}
						/>
					)
				}
				{/* 发送按钮 - 当有内容时显示 */}
				<div
					ref={sendBtnRef}
					className={classNames(styles['send'], {
						[styles['active']]: questions.length > 0 || fileList.length > 0 || hasPendingImages,
						[styles['show']]: bFocused || hasPendingImages,
					})}
					onClick={onPressEnter}
				/>
			</div>
			{/* 图片预览组件 */}
			<ImageViewer
				image={currentImages[currentImageIndex]}
				visible={imageViewerVisible}
				onClose={() => setImageViewerVisible(false)}
				getContainer={null}
			/>
		</div>
	);
});

export default MobileQuestionInput;
