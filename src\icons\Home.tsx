/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2025-01-06 04:04:36
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-01-06 12:12:55
 * @FilePath: \note-exam\src\icons\Home.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Home(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14aeef__a"><rect width="18" height="18" rx="0"/></clipPath></defs><g clipPath="url(#14aeef__a)" fill="#4873FF"><path data-follow-fill="#4873FF" d="m17.609 8.438-2.427-2.04V2.812a1.116 1.116 0 0 0-2.232 0v1.716L9.805 1.89a1.107 1.107 0 0 0-.79-.255h-.026a1.097 1.097 0 0 0-.788.255L.397 8.438a1.12 1.12 0 0 0-.137 1.573c.397.47 1.1.531 1.572.137l7.17-6.015 7.172 6.015a1.12 1.12 0 0 0 1.56-.137c.395-.47.34-1.17-.125-1.573Z"  fill={_fill}/><path data-follow-fill="#4873FF" d="M2.425 10.607v4.484c0 .705.571 1.276 1.276 1.276h3.508v-4.465h3.508v4.465h3.508c.705 0 1.276-.571 1.276-1.276v-4.605l-6.538-5.28-6.538 5.401Z" fill={_fill}/></g></g>
        </svg>
    )
}
