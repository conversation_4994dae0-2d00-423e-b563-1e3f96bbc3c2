
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react-swc'
import path from "path";
import zipPack from 'vite-plugin-zip-pack';
import dayjs from 'dayjs';
import { name as packageName, version, releaseAddress } from './package.json';
import fs from 'fs';


export default defineConfig(({ command, mode }) => {
  let proxyConfig = {};

  // 生成时间
  const time = dayjs().format('YYYYMMDDHH');

  const getZipFileName = () => {
    return `${packageName}_r${version}_${time}`;
  };

  let buildZipPath = '';
  const regExp = /[a-zA-Z]:\//;
  if (regExp.test(releaseAddress)) {
    buildZipPath = releaseAddress;
  } else {
    let currentDisk = '';
    currentDisk = __dirname.substring(0, 2).toUpperCase();
    buildZipPath = `${currentDisk}${releaseAddress.substring(1, releaseAddress.length)}`;
  }

  const outDir = buildZipPath + `/r_${version}`;

  // 生成版本号文件的函数
  const generateVersionFile = () => {
    const versionFilePath = path.resolve(__dirname, 'dist/version.txt');
    fs.writeFileSync(versionFilePath, getZipFileName(), 'utf-8');
    console.log(`Version file generated at ${versionFilePath}`);
  };

  // const url2 = 'https://www.gingpt.cn/';
  // const url = 'https://www.gingpt.cn/'
  const url = 'http://***************:8008/';

  if (mode === 'development') {
    proxyConfig = {
      '/api': {
        // target: 'http://**************:9366/', // 开发环境的代理目标
        target: url,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      },
      '/apisss': {
        target: url, // 开发环境的代理目标
        // target: 'https://www.gingpt.cn/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/apisss/, '')
      },

      '/ginkgo': {
        target: url,
        changeOrigin: true,
        ws: true,
        secure: false,
      },
      '/scooper-lcap': {
        target: url,
        changeOrigin: true,
        ws: true,
        secure: false,
      },
      '/scooper-data':{
        target: url,
        changeOrigin: true,
        ws: true,
        secure: false,
      },
      '/szgd': {
        target: url,
        changeOrigin: true,
        ws: true,
        secure: false,
      }
    };
  }

  return {
    pubLicPath: mode === 'development' ? '/':`/${packageName}/`,
    base: mode === 'development' ? '/':`/${packageName}/`,
    plugins: [
        react(),
        // 打包插件
        zipPack({
          outDir,
          outFileName: `${getZipFileName()}.zip`,
          pathPrefix: packageName
        }),
        {
          name: 'generate-version-file',
          closeBundle: generateVersionFile, // 在打包完成后生成版本号文件
        },
    ],
    server: {
      port: 3000,
      host: '0.0.0.0',
      proxy: proxyConfig
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src'),
        '~': '/src',
      }
    },
    build: {
      rollupOptions: {
        onwarn(warning, warn) {
          // 如果是特定的警告或错误类型，你可以选择忽略它们
          warn(warning);
        }
      },
      sourcemap: true, // 或者使用 'inline' 等其他选项
    }
  }
})
