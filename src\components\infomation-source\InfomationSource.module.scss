.recomment-list {
    max-width: 50rem;
    margin: 0 auto;
    .recomment-list-title {
        // font-size: 16px;
        font-weight: bold;
        color: rgba(0,0,0,0.8);
        margin: 1rem 0 0.5rem;
        display: flex;
        gap: 1rem;
        .list-title-icon {
            width: 16px;
            height: 16px;
            cursor: pointer;
            :global {
                .anticon  {
                    color: #4873FF;
                }
            }
        }
    }
    .list-container {
        display: flex;
        flex-direction: column;
        gap: 1rem;
        .recomment-item {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            .recomment-item-header {
                display: flex;
                flex-direction: row;
                gap: 0.5rem;
                .recomment-item-title {
                    // margin-right: 0.5rem;
                }
                .recomment-item-file {
                    width: fit-content;
                    max-width: calc(100% - 3rem);
                    font-size: 16px;
                    word-break: break-all;
                    color: #4873FF;
                    cursor: pointer;
                    margin-right: 0.5rem;
                    padding-left: 1rem;
                    position: relative;
                    &::after {
                        content: '';
                        width: 6px;
                        height: 6px;
                        border-radius: 100%;
                        background-color: #000000;
                        position: absolute;
                        top: 10px;
                        left: 0;
                    }
                }
                .recomment-item-icon {
                    cursor: pointer;
                }
            }
            .recomment-item-content {
                font-size: 14px;
                white-space: pre-wrap;
                margin-bottom: 1rem;
                padding-left: 0.5rem;
                border-left: 3px solid #D9D9D9;
                border-radius: 6px;
                color: rgba(0,0,0,0.6);
            }
        }
    }
    
}