/*
 * @Description: 检查项目详情页面
 * @Author: AI Assistant
 * @Date: 2025-06-27
 */
import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button, Toast, ImageViewer } from 'antd-mobile';
import { Select } from 'antd';
import { LeftOutline, PlayOutline, SoundOutline, CameraOutline, CloseOutline } from 'antd-mobile-icons';
import FormSubmission from '@/components/FormSubmission';
import PhotoCapture from '@/components/PhotoCapture';
import { SpeechRecognitionContainer } from '@/containers';

import { beforeUpload, createEventSourceHandler, getReqId } from '@/util/method';
import { apis } from '@/api/api';
import './CheckItemDetail.less';
import { urlName } from '@/util/method';

// 检查要求接口
interface CheckRequirement {
  id: string;
  content: string;
  completed: boolean;
  value: string;
  material: string;
  imagUrl: string[];
  isHazard?: number;
  hazardDesc?: string;
}



// 上传状态类型
type UploadStatus = 'default' | 'photo' | 'voice';

const CheckItemDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [taskData, setTaskData] = useState<any>({});
  const [mediaList, setMediaList] = useState<any[]>([]);
  const [currentMedia, setCurrentMedia] = useState<{url: string, type: 'video' | 'audio' | 'image'} | null>(null);
  // 显示模式类型
  type DisplayMode = 'requirements' | 'analysis' | 'form';

  // 组件状态
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>('default');
  const [hasVoiceRecord, setHasVoiceRecord] = useState(false);
  const [showVoiceInput, setShowVoiceInput] = useState(false);
  const [displayMode, setDisplayMode] = useState<DisplayMode>('analysis');
  // PhotoCapture相关状态
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [capturedImages, setCapturedImages] = useState<{ url: string, file: File }[]>([]);
  // Loading状态
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  // 图片预览相关状态
  const [imageViewerVisible, setImageViewerVisible] = useState(false);
  const [currentImages, setCurrentImages] = useState<string[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // 从taskData中获取检查要求数据
  const getCheckRequirements = (checkWay: number): CheckRequirement[] => {
    if (!taskData?.versions?.[0]?.results) return [];

    return taskData.versions[0].results.filter((result: any) => result.checkWay === checkWay).map((result: any) => ({
      id: result.id.toString(),
      content: result.config?.reqName || result.description || '',
      completed: false,
      value: result?.value || '',
      material: result?.material || '',
      imagUrl: result?.imagUrl || [],
      isHazard: result?.isHazard || 0,
      hazardDesc: result?.hazardDesc || ''
    }));
  };

  const pngList = getCheckRequirements(0);
  const requirements = getCheckRequirements(1);

  // 检查所有要求是否都有value值 - 基于传入的数据检查
  const checkAllRequirementsCompleted = (dataToCheck?: any) => {
    // 使用传入的数据或当前的 taskData
    const checkData = dataToCheck || taskData;

    if (!checkData?.versions?.[0]?.results) {
      console.log('checkAllRequirementsCompleted: 没有检查要求数据');
      return false;
    }

    const allResults = checkData.versions[0].results;
    console.log('checkAllRequirementsCompleted: 所有结果数据:', allResults);

    if (allResults.length === 0) {
      console.log('checkAllRequirementsCompleted: 没有检查要求');
      return false;
    }

    // 检查每个要求是否都有 value 值
    const completedResults = allResults.filter((result: any) => {
      const hasValue = result.value && result.value.toString().trim() !== '';
      console.log(`要求 ${result.id} (${result.config?.reqName}): value="${result.value}", 已完成=${hasValue}`);
      return hasValue;
    });

    const isAllCompleted = completedResults.length === allResults.length;
    console.log(`checkAllRequirementsCompleted: 总要求=${allResults.length}, 已完成=${completedResults.length}, 全部完成=${isAllCompleted}`);

    return isAllCompleted;
  };

  // 检查并跳转的函数
  const checkAndNavigateIfCompleted = (dataToCheck?: any, source: string = 'unknown') => {
    console.log(`checkAndNavigateIfCompleted 被调用，来源: ${source}`);

    if (checkAllRequirementsCompleted(dataToCheck)) {
      console.log('所有要求已完成，准备自动跳转到检查记录页面');
      navigate(`${urlName}/inspection-record/${id}`);
      return true;
    } else {
      console.log('还有要求未完成，不跳转');
      return false;
    }
  };

  // 调试日志
  console.log('CheckItemDetail taskData:', taskData);
  console.log('CheckItemDetail pngList:', pngList);
  console.log('CheckItemDetail requirements:', requirements);



  // 根据checkWay判断是否显示特定按钮
  const shouldShowPhotoUpload = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(0);
  };
  // 根据checkWay判断是否显示语音识别按钮
  const shouldShowVoiceRecognition = () => {
    if (!taskData?.checkWay) return true; // 默认显示
    return taskData.checkWay.includes(1);
  };

  useEffect(() => {
    // 自动跳转
    if (checkAllRequirementsCompleted()) {
      console.log('所有要求已完成，自动跳转到检查记录页面');
      navigate(`${urlName}/inspection-record/${id}`);
    }
  }, []);

  const handleBack = () => {
    const taskCode = JSON.parse(sessionStorage.getItem('prameData') || '{}').taskCode;
    const newId = JSON.parse(sessionStorage.getItem('prameData') || '{}').newId;
    navigate(`${urlName}/task-detail/${newId}?taskCode=${taskCode}&stageId=${sessionStorage.getItem('stageId')}`);
  };

  // 处理语音录制
  const handleVoiceRecord = () => {
    // 显示语音输入组件
    setShowVoiceInput(true);
  };

  // 处理语音输入发送
  const handleVoiceSend = (speechText: string) => {
    console.log('语音识别文本:', speechText);
    const origin = window.location.origin;
    // 生成reqId 时间戳+随机4位数字
    const reqId = getReqId();

    const params = {
      type: 1,
      query: taskData.versions[0].id,
      executionId: JSON.parse(sessionStorage.getItem('prameData')).newId,
      curentPlaceId: sessionStorage.getItem('stageId'),
      curentPlaceName: sessionStorage.getItem('stageName'),
    }
    let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&question=${encodeURIComponent(speechText)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
    console.log('url', url);

    // 开始接收消息时显示loading
    if (!isAnalyzing) {
      setIsAnalyzing(true);
    }
    // 更新语音分析结果
    const eventSourceHandler = createEventSourceHandler({
      url,
      qaList: [],
      onMessage: (qaList: any[], answer: string) => {
        console.log('qaList', qaList);
        console.log('answer', answer);
        
      },
      onComplete: (qaList: any[], answerId: string, questionId: string) => {
        console.log('语音识别完成 - qaList', qaList);
        console.log('语音识别完成 - answerId', answerId);
        console.log('语音识别完成 - questionId', questionId);
        // 完成时隐藏loading
        setIsAnalyzing(false);
        Toast.show('语音识别完成');

        // 重新获取数据，getDetail 内部会检查是否需要跳转
        getDetail();
      },
      onError: (error: any) => {
        console.log('error', error);
        // 错误时也要隐藏loading
        setIsAnalyzing(false);
      }
    })
    eventSourceHandler.start();

    setHasVoiceRecord(true);
    setShowVoiceInput(false);
  };

  // 关闭语音输入
  const handleVoiceClose = () => {
    setShowVoiceInput(false);
  };

  // 处理图片预览
  const handleImagePreview = (images: string[], imageIndex: number) => {
    setCurrentImages(images);
    setCurrentImageIndex(imageIndex);
    setImageViewerVisible(true);
  };

  // 打开拍照组件
  const handleOpenPhotoCapture = () => {
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理拍照成功
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    // 添加到图片列表
    const newImage = { url: dataUrl, file };
    setCapturedImages(prev => [...prev, newImage]);

    beforeUpload(file, (res: any) => {
      console.log('res', res);
      const ids = res.map((item: any) => item.id).join(',');

      // 更新上传状态
      const origin = window.location.origin;
      // 生成reqId 时间戳+随机4位数字
      const reqId = getReqId();
      const params = {
        type: 0,
        query: taskData.versions[0].id,
        executionId: JSON.parse(sessionStorage.getItem('prameData')).newId
      }
      let url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData.versions[0].results[0].businessId}&imageIds=${encodeURIComponent(ids)}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;
      console.log('url', url);
      // 开始接收消息时显示loading
      if (!isAnalyzing) {
        setIsAnalyzing(true);
      }
      // 更新图片分析结果
      const eventSourceHandler = createEventSourceHandler({
        url,
        qaList: [],
        onMessage: (qaList: any[], answer: string) => {
          console.log('qaList', qaList);
          console.log('answer', answer);
          
        },
        onComplete: (qaList: any[], answerId: string, questionId: string) => {
          console.log('图片识别完成 - qaList', qaList);
          console.log('图片识别完成 - answerId', answerId);
          console.log('图片识别完成 - questionId', questionId);
          // 完成时隐藏loading
          setIsAnalyzing(false);
          Toast.show('图片识别完成');

          // 重新获取数据，getDetail 内部会检查是否需要跳转
          getDetail();
        },
        onError: (error: any) => {
          console.log('error', error);
          // 错误时也要隐藏loading
          setIsAnalyzing(false);
        }
      })
      eventSourceHandler.start();
    });

    // 更新上传状态




    // // 更新上传状态
    // setUploadStatus('photo');

    // // 模拟AI分析
    // setTimeout(() => {
    //   setAnalysisResults([
    //     { id: '1', requirement: '记录消防水泵接合器数量', result: '118个' },
    //     { id: '2', requirement: '记录喷淋水泵接合器数量', result: '118个' }
    //   ]);
    //   Toast.show('AI分析完成');
    // }, 1000);

    // Toast.show('拍照成功');
  };



  const handleOpenForm = () => {
    console.log('表单填报');
    // 如果当前已经是表单模式，则切换回检查要求，否则切换到表单模式
    setDisplayMode('form');
  };

  const handleSmartAnalysis = () => {
    console.log('智能分析');
    // 如果当前已经是分析模式，则切换回检查要求，否则切换到分析模式
    setDisplayMode('analysis');
  };

  
  // 获取详情
  const getDetail = () => {
    console.log('获取详情');
    // 使用新的接口 getCheckItemDetail
    apis.ginkgoSystem.getCheckItemDetail({
      placeId: sessionStorage.getItem('stageId'),
      configItemId: id
    }).then(res => {
      console.log('getCheckItemDetail res:', res);
      if (res.code === 0) {
        const taskDataList = res.data;

        // 处理图片材料数据
        const promises: Promise<void>[] = [];
        if (taskDataList.versions && taskDataList.versions[0] && taskDataList.versions[0].results) {
          taskDataList.versions[0].results.forEach((item: any) => {
            if (item.material !== '') {
              const promise = apis.ginkgoUpload.getFileInfos({ ids: item.material }).then(res => {
                console.log('图片材料 res:', res);
                item.imagUrl = res.data.map((fileItem: any) => fileItem.fileUrl);
              });
              promises.push(promise);
            }
            item.material = item.material !== '' ? item.material.split(',') : [];
          });
        }

        // 处理指导媒体文件数据
        if (taskDataList.guidanceFileIds) {
          apis.ginkgoUpload.getFileInfos({ ids: taskDataList.guidanceFileIds.join(',') }).then(res => {
            // 处理媒体文件，识别文件类型
            const processedMediaList = res.data.map((item: any) => {
              const fileName = item.fileName.toLowerCase();
              const isVideo = fileName.endsWith('.mp4') || fileName.endsWith('.avi') || fileName.endsWith('.mov') || fileName.endsWith('.wmv') || fileName.endsWith('.webm');
              const isAudio = fileName.endsWith('.mp3') || fileName.endsWith('.wav') || fileName.endsWith('.aac') || fileName.endsWith('.ogg') || fileName.endsWith('.m4a');
              const isImage = fileName.endsWith('.png') || fileName.endsWith('.jpg') || fileName.endsWith('.jpeg') || fileName.endsWith('.gif') || fileName.endsWith('.bmp') || fileName.endsWith('.webp');

              return {
                ...item,
                mediaType: isVideo ? 'video' : (isAudio ? 'audio' : (isImage ? 'image' : 'video')) // 默认当作视频处理
              };
            });

            setMediaList(processedMediaList);
            // 默认显示第一个媒体文件
            if (processedMediaList && processedMediaList.length > 0) {
              const firstMedia = processedMediaList[0];
              setCurrentMedia({
                url: firstMedia.fileUrl,
                type: firstMedia.mediaType
              });
            }
          });
        } else {
          setMediaList([]);
          setCurrentMedia(null);
        }

        // 等待所有图片获取完成后更新taskData，如果没有图片需要处理，直接设置taskData
        if (promises.length === 0) {
          setTaskData(taskDataList);
          // 数据更新完成后检查是否需要自动跳转
          setTimeout(() => {
            checkAndNavigateIfCompleted(taskDataList, 'getDetail-immediate');
          }, 100);
        } else {
          Promise.all(promises).then(() => {
            console.log('所有图片获取完成，更新taskData');
            const updatedTaskData = {...taskDataList};
            setTaskData(updatedTaskData);
            // 数据更新完成后检查是否需要自动跳转
            setTimeout(() => {
              checkAndNavigateIfCompleted(updatedTaskData, 'getDetail-after-images');
            }, 100);
          });
        }
        console.log('CheckItemDetail 设置 taskData:', taskDataList);
      }
    });
  }

  const onSubmit = (data: any) => {
    console.log('表单提交数据:', data);
      data.forEach((item: any, index: number) => {
        console.log(`项目${index + 1}:`, {
          id: item.id,
          value: item.value,
          isHazard: item.isHazard,
          hazardDesc: item.hazardDesc,
          imageCount: item.images.length
        });

      });
      const params = data.map((item: any) => ({
        id: item.id,
        value: item.value,
        isHazard: item.isHazard,
        hazardDesc: item.hazardDesc || '',
        material: item.material,
      }))
      apis.ginkgoSystem.requirementSubmit(params).then(res => {
        console.log('表单提交结果:', res);
        if (res.code === 0) {
          Toast.show('提交成功');
          // 表单提交成功后直接跳转，不需要再检查
          navigate(`${urlName}/inspection-record/${id}`);
        } else {
          Toast.show(res.message);
        }
      })
  }

  useEffect(() => {
    // 优先使用新接口获取数据，而不是依赖 sessionStorage
    console.log('组件初始化，调用 getDetail 获取最新数据');
    getDetail();
  }, []);

  // 监听 taskData 变化，作为额外的检查保障
  useEffect(() => {
    if (taskData && taskData.versions && taskData.versions.length > 0) {
      console.log('taskData 已更新，检查是否需要自动跳转');
      // 延迟一点时间确保状态完全更新
      setTimeout(() => {
        checkAndNavigateIfCompleted(taskData, 'taskData-useEffect');
      }, 200);
    }
  }, [taskData]);

  return (
    <div className="check-item-detail-container">
      {/* 头部 */}
      <header className="check-item-header">
        <LeftOutline
          className="back-icon"
          onClick={handleBack}
        />
        <h1 className="page-title">{taskData.cateName}</h1>
      </header>

      {/* 主要内容区域 - 可滚动 */}
      <div className="main-content">
        <div className="section-header">
          <span className="section-icon guidance-icon"></span>
          <span className="section-title">巡检指导</span>
          <Select
            className="media-selector"
            value={currentMedia ? mediaList.find(item => item.fileUrl === currentMedia.url)?.id : undefined}
            onChange={(value) => {
              const selectedMedia = mediaList.find(media => media.id === value);
              if (selectedMedia) {
                setCurrentMedia({
                  url: selectedMedia.fileUrl,
                  type: selectedMedia.mediaType
                });
              }
            }}
            placeholder={mediaList.length > 0 ? "选择巡检指导文件" : "无指导文件"}
            disabled={mediaList.length === 0}
            options={mediaList.map((item: any) => ({
              label: `${item.fileName} (${item.mediaType === 'video' ? '视频' : (item.mediaType === 'audio' ? '音频' : '图片')})`,
              value: item.id
            }))}
          >
          </Select>
        </div>

        {/* 巡检指导区域 */}
        <div className="guidance-section">
          <div className="media-container">
            {mediaList.length === 0 ? (
              <div className="media-placeholder">
                <PlayOutline className="play-icon" />
                <div className="placeholder-text">无指导文件</div>
              </div>
            ) : currentMedia ? (
              currentMedia.type === 'video' ? (
                <video
                  controls
                  width="100%"
                  height="200"
                  src={currentMedia.url}
                  style={{ borderRadius: '8px' }}
                >
                  您的浏览器不支持视频播放
                </video>
              ) : currentMedia.type === 'audio' ? (
                <div className="audio-container">
                  <div className="audio-info">
                    <SoundOutline className="audio-icon" />
                    <span className="audio-label">音频指导</span>
                  </div>
                  <audio
                    controls
                    src={currentMedia.url}
                    style={{ width: '100%', borderRadius: '8px', marginTop: '12px' }}
                  >
                    您的浏览器不支持音频播放
                  </audio>
                </div>
              ) : (
                <div className="image-container">
                  <img
                    src={currentMedia.url}
                    alt="巡检指导图片"
                    className="guide-image"
                    onClick={() => handleImagePreview([currentMedia.url], 0)}
                    style={{
                      width: '100%',
                      height: '180px',
                      objectFit: 'contain',
                      borderRadius: '8px',
                      cursor: 'pointer',
                      backgroundColor: 'rgba(255, 255, 255, 0.1)'
                    }}
                  />
                </div>
              )
            ) : (
              <div className="media-placeholder">
                <PlayOutline className="play-icon" />
                <div className="placeholder-text">加载中...</div>
              </div>
            )}
          </div>
        </div>

        <div className="section-header">
          <span className="section-icon requirements-icon"></span>
          <span className="section-title">检查要求</span>
          <div className="action-buttons">
            <div
              className={`check-btn ${displayMode === 'analysis' ? 'active' : ''}`}
              onClick={handleSmartAnalysis}
            >
              智能分析
            </div>
            <div
              className={`check-btn ${displayMode === 'form' ? 'active' : ''}`}
              onClick={handleOpenForm}
            >
              表单填报
            </div>
          </div>
        </div>

        {/* 检查要求区域 - 拍照上传 */}
        {displayMode !== 'form' && shouldShowPhotoUpload() && (
          <div className="requirements-section">
            {/* 智能分析 */}
            {displayMode === 'analysis' && (
              <div className="analysis-content">
                {/* 只有在没有AI分析结果时才显示检查要求列表 */}
                {!pngList.some(item => item.value) && (
                  <div className="requirements-list">
                    {pngList.map((req, index) => (
                      <div key={req.id} className="requirement-item">
                        <span className="requirement-number">{index + 1}.</span>
                        <span className="requirement-content">{req.content}</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* AI分析结果区域 - 替换检查要求列表 */}
                {pngList.some(item => item.value) && (
                  <div className="analysis-sections">
                    <div className="section-header">
                      <span className="analysis-icon"></span>
                      <span className="section-title titleColor">分析结果</span>
                    </div>
                    <div className="analysis-results">
                      {pngList.map((result, index) => (
                        <div key={result.id} className="analysis-item">
                          <div className="analysis-header">
                            <span className="analysis-number">{index + 1}.</span>
                            <span className="analysis-requirement">{result.content}</span>
                            {result.value && (
                              <span className={`analysis-status ${result.isHazard === 1 ? 'hazard' : 'normal'}`}>
                                {result.isHazard === 1 ? '隐患' : '正常'}
                              </span>
                            )}
                          </div>

                          {/* 显示分析结果值 */}
                          {result.value && (
                            <div className="analysis-result">
                              <span className="result-label">分析结果：</span>
                              <span className="result-value">{result.value}</span>
                            </div>
                          )}

                          {/* 显示隐患描述 */}
                          {result.hazardDesc && (
                            <div className="hazard-description">
                              <span className="hazard-label">隐患描述：</span>
                              <span className="hazard-content">{result.hazardDesc}</span>
                            </div>
                          )}

                          {/* 显示图片 */}
                          {result.imagUrl && result.imagUrl.length > 0 && result.imagUrl.some(url => url) && (
                            <div className="analysis-images">
                              <div className="images-grid">
                                {result.imagUrl.filter(url => url).map((imageUrl, imgIndex) => (
                                  <div key={imgIndex} className="analysis-image-item">
                                    <img
                                      src={imageUrl}
                                      alt={`检查图片${imgIndex + 1}`}
                                      onClick={() => handleImagePreview(result.imagUrl.filter(url => url), imgIndex)}
                                    />
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {displayMode !== 'form' && shouldShowPhotoUpload() && (<div className="upload-section">
          <div className="upload-buttons">
            <Button
              className="upload-btn photo-btn"
              block
              onClick={handleOpenPhotoCapture}
              loading={isAnalyzing}
              disabled={isAnalyzing}
            >
              <CameraOutline style={{ marginRight: '8px' }} /> {isAnalyzing ? '分析中...' : (uploadStatus === 'photo' ? '再次拍照' : '拍照上传')}
            </Button>
          </div>
        </div>)}

        {/* 检查要求区域 - 语音识别 */}
        {displayMode !== 'form' && shouldShowVoiceRecognition() && (
          <div className="requirements-section">
            {/* 只有在没有AI分析结果时才显示检查要求列表 */}
            {!requirements.some(item => item.value) && (
              <div className="requirements-list">
                {requirements.map((req, index) => (
                  <div key={req.id} className="requirement-item">
                    <span className="requirement-number">{index + 1}.</span>
                    <span className="requirement-content">{req.content}</span>
                  </div>
                ))}
              </div>
            )}

            {/* AI分析结果区域 - 替换检查要求列表 */}
            {requirements.some(item => item.value) && (
              <div className="analysis-sections">
                <div className="section-header">
                  <span className="analysis-icon"></span>
                  <span className="section-title">分析结果</span>
                </div>
                <div className="analysis-results">
                  {requirements.map((result, index) => (
                    <div key={result.id} className="analysis-item">
                      <div className="analysis-header">
                        <span className="analysis-number">{index + 1}.</span>
                        <span className="analysis-requirement">{result.content}</span>
                        {result.value && (
                          <span className={`analysis-status ${result.isHazard === 1 ? 'hazard' : 'normal'}`}>
                            {result.isHazard === 1 ? '隐患' : '正常'}
                          </span>
                        )}
                      </div>

                      {/* 显示分析结果值 */}
                      {result.value && (
                        <div className="analysis-result">
                          <span className="result-label">分析结果：</span>
                          <span className="result-value">{result.value}</span>
                        </div>
                      )}

                      {/* 显示隐患描述 */}
                      {result.hazardDesc && (
                        <div className="hazard-description">
                          <span className="hazard-label">隐患描述：</span>
                          <span className="hazard-content">{result.hazardDesc}</span>
                        </div>
                      )}

                      {/* 显示图片 */}
                      {result.imagUrl && result.imagUrl.length > 0 && result.imagUrl.some(url => url) && (
                        <div className="analysis-images">
                          <div className="images-grid">
                            {result.imagUrl.filter(url => url).map((imageUrl, imgIndex) => (
                              <div key={imgIndex} className="analysis-image-item">
                                <img
                                  src={imageUrl}
                                  alt={`检查图片${imgIndex + 1}`}
                                  onClick={() => handleImagePreview(result.imagUrl.filter(url => url), imgIndex)}
                                />
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* 语音识别按钮 - 根据checkWay和displayMode控制显示 */}
        {displayMode !== 'form' && shouldShowVoiceRecognition() && (
          <div className="voice-section">
            <Button
              className={`voice-btn`}
              block
              onClick={handleVoiceRecord}
              loading={isAnalyzing}
              disabled={isAnalyzing}
            >
              <SoundOutline style={{ marginRight: '8px' }} />
              {isAnalyzing ? '分析中...' : (hasVoiceRecord ? '重新识别' : '语音识别')}
            </Button>
          </div>
        )}

        {/* 表单填报 */}
        {displayMode === 'form' && (
          <FormSubmission
            formItems={taskData.versions[0].results}
            onSubmit={(data) => {
              onSubmit(data);
            }}
            onUpload={(itemId) => {
              console.log('上传文件:', itemId);
              // 这里可以调用拍照或选择文件的功能
              handleOpenPhotoCapture();
            }}
          />
        )}
      </div>

      {/* PhotoCapture 组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
        quality={0.8}
        maxWidth={1200}
        maxHeight={1200}
      />

      {/* 语音输入组件 */}
      {showVoiceInput && (
        <div className="voice-input-overlay">
          <SpeechRecognitionContainer
            onSend={handleVoiceSend}
            onClose={handleVoiceClose}
            loading={isAnalyzing}
          />
        </div>
      )}

      {/* 图片预览组件 */}
      <ImageViewer
        image={currentImages[currentImageIndex]}
        visible={imageViewerVisible}
        onClose={() => setImageViewerVisible(false)}
        getContainer={null}
      />
    </div>
  );
};

export default CheckItemDetail;
