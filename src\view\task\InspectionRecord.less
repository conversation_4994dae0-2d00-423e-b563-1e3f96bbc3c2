.inspection-record-container {
  min-height: 100vh;
  height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  // 头部样式
  .inspection-record-header {
    background-color: #fff;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;

    .back-icon {
      font-size: 20px;
      color: #333;
      margin-right: 12px;
      cursor: pointer;
    }

    .page-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin: 0;
      flex: 1;
    }
  }

  // 标签页样式
  .tab-bar {
    background-color: #fff;
    display: flex;
    border-bottom: 1px solid #e8e8e8;

    .tab-item {
      flex: 1;
      padding: 12px 16px;
      text-align: center;
      font-size: 15px;
      color: #999;
      cursor: pointer;
      position: relative;
      font-weight: 400;

      &.active {
        color: #1890ff;
        font-weight: 500;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #1890ff;
        }
      }
    }
  }

  // 记录内容样式
  .record-content {
    // flex: 1;
    padding: 12px;
    background-color: #f5f5f5;
    overflow-y: auto;
    // height: 0; // 强制flex子元素可滚动
    margin-bottom: 106px;
    padding-bottom: 230px;
    .record-section{
      margin-bottom: 12px;
    }
    .record-section:last-child{
      margin-bottom: 120px;
    }
    

    // 当前记录时间
    .current-record {
      padding: 10px 12px;
      display: flex;
      align-items: center;
      box-shadow: none;
      background: linear-gradient( 90deg, #EBF3F9 0%, #fff 100%);
      box-shadow: 2px 2 7px 0px rgba(0,0,0,0.04);
      border-radius: 9px 9px 0px 0px;

      .record-icon {
        font-size: 14px;
        margin-right: 6px;
        color: #1890ff;
      }

      .record-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-right: 8px;
        min-width: 0; // 确保可以收缩
        
        .record-name-container {
          display: flex;
          flex-direction: column;
          margin-bottom: 4px;

          .record-name {
            font-size: 14px;
            color: #333;
            font-weight: 500;
            max-width: 200px; // 设置最大宽度
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }


        }

        .record-name-input {
          margin-bottom: 4px;
          max-width: 200px;
          border: 1px solid rgba(202, 217, 250, 1);
          background: #FFFFFF;
          border-radius: 2px;

          .record-name-input{

          }
          .adm-input-element{
            font-size: 15px;
            background: #FFFFFF;
            padding: 2px 10px;

          }
          .adm-input {
            font-size: 14px;
            font-weight: 500;
            border: 2px solid #1890ff;
            border-radius: 6px;
            padding: 6px 10px;
            background-color: #fff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
            min-height: 32px; // 确保足够的触摸区域

            &:focus {
              border-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.25);
              outline: none;
            }

            &::placeholder {
              color: #bfbfbf;
              font-weight: normal;
            }
          }
        }

        .record-time {
          font-size: 12px;
          color: #999;
          background: #f0f0f0;
          padding: 2px 8px;
          border-radius: 10px;
          white-space: nowrap;
          align-self: flex-start; // 左对齐
          max-width: fit-content;
        }
      }

      .record-actions {
        display: flex;
        align-items: center;
        gap: 8px;

        .edit-btn, .complete-btn {
          font-size: 12px;
          height: 24px;
          padding: 2px 12px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.2s ease;
          border: none;
        }

        .edit-btn {
          background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
          border-radius: 22px;
          color: #fff;
        }

        .complete-btn {
          background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
          border-radius: 22px;
          color: #fff;

          &:hover {
            background-color: rgba(82, 196, 26, 0.2);
            border-color: rgba(82, 196, 26, 0.5);
          }
        }

        .delete-btn {
          font-size: 12px;
          color: #fff;
          cursor: pointer;
          padding: 4px 12px;
          background: linear-gradient( 315deg, rgba(170,30,30,0.9) 0%, rgba(255,105,105,0.9) 100%);
          border-radius: 22px;
        }
      }

      .collapse-icon {
        font-size: 12px;
        color: #999;
        transition: transform 0.3s ease;
        cursor: pointer;

        &.collapsed {
          transform: rotate(90deg);
        }
      }
    }

    // 检查项目列表
    .inspection-items {
      .inspection-item {
        background-color: #fff;
        overflow: hidden;
        box-shadow: none;

        .item-header {
          padding: 10px 12px;
          display: flex;
          align-items: center;

          .status-icon {
            font-size: 14px;
            margin-right: 6px;
            font-weight: bold;
            width: 16px;
            text-align: center;
          }

          .item-number {
            font-weight: 500;
            color: #333;
            margin-right: 6px;
            font-size: 13px;
          }

          .item-title {
            flex: 1;
            font-size: 13px;
            color: #333;
          }

          .status-selector {
            display: flex;
            gap: 6px;
            margin-right: 8px;

            .status-btn {
              font-size: 11px;
              height: 22px;
              padding: 0 6px;
              border-radius: 3px;
              border: 1px solid #d9d9d9;
              background-color: #fff;
              color: #666;

              &.active {
                background-color: #e6f7ff;
                border-color: #91d5ff;
                color: #1890ff;
              }

              &:hover {
                border-color: #40a9ff;
              }
            }
          }

          .status-text {
            font-size: 12px;
            font-weight: 500;
            margin-right: 8px;
          }

          .arrow-icon {
            font-size: 12px;
            color: #1890ff;
          }
        }

        .item-content {
          padding: 12px;

          // 隐患描述样式
          .hazard-description {
            margin-bottom: 12px;
            padding: 8px 10px;
            background-color: #fff2e8;
            border: 1px solid #ffbb96;
            border-radius: 4px;
            border-left: 3px solid #ff7a45;

            .hazard-label {
              font-size: 12px;
              font-weight: 500;
              color: #d4380d;
              margin-bottom: 4px;
            }

            .hazard-content {
              font-size: 13px;
              color: #ad2102;
              line-height: 1.4;
              word-break: break-all;
            }

            .hazard-input {
              background-color: #fff;
              border: 1px solid #ffbb96;
              border-radius: 4px;
              font-size: 13px;
              color: #ad2102;

              &:focus {
                border-color: #ff7a45;
                box-shadow: 0 0 0 2px rgba(255, 122, 69, 0.2);
              }

              .adm-text-area-element {
                background-color: #fff;
                color: #ad2102;
                font-size: 13px;
                line-height: 1.4;
              }
            }
          }

          .button-group-section {
            margin-bottom: 10px;

            .option-buttons {
              display: flex;
              gap: 8px;
              .adm-button-disabled{
                opacity: 0.8;
              }
              .option-btn {
                flex: 1;
                min-width: 50px;
                height: 28px;
                border-radius: 4px;
                font-size: 13px;
                font-weight: 500;
                border: 1px solid #d9d9d9;
                background-color: #fff;
                color: #666;

                &.active {
                  background-color: rgba(72, 115, 255, 0.20);
                  border-color: rgba(72, 115, 255, 1);
                  color: rgba(72, 115, 255, 1);
                }

                &:hover:not(.disabled) {
                  border-color: #40a9ff;
                }

                &.disabled {
                  // opacity: 0.6;
                  // cursor: not-allowed;
                  // background-color: #f5f5f5;
                  // color: #bfbfbf;
                  // border-color: #e8e8e8;
                }

                .adm-button-content {
                  font-size: 13px;
                }
              }
            }
          }

          .counter-section {
            margin-bottom: 10px;
            display: flex;
            justify-content: flex-start;
            align-items: center;

            .item-stepper {
              width: 100%;
              .adm-stepper-minus,
              .adm-stepper-plus {
                width: 40px;
                height: 40px;
                border-radius: 4px;
                border: 1px solid #d9d9d9;
                background-color: #fff;
              }

              .adm-stepper-input {
                // width: 60px;
                height: 40px;
                text-align: center;
                font-size: 14px;
                font-weight: 500;
                border: 1px solid #d9d9d9;
                border-left: none;
                border-right: none;
              }
            }
          }

          .media-section {
            display: flex;
            align-items: flex-start;
            gap: 8px;
            flex-wrap: wrap;

            .uploaded-images {
              display: flex;
              gap: 6px;
              flex-wrap: wrap;

              .image-item {
                position: relative;
                width: 78px;
                height: 78px;
                border-radius: 4px;
                border: 1px solid #e8e8e8;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  transform: scale(1.05);
                  border-color: #1890ff;
                  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
                }

                &:active {
                  transform: scale(0.98);
                }

                img {
                  width: 100%;
                  height: 100%;
                  object-fit: cover;
                  transition: all 0.2s ease;
                }

                &:hover img {
                  filter: brightness(1.1);
                }

                .play-overlay {
                  position: absolute;
                  top: 50%;
                  left: 50%;
                  transform: translate(-50%, -50%);
                  width: 16px;
                  height: 16px;
                  background-color: rgba(0, 0, 0, 0.7);
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 8px;
                }

                .delete-image-btn {
                  position: absolute;
                  top: -4px;
                  right: -4px;
                  width: 16px;
                  height: 16px;
                  background-color: #ff4d4f;
                  border-radius: 50%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  color: white;
                  font-size: 12px;
                  font-weight: bold;
                  cursor: pointer;
                  z-index: 10;
                  transition: all 0.2s ease;
                  border: 1px solid white;

                  &:hover {
                    background-color: #d9363e;
                    transform: scale(1.1);
                  }

                  &:active {
                    transform: scale(0.9);
                  }
                }

                .preview-overlay {
                  position: absolute;
                  top: 0;
                  left: 0;
                  right: 0;
                  bottom: 0;
                  background-color: rgba(0, 0, 0, 0.3);
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  opacity: 0;
                  transition: opacity 0.2s ease;
                  cursor: pointer;

                  .preview-text {
                    color: white;
                    font-size: 10px;
                    text-align: center;
                    padding: 2px;
                  }

                  &:hover {
                    opacity: 1;
                  }
                }

                &:hover .preview-overlay {
                  opacity: 1;
                }
              }
            }

            .loading-images {
              display: flex;
              align-items: center;
              padding: 8px 12px;
              background-color: #f8f9fa;
              border-radius: 4px;
              border: 1px solid #e9ecef;
              color: #666;
              font-size: 12px;

              span {
                position: relative;

                &::after {
                  content: '';
                  display: inline-block;
                  width: 12px;
                  height: 12px;
                  margin-left: 8px;
                  border: 2px solid #e9ecef;
                  border-top: 2px solid #4873FF;
                  border-radius: 50%;
                  animation: spin 1s linear infinite;
                }
              }
            }

            @keyframes spin {
              0% { transform: rotate(0deg); }
              100% { transform: rotate(360deg); }
            }

            .photo-btn {
              width: 104px;
              height: 78px;
              background-color: #f0f9ff;
              border: 1px dashed #1890ff;
              border-radius: 4px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              cursor: pointer;
              color: #1890ff;

              &:hover {
                background-color: #e6f7ff;
              }

              span {
                font-size: 15px;
                margin-top: 1px;
                line-height: 1;
              }
            }
          }
        }
      }

      .description-section {
        padding: 12px;
        background-color: #fff;
        overflow: hidden;
        box-shadow: none;
        .description-input{
          background-color: #f5f5f5;
          border-radius: 4px;
          font-size: 13px;
          color: #333;
          padding: 10px 12px;
        }
      }
    }

    // 历史记录
    .record-history {
      margin-top: 12px;

      .history-item {
        background-color: #fff;
        padding: 10px 12px;
        border-radius: 6px;
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        box-shadow: none;
        border: 1px solid #e8e8e8;

        .record-icon {
          font-size: 14px;
          margin-right: 6px;
          color: #1890ff;
        }

        .record-time {
          flex: 1;
          font-size: 13px;
          color: #333;
        }

        .delete-btn {
          font-size: 12px;
          color: #ff4d4f;
          cursor: pointer;
          padding: 2px 6px;
          border-radius: 3px;
          background-color: #fff2f0;
          border: 1px solid #ffccc7;
        }
      }
    }

    // 底部区域
    .bottom-section {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      margin-top: 20px;
      padding-bottom: 16px;
      background: #F2F5F7;
      // box-shadow: 0px -1 26px 0px rgba(0,0,0,0.24);
      // 上阴影
      box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.1);
      padding: 16px;

      .tip-text {
        text-align: center;
        font-size: 12px;
        color: #999;
        margin-bottom: 12px;
      }

      .add-btn {
        height: 44px;
        border-radius: 22px;
        font-size: 15px;
        font-weight: 500;
        background: linear-gradient( 315deg, rgba(35,60,138,0.9) 0%, rgba(72,115,255,0.9) 100%);
        border: none;
        color: white;

        &:active {
          background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
        }
      }
    }
  }
  

  // 指导内容样式
  .guide-content {
    flex: 1;
    padding: 16px;
    background-color: #f8f9fa;

    .guide-section {
      background-color: #fff;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .video-selector {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;

        .ant-select {
          .ant-select-selector {
            border-radius: 8px;
            border: 1px solid #d9d9d9;
            height: 44px;

            .ant-select-selection-item {
              line-height: 42px;
              font-size: 14px;
              color: #333;
            }

            .ant-select-selection-placeholder {
              line-height: 42px;
              color: #999;
            }
          }

          &:hover .ant-select-selector {
            border-color: #1890ff;
          }

          &.ant-select-focused .ant-select-selector {
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        .no-video-tip {
          display: flex;
          align-items: center;
          justify-content: center;
          height: 44px;
          border: 1px solid #d9d9d9;
          border-radius: 8px;
          background-color: #fafafa;

          .no-video-text {
            font-size: 14px;
            color: #999;
            font-style: italic;
          }
        }
      }

      .media-container {
        .video-container {
          position: relative;
          background-color: #000;

          .guide-video {
            width: 100%;
            height: 240px;
            object-fit: contain;
            background-color: #000;

            &::-webkit-media-controls-panel {
              background-color: rgba(0, 0, 0, 0.8);
            }
          }
        }

        .image-container {
          position: relative;
          background-color: #f5f5f5;
          border-radius: 8px;
          overflow: hidden;

          .guide-image {
            width: 100%;
            height: 240px;
            object-fit: contain;
            cursor: pointer;
            transition: transform 0.2s ease;

            &:hover {
              transform: scale(1.02);
            }
          }
        }
      }

      .video-description {
        padding: 16px;

        .description-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .description-content {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }
}

.confirm-cancel-css{
  .adm-dialog-body{
    .adm-dialog-body-title{
      // font-size: 16px;
    }
  }
  .adm-dialog-footer{
    .adm-dialog-action-row{
      .adm-button{
        // 灰色
        color: rgba(0, 0, 0, 0.8);
      }
      .adm-dialog-button-bold{
        color: #4873FF;
        font-weight: 500;
      }
    }
  }
}

// 响应式适配
@media (max-width: 375px) {
  .inspection-record-container {
    .record-content {
      padding: 12px;
      padding-bottom: 190px;
      .inspection-items {
        .inspection-item {
          .item-content {
            padding: 12px;
          }
        }
      }
    }

    .guide-content {
      padding: 12px;

      .guide-section {
        .video-selector {
          padding: 12px;
        }

        .video-container {
          .guide-video {
            height: 200px;
          }
        }

        .video-description {
          padding: 12px;

          .description-title {
            font-size: 15px;
          }

          .description-content {
            font-size: 13px;
          }
        }
      }
    }
  }
}

.upload-btn-icon {
  width: 24px;
  height: 24px;
  background: url('../../assets/images/icon_camera.png') no-repeat center center;
  background-size: 100% 100%;
}
