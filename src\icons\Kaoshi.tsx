/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2025-01-07 09:09:14
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-01-07 17:09:54
 * @FilePath: \note-exam\src\icons\Kaoshi.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, { useEffect, useRef } from 'react';
import styles from './style.module.css';
interface IconProps extends React.SVGProps<SVGSVGElement> {
    size?: string | number;
    width?: string | number;
    height?: string | number;
    spin?: boolean;
    rtl?: boolean;
    color?: string;
    fill?: string;
    stroke?: string;
}

export default function Kaoshi(props: IconProps) {
    const root = useRef<SVGSVGElement>(null)
    const { size = '1em', width, height, spin, rtl, color, fill, stroke, className, ...rest } = props;
    const _width = width || size;
    const _height = height || size;
    const _stroke = stroke || color;
    const _fill = fill || color;
    useEffect(() => {
      if (!_fill) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-fill]').forEach(item => {
          item.setAttribute('fill', item.getAttribute('data-follow-fill') || '')
        })
      }
      if (!_stroke) {
        (root.current as SVGSVGElement)?.querySelectorAll('[data-follow-stroke]').forEach(item => {
          item.setAttribute('stroke', item.getAttribute('data-follow-stroke') || '')
        })
      }
    }, [stroke, color, fill])
    return (
        <svg
          ref={root}
          width={_width} 
          height={_height}
          viewBox="0 0 18 18"
          preserveAspectRatio="xMidYMid meet"
          fill="none"
          role="presentation"
          xmlns="http://www.w3.org/2000/svg"
          className={`${className || ''} ${spin ? styles.spin : ''} ${rtl ? styles.rtl : ''}`.trim()}
          {...rest}
        >
          <g><defs><clipPath id="14b138__a"><rect x="3" y="3" width="12" height="12" rx="0"/></clipPath><clipPath id="14b138__b"><rect x="3" y="3" width="12" height="12" rx="0"/></clipPath></defs><rect width="18" height="18" rx="4" fill="#70A4FF"/><g clipPath="url(#14b138__a)"><g clipPath="url(#14b138__b)"><path d="m8.588 3.027 4.782 2.761a.199.199 0 0 1 .073.273l-.711 1.232a.2.2 0 0 1-.273.072l-4.782-2.76a.2.2 0 0 1-.073-.273l.71-1.232a.2.2 0 0 1 .274-.073Zm2.345 8.991c0 .***************.042a.629.629 0 0 1-.373.632l-4.64 2.028a.355.355 0 0 1-.472-.187.369.369 0 0 1 .022-.325l1.38-2.392.222.057a1.324 1.324 0 0 0 1.464-.63c.366-.636.158-1.442-.464-1.8a1.316 1.316 0 0 0-1.792.497c-.292.5-.226 1.13.164 1.559l.15.163-1.383 2.398a.35.35 0 0 1-.477.131.345.345 0 0 1-.17-.262l-.563-5.031a.628.628 0 0 1 .361-.64l.088-.04a6.91 6.91 0 0 0 2.988-2.687.587.587 0 0 1 .79-.206l1.66.958 1.634.952a.612.612 0 0 1 .225.821 6.918 6.918 0 0 0-.818 3.962ZM7.758 14.79v-.198c0-.117.095-.212.212-.212h5.817c.117 0 .211.095.211.212v.198a.211.211 0 0 1-.211.211H7.969a.211.211 0 0 1-.21-.211Z" fill="#FFF"/></g></g></g>
        </svg>
    )
}
