/*
 * @Description: 文件上传选择组件 - 选择附件对应的检查项
 * @Author: AI Assistant
 * @Date: 2025-01-10
 */
import React, { useState, useEffect } from 'react';
import { Popup, Button, Toast } from 'antd-mobile';
import { CloseOutline, CameraOutline, AddOutline } from 'antd-mobile-icons';
import PhotoCapture from '@/components/PhotoCapture';
import { beforeUpload } from '@/util/method';
import './index.less';


interface UploadedFile {
  file: File;
  dataUrl: string;
  id: string;
}

interface FileUploadSelectorProps {
  visible: boolean;
  onClose: () => void;
  checkItems: any; // 修改为any类型，支持taskData结构
  onUploadComplete: (checkItemId: string, files: UploadedFile[], imagerIds: string[]) => void;
  title?: string;
}

const FileUploadSelector: React.FC<FileUploadSelectorProps> = ({
  visible,
  onClose,
  checkItems,
  onUploadComplete,
  title = "请选择附件对应的检查项"
}) => {
  const [selectedCheckItem, setSelectedCheckItem] = useState<string>('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [showPhotoCapture, setShowPhotoCapture] = useState(false);
  const [showAddButton, setShowAddButton] = useState(false);
  const [imagerIds, setImagerIds] = useState<string[]>([]);

  useEffect(() => {
    console.log('checkItems', checkItems);
    console.log('uploadedFiles', uploadedFiles);
  }, [checkItems,uploadedFiles]);

  // 重置状态
  const resetState = () => {
    setSelectedCheckItem('');
    setUploadedFiles([]);
    setShowAddButton(false);
    setImagerIds([]);
    setShowPhotoCapture(false);
    
  };

  // 关闭弹窗
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 选择检查项
  const handleSelectCheckItem = (itemId: string) => {
    setSelectedCheckItem(itemId);
    setShowAddButton(true);
  };

  // 打开拍照组件
  const handleOpenPhotoCapture = () => {
    if (!selectedCheckItem) {
      Toast.show('请先选择检查项');
      return;
    }
    setShowPhotoCapture(true);
  };

  // 关闭拍照组件
  const handleClosePhotoCapture = () => {
    setShowPhotoCapture(false);
  };

  // 处理拍照完成
  const handlePhotoCaptured = (file: File, dataUrl: string) => {
    beforeUpload(file, (res: any) => {
      const newFile: UploadedFile = {
        file,
        dataUrl,
        id: res[0]?.id || Date.now().toString()
      };
      setImagerIds(prev => [...prev, res[0]?.id]);
      setUploadedFiles(prev => [...prev, newFile])
      setShowPhotoCapture(false);
      Toast.show('图片上传成功');
    });
  };

  // 删除已上传的文件
  const handleDeleteFile = (index: number) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  // 确认上传
  const handleConfirm = () => {
    if (!selectedCheckItem) {
      Toast.show('请选择检查项');
      return;
    }
    
    if (uploadedFiles.length === 0) {
      Toast.show('请至少上传一个文件');
      return;
    }
    onUploadComplete(selectedCheckItem, uploadedFiles, imagerIds);
    handleClose();
    Toast.show('文件上传完成');
  };

  // // 获取选中检查项的名称
  // const getSelectedCheckItemName = () => {
  //   const item = checkItemsList.find((item: any) => item.id === selectedCheckItem);
  //   return item?.name || '';
  // };

  return (
    <>
      <Popup
        visible={visible}
        onMaskClick={handleClose}
        position="bottom"
        bodyStyle={{
          borderTopLeftRadius: '12px',
          borderTopRightRadius: '12px',
          minHeight: '60vh',
          maxHeight: '80vh',
          padding: 0,
          display: 'flex',
          flexDirection: 'column'
        }}
      >
        <div className="file-upload-selector">
          {/* 头部 */}
          <div className="selector-header">
            <h3 className="selector-title">{title}</h3>
            <CloseOutline 
              className="close-btn" 
              onClick={handleClose}
            />
          </div>

          {/* 内容区域 */}
          <div className="selector-content">
            {/* 已上传的文件预览 */}
            {uploadedFiles.length > 0 && (
              <div className="uploaded-files-section">
                {uploadedFiles.map((file, index) => (
                  <div className="uploaded-file-item" key={file.id}>
                    <img
                      src={file.dataUrl}
                      alt="上传的图片"
                      className="uploaded-image"
                    />
                    <CloseOutline
                      className="delete-file-btn"
                      onClick={() => handleDeleteFile(index)}
                    />
                  </div>
                ))}
                {/* {uploadedFiles.length > 3 && (
                  <div className="more-files">
                    +{uploadedFiles.length - 3}
                  </div>
                )} */}
              </div>
            )}

            {/* 添加按钮 */}
            {showAddButton && (
              <div className="add-file-section">
                <div 
                  className="add-file-btn"
                  onClick={handleOpenPhotoCapture}
                >
                  <AddOutline />
                </div>
              </div>
            )}

            {/* 检查项选择区域 */}
            <div className="check-items-section">
              <div className="check-items-grid">
                {checkItems?.map((item: any) => (
                  <div
                    key={item.configItemId}
                    className={`check-item-card ${selectedCheckItem === item.versions[0].id ? 'selected' : ''}`}
                    onClick={() => handleSelectCheckItem(item.versions[0].id)}
                  >
                    <div className="check-item-name">{item.cateName}</div>
                    {/* <div className="check-item-info">
                      <span className="requirements-count">{item.requirementCount}项要求</span>
                    </div> */}
                  </div>
                ))}
              </div>
            </div>

            {/* 提示信息 */}
            {/* selectedCheckItem && (
              <div className="selected-info">
                <p>已选择：<span className="selected-name">{getSelectedCheckItemName()}</span></p>
                <p className="tip-text">如果数量很多就换行显示</p>
              </div>
            ) */}
          </div>

          {/* 底部按钮 */}
          <div className="selector-footer">
            <Button 
              className="cancel-btn"
              onClick={handleClose}
            >
              取消
            </Button>
            <Button 
              className="confirm-btn"
              color="primary"
              onClick={handleConfirm}
              disabled={!selectedCheckItem || uploadedFiles.length === 0}
            >
              确认
            </Button>
          </div>
        </div>
      </Popup>

      {/* 拍照组件 */}
      <PhotoCapture
        visible={showPhotoCapture}
        onClose={handleClosePhotoCapture}
        onImageCaptured={handlePhotoCaptured}
        title="拍照上传"
      />
    </>
  );
};

export default FileUploadSelector;
