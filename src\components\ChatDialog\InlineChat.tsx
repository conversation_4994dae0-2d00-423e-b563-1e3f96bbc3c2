/*
 * @Description: 内嵌式聊天组件 - 用于页面内直接显示，使用MobileQuestionsAnswerView作为底部输入
 * @Features:
 *   - 支持历史聊天记录加载和显示
 *   - 使用AnswerContent组件展示AI回复，支持插件和扩展功能
 *   - 自动在组件加载时获取历史记录
 *   - 每次AI回复完成后更新历史记录
 *   - 支持图片上传和文件附件
 * @Author: AI Assistant
 * @Date: 2025-01-10
 * @Updated: 2025-01-11 - 添加历史聊天记录功能
 */
import React, { useRef, useEffect, useState, forwardRef, useImperativeHandle } from 'react';
import { ProgressBar, Toast } from 'antd-mobile';
import AnswerContent from '../answer-content';
import MobileQuestionsAnswerView from '@/view/mobile-questions-answer-view/index.tsx';
import FileUploadSelector from '../FileUploadSelector';
import { createEventSourceHandler, getReqId } from '@/util/method';
import { initMobileInputFix, destroyMobileInputFix } from '@/util/mobile-input-fix';
import { apis } from '@/api/api';
import SupplementAttachmentPopup from './SupplementAttachmentPopup.jsx';
import './InlineChat.less';

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: number;
  // 扩展字段以支持answer-content组件
  plugins?: any[];
  sources?: any[];
  webSearches?: any[];
  chatAiCheck?: {
    status: number;
    records: string;
  };
  // 图片相关字段
  images?: {
    id: string;
    url: string;
    checkItemId: string;
    checkItemName: string;
  }[];
}

interface InlineChatProps {
  currentStep?: number;
  messages?: ChatMessage[];
  loading?: boolean;
  taskData?: any; // 任务数据，用于构建API请求
  ref?: React.RefObject<any>;
  onLoad?: () => void;
}

const InlineChat = forwardRef<any, InlineChatProps>(({
  messages = [],
  loading = false,
  taskData,
  onLoad,
}, ref) => {
  const messagesEndRef = useRef<HTMLDivElement>(null); // 消息列表底部
  const mobileQuestionsAnswerViewRef = useRef<any>(null); // MobileQuestionsAnswerView引用
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>(messages);
  const [isLoading, setIsLoading] = useState(false);
  const [currentAnswer, setCurrentAnswer] = useState('');
  const [showFileUploadSelector, setShowFileUploadSelector] = useState(false);
  const [clickItemId, setClickItemId] = useState<any>(null); // 点击的检查项id
  const [clickaiCheckId, setClickaiCheckId] = useState<any>(null); // 点击的检查项名称
  // 键盘状态管理
  const [isKeyboardVisible, setIsKeyboardVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  // 待发送的图片状态
  const [pendingImages, setPendingImages] = useState<{
    id: string;
    url: string;
    checkItemId: string;
    checkItemName: string;
    fileId: string;
  }[]>([]);

  // 补充附件弹窗状态
  const [supplementPopupVisible, setSupplementPopupVisible] = useState(false);
  const [supplementRecords, setSupplementRecords] = useState<any[]>([]);

  // 加载历史聊天记录
  const loadChatHistory = async (mergeWithCurrent = false) => {
    if (!taskData?.businessId) return;

    try {
      const res = await apis.chatApi.getDialogue({
        sessionId: taskData.businessId
      });
      console.log('getDialogue res:', res);

      if (res.code === 0 && res.data && res.data.length > 0) {
        // 转换历史记录为ChatMessage格式
        const historyMessages: ChatMessage[] = [];

        res.data.forEach((dialogItem: any) => {
          // 处理时间戳 - API返回的是数组格式 [2025, 7, 11, 13, 45, 32, 720]
          let timestamp = Date.now();
          if (dialogItem.createTime && Array.isArray(dialogItem.createTime)) {
            const [year, month, day, hour, minute, second, ms] = dialogItem.createTime;
            timestamp = new Date(year, month - 1, day, hour, minute, second, ms || 0).getTime();
          }

          // 根据senderType判断消息类型
          if (dialogItem.senderType === 'user') {
            // 用户消息
            const content = dialogItem.chatContents && dialogItem.chatContents[0]
              ? dialogItem.chatContents[0].content
              : '';

            // 处理图片附件
            let images: any[] | undefined = undefined;
            if (dialogItem.chatImages && dialogItem.chatImages.length > 0) {
              images = dialogItem.chatImages.map((img: any) => ({
                id: img.id || img.fileId,
                url: img.fileUrl || img.filePath,
                checkItemId: '',
                checkItemName: img.fileName || '上传图片'
              }));
            }

            // 只要有内容或图片就创建消息记录
            if (content || (images && images.length > 0)) {
              historyMessages.push({
                id: `history-user-${dialogItem.id}`,
                type: 'user',
                content: content,
                timestamp: timestamp,
                images: images
              });
            }
          } else if (dialogItem.senderType === 'assistant') {
            // AI助手消息
            const content = dialogItem.chatContents && dialogItem.chatContents[0]
              ? dialogItem.chatContents[0].content
              : '';

            if (content) {
              historyMessages.push({
                id: `history-ai-${dialogItem.id}`,
                type: 'assistant',
                content: content,
                timestamp: timestamp,
                // 添加插件和扩展数据支持
                plugins: dialogItem.chatPlugins || [],
                sources: dialogItem.chatSources || [],
                webSearches: dialogItem.chatWebSearches || [],
                chatAiCheck: dialogItem.chatAiCheck
              });

              // 调试日志：检查chatAiCheck数据
              if (dialogItem.chatAiCheck) {
                console.log('发现chatAiCheck数据:', dialogItem.chatAiCheck);
              }
            }
          }
        });

        // 按时间戳排序
        historyMessages.sort((a, b) => a.timestamp - b.timestamp);

        console.log('转换后的历史聊天记录:', historyMessages);
        console.log('原始API数据:', res.data);

        if (mergeWithCurrent) {
          // 合并模式：以历史记录为准，完全替换为历史记录
          // 因为历史记录是服务器的权威数据，包含了所有已保存的对话
          setChatMessages(historyMessages);
          console.log('使用历史记录替换当前消息:', historyMessages);
        } else {
          // 替换模式：直接设置历史消息（用于初始加载）
          setChatMessages(historyMessages);
          console.log('初始加载历史记录:', historyMessages);
        }
      } else if (!mergeWithCurrent) {
        // 如果没有历史记录且不是合并模式，清空消息列表
        setChatMessages([]);
      }
    } catch (error) {
      console.error('加载历史聊天记录失败:', error);
    }
  };

  useEffect(() => {
    loadChatHistory();
  }, [taskData?.businessId]);

  // 监听软键盘状态
  useEffect(() => {
    // const mobileInputFix = initMobileInputFix({
    //   inputContainerSelector: '.text-input-wrapper', // 输入框容器选择器
    //   enableAutoScroll: true, // 自动滚动
    //   scrollOffset: 30, // 滚动偏移量
    //   iosScrollReduction: 0.2, // iOS设备滚动减少系数，更保守的设置
    //   debug: true // 调试模式
    // });

    // // 监听键盘状态变化
    // const checkKeyboardStatus = () => {
    //   const status = mobileInputFix.getStatus();
    //   setIsKeyboardVisible(status.isKeyboardVisible);
    //   setKeyboardHeight(status.keyboardHeight);
    // };

    // // 定期检查键盘状态
    // const statusInterval = setInterval(checkKeyboardStatus, 200);

    // return () => {
    //   clearInterval(statusInterval);
    //   destroyMobileInputFix();
    // };
  }, []);
  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    sendToAI: (message: string, type: number) => {
      sendToAI(message, type);
    }
  }));

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    console.log('messages', chatMessages);
  }, [chatMessages]);

  // 同步外部messages
  useEffect(() => {
    setChatMessages(messages);
  }, [messages]);

  // 处理MobileQuestionsAnswerView的发送消息
  const handleMobileSend = (data: any) => {
    const message = data.questions ? data.questions.trim() : '';

    // 使用pendingImages中的图片（来自FileUploadSelector）
    const allImages = [...pendingImages];

    // 如果有文本或图片，发送消息
    if (message || allImages.length > 0) {
      if (allImages.length > 0) {
        const fileIds = allImages.map((img: any) => img.fileId);
        sendToAI(message, 3, fileIds, allImages); // type=3 表示对话模式
      } else {
        sendToAI(message, 3); // type=3 表示普通对话模式
      }
      // 清空待发送图片
      setPendingImages([]);
    }

    // 处理文件上传 - 当setBUploaded被触发时
    if (data.bUploaded) {
      setShowFileUploadSelector(true);
    }
  };


  // 处理补充附件按钮点击
  const handleSupplementAttachment = (chatAiCheck: any,itemId:string) => {
    try {
      const records = JSON.parse(chatAiCheck.records || '[]');
      setSupplementRecords(records);
      setSupplementPopupVisible(true);
      setClickaiCheckId(itemId);
    } catch (error) {
      console.error('解析补充附件记录失败:', error);
      Toast.show('数据格式错误');
    }
  };

  // 处理补充附件确认
  const handleSupplementConfirm = (uploadedFiles: { [recordId: number]: any[] }) => {
    console.log('补充附件上传完成:', uploadedFiles);
    const records = Object.keys(uploadedFiles)
    let params = []
    records.forEach(record => {
      const attachmentsArray = uploadedFiles[record].map((file: any) => (file.id))
      const attachments = attachmentsArray.length > 0 ? attachmentsArray.join(',') : ''
      params.push({
        id: record,
        attachments: attachments
      })
    })
    apis.ginkgoSystem.attachmentSubmit(params).then(res => {
      console.log('附件提交结果:', res);
      setSupplementPopupVisible(false);
      apis.chatApi.updateStatus({
        parentId: clickaiCheckId.split('-')[2]
      }).then(res => {
        console.log('更新状态结果:', res);
        const array = []
        setIsLoading(true);
        supplementRecords.forEach(record => {
          if(record.checkWay ===0) {
            array.push({
              id: record.id,
              attachments: params.find(item => item.id == record.id).attachments
            })
          }
        })
        if(array.length > 0) {
        // 问答
        const origin = window.location.origin;
        const reqId = getReqId();
        let url;
        // 选定结果补充附件为2
        const param = {
          type: 2,
          query: array.map(item => item.id).join(','),
          executionId: JSON.parse(sessionStorage.getItem('prameData') || '{}').newId,
          curentPlaceId: sessionStorage.getItem('stageId'),
          curentPlaceName: sessionStorage.getItem('stageName'),
        };
        const fileIds = array.map(item => item.attachments).join(',')
    
        url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData?.businessId}&question=${encodeURIComponent('')}&imageIds=${fileIds}&params=${encodeURIComponent(JSON.stringify(param))}&scene=ai_check`;
    
    
        const eventSourceHandler = createEventSourceHandler({
          url,
          qaList: [],
          onMessage: (qaList: any[], answer: string) => {
            console.log('AI回复:', { qaList, answer });
          },
          onComplete: (qaList: any[], answerId: string, questionId: string) => {
            console.log('AI回复完成:', { qaList, answerId, questionId });
            setCurrentAnswer('');
            // 延迟重新加载历史记录，以服务器数据为准
            setTimeout(() => {
              loadChatHistory(true); // 使用合并模式，实际上会完全替换为历史记录
              setIsLoading(false);
              onLoad?.();
            }, 1000); // 延迟2秒确保服务器端数据已更新并保存

          },
          onError: (error: any) => {
            console.error('AI回复错误:', error);
          }
        });
        eventSourceHandler.start();
        
        }else{
          setIsLoading(false);
          setCurrentAnswer('');
          setTimeout(() => {
            loadChatHistory(true); // 使用合并模式，实际上会完全替换为历史记录
          }, 1000); // 延迟2秒确保服务器端数据已更新并保存
        }
      })
    });

  };

  // 处理文件上传完成
  const handleFileUploadComplete = (checkItemId: string, files: any[], fileIds: string[]) => {
    setClickItemId(checkItemId);
    const checkItem = taskData.itemRespVOList.find(item => item.id === checkItemId);

    // 将图片添加到待发送列表，显示在底部
    const newImages = files.map((file, index) => ({
      id: `img-${Date.now()}-${index}`,
      url: file.dataUrl || file.url,
      checkItemId: checkItemId,
      checkItemName: checkItem?.name || `检查项 ${checkItemId}`,
      fileId: fileIds[index]
    }));
    setPendingImages(prev => [...prev, ...newImages]);
    setShowFileUploadSelector(false);

    // 上传完图片后，自动进入输入文字状态
    // 使用setTimeout确保DOM更新完成后再聚焦
    setTimeout(() => {
      if (mobileQuestionsAnswerViewRef.current) {
        mobileQuestionsAnswerViewRef.current.focusInput();
      }
    }, 100);

    Toast.show(`已添加 ${files.length} 张图片，请输入文字后发送`);
  };

  // 关闭文件上传选择器
  const handleCloseFileUploadSelector = () => {
    setShowFileUploadSelector(false);
  };

  // 发送消息到AI并处理实时回复
// 0：大模型（填报模式，选择大模型）
// 1：文字/语音（填报模式，选择文字、语音）
// 2：选定结果（对话模式，附件上传）
// 3：对话 （对话模式，除选择巡检场所时）
// 4：初始化 （对话模式，选择巡检场所时）
  const sendToAI = (message: string, type: number, fileIds: string[] = [], images: any[] = []) => {
    if (!taskData?.businessId) {
      Toast.show('缺少必要的任务数据');
      return;
    }

    setIsLoading(true);
    setCurrentAnswer('');

    // 先添加用户消息
    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: message,
      timestamp: Date.now(),
      images: images.length > 0 ? images.map(img => ({
        id: img.id,
        url: img.url,
        checkItemId: img.checkItemId,
        checkItemName: img.checkItemName
      })) : undefined
    };

    const origin = window.location.origin;
    const reqId = getReqId();
    let query;
    let url;
    if(type === 4) {
      query = taskData.id
    } else if(type === 3&&fileIds.length > 0) {
      query = clickItemId
    }else{
      query = ''
    }
    const params = {
      type: type,
      query:query.toString(),
      executionId: JSON.parse(sessionStorage.getItem('prameData') || '{}').newId,
      curentPlaceId: sessionStorage.getItem('stageId'),
      curentPlaceName: sessionStorage.getItem('stageName'),
    };

    url = `${origin}/ginkgo/chat-api/v2/chat/sendQuestion?reqId=${reqId}&sessionId=${taskData?.businessId}&question=${encodeURIComponent(message)}&imageIds=${fileIds.join(',')}&params=${encodeURIComponent(JSON.stringify(params))}&scene=ai_check`;

    // 创建AI回复消息的占位符
    const aiMessageId = `ai-${Date.now()}`;
    const aiMessage: ChatMessage = {
      id: aiMessageId,
      type: 'assistant',
      content: '',
      timestamp: Date.now()
    };

    // 同时添加用户消息和AI消息占位符，先检查重复
    setChatMessages(prev => {
      // 检查是否已存在相同内容的用户消息
      const isDuplicateUser = prev.some(msg =>
        msg.type === 'user' &&
        msg.content === message &&
        Math.abs(msg.timestamp - userMessage.timestamp) < 2000 // 2秒内的消息认为是重复
      );

      // 检查是否已存在相同ID的AI消息
      const isDuplicateAI = prev.some(msg => msg.id === aiMessage.id);

      if (isDuplicateUser && isDuplicateAI) {
        console.log('检测到重复消息，跳过添加');
        return prev;
      } else if (isDuplicateUser) {
        console.log('检测到重复用户消息，只添加AI消息');
        return [...prev, aiMessage];
      } else if (isDuplicateAI) {
        console.log('检测到重复AI消息，只添加用户消息');
        return [...prev, userMessage];
      }

      return [...prev, userMessage, aiMessage];
    });

    const eventSourceHandler = createEventSourceHandler({
      url,
      qaList: [],
      onMessage: (qaList: any[], answer: string) => {
        if (answer && answer.trim()) {
          // 过滤掉<wait>标签内容
          const cleanAnswer = answer.replace(/<wait>.*?<\/wait>/g, '').trim();

          if (cleanAnswer) {
            setCurrentAnswer(cleanAnswer);
            // 直接设置完整内容，而不是累加
            setChatMessages(prev =>
              prev.map(msg =>
                msg.id === aiMessageId
                  ? { ...msg, content: cleanAnswer }
                  : msg
              )
            );
          }
        }
      },
      onComplete: (qaList: any[], answerId: string, questionId: string) => {
        console.log('AI回复完成:', { qaList, answerId, questionId });
        setIsLoading(false);
        setCurrentAnswer('');

        // 处理插件数据和扩展信息
        if (qaList && qaList.length > 0) {
          const latestQA = qaList[qaList.length - 1];
          const answerData = latestQA.answer || {};
          const questionData = latestQA.question || {};

          // 更新AI消息，添加插件和扩展数据
          setChatMessages(prev =>
            prev.map(msg =>
              msg.id === aiMessageId
                ? {
                    ...msg,
                    plugins: questionData.chatPlugins || [],
                    sources: questionData.chatSources || [],
                    webSearches: questionData.chatWebSearches || []
                  }
                : msg
            )
          );
        }

        // 延迟重新加载历史记录，以服务器数据为准
        setTimeout(() => {
          loadChatHistory(true); // 使用合并模式，实际上会完全替换为历史记录
          onLoad?.();
        }, 2000); // 延迟2秒确保服务器端数据已更新并保存
      },
      onError: (error: any) => {
        // console.error('AI回复错误:', error);
        // setIsLoading(false);
        // setCurrentAnswer('');
        // Toast.show('AI回复失败，请重试');

        // // 移除失败的AI消息
        // setChatMessages(prev => prev.filter(msg => msg.id !== aiMessageId));
      }
    });
    

    eventSourceHandler.start();
  };

  return (
    <div
      className={`inline-chat ${isKeyboardVisible ? 'keyboard-visible' : ''}`}
      style={{
        paddingBottom: isKeyboardVisible ? `${keyboardHeight}px` : '0px'
      }}
    >

      {/* 消息列表 */}
      <div
        className="messages-container"
        style={{
          marginBottom: isKeyboardVisible ? `${Math.min(keyboardHeight, 300)}px` : '0px'
        }}
      >
        {chatMessages.map((message, index) => (
          <div key={message.id} className={`message-item ${message.type}`}>
            {(message.content || (message.images && message.images.length > 0))&& <div className={`message-bubble ${message.type}-bubble`}>
              {message.type === 'assistant' ? (
                <div>
                  <AnswerContent
                    answerItem={{
                      chatContents: [{ content: message.content || '' }],
                      chatPlugins: message.plugins || []
                    }}
                    questionItem={{
                      chatSources: message.sources || [],
                      chatWebSearches: message.webSearches || [],
                      chatPlugins: message.plugins || [],
                      chatAiCheck: message.chatAiCheck
                    }}
                    pluginsLoading={false}
                    loading={false}
                    sceneData={{ code: 'ai_check' }}
                    isLast={index === chatMessages.length - 1}
                  />

                  {/* 补充附件按钮 */}
                  {message.chatAiCheck && message.chatAiCheck.status === 0 && (
                    <div className="supplement-attachment-btn-container">
                      <button
                        className="supplement-attachment-btn"
                        onClick={() => handleSupplementAttachment(message.chatAiCheck,message.id)}
                      >
                        补充附件
                      </button>
                    </div>
                  )}
                </div>
               ) : (
                <div>
                  {/* 显示图片 */}
                  {message.images && message.images.length > 0 && (
                    <div className="message-images">
                      {message.images.map((img) => (
                        <div key={img.id} className="message-image-item">
                          <img src={img.url} alt="上传的图片" className="message-image" />
                          <div className="image-info">
                            <span className="check-item-name">{img.checkItemName}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                  {/* 显示文字内容 */}
                  {message.content && <span>{message.content}</span>}
                </div>
              )}
            </div>}
          </div>
        ))}

        {/* 加载状态 */}
        {(loading || isLoading) && (
          <div className="message-item assistant">
            <div className="message-bubble assistant-bubble">
              <div className="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* 待发送图片预览 */}
      {pendingImages.length > 0 && (
        <div className="pending-images-preview">
          <div className="pending-images-header">
            <span className="pending-images-title">待发送图片 ({pendingImages.length})</span>
            <span
              className="clear-all-btn"
              onClick={() => setPendingImages([])}
            >
              清空
            </span>
          </div>
          <div className="pending-images-list">
            {pendingImages.map((img, index) => (
              <div key={img.id} className="pending-image-item">
                <img src={img.url} alt="待发送图片" className="pending-image" />
                <div className="pending-image-info">
                  <span className="check-item-name">{img.checkItemName}</span>
                </div>
                <span
                  className="remove-image-btn"
                  onClick={() => setPendingImages(prev => prev.filter((_, i) => i !== index))}
                >
                  ×
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 底部输入区域 - 使用MobileQuestionsAnswerView */}
      <MobileQuestionsAnswerView
        ref={mobileQuestionsAnswerViewRef}
        sceneType="default"
        showBtn={false}
        showStop={loading}
        loading={loading}
        disabled={loading}
        onSend={handleMobileSend}
        onStopQuestion={() => {}}
        onReSendQuestion={() => {}}
        setSearchState={() => {}} // 设置搜索状态
        setOpenUpload={() => {setShowFileUploadSelector(true)}}
        taskData={taskData}
        hasPendingImages={pendingImages.length > 0}
      />

      {/* 文件上传选择器 */}
      <FileUploadSelector
        visible={showFileUploadSelector}
        onClose={handleCloseFileUploadSelector}
        checkItems={taskData.itemRespVOList}

        onUploadComplete={handleFileUploadComplete}
        title="请选择附件对应的检查项"
      />

      {/* 补充附件弹窗 */}
      <SupplementAttachmentPopup
        visible={supplementPopupVisible}
        onClose={() => setSupplementPopupVisible(false)}
        records={supplementRecords}
        onConfirm={handleSupplementConfirm}
      />
    </div>
  );
});

export default InlineChat;
