# iOS 软键盘抬起过高问题修复方案

## 🔍 问题分析

iOS设备上软键盘弹起时，输入框被抬起过高的问题主要原因：

1. **键盘高度检测不准确**：iOS的 `window.innerHeight` 变化可能不稳定
2. **滚动量计算过大**：按照Android的逻辑计算滚动量会导致过度调整
3. **CSS变换过度**：`transform` 的调整幅度太大
4. **延迟时间不足**：iOS键盘动画时间较长，检测时机不准确

## ✅ 修复方案

### 1. **键盘高度优化**

```typescript
private getKeyboardHeight(): number {
  const currentHeight = window.innerHeight;
  const calculatedHeight = Math.max(0, this.originalViewportHeight - currentHeight);
  
  // iOS设备的键盘高度检测优化
  const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
  
  if (isIOS) {
    // iOS设备使用更保守的键盘高度估算
    const estimatedHeight = this.originalViewportHeight * 0.35;
    
    // 如果计算出的高度过大，使用估算值
    if (calculatedHeight > estimatedHeight * 1.5) {
      return estimatedHeight;
    }
    
    // 如果计算出的高度太小，使用最小值
    if (calculatedHeight < 100 && this.isKeyboardVisible) {
      return estimatedHeight;
    }
  }
  
  return calculatedHeight;
}
```

### 2. **滚动量减少**

```typescript
// iOS设备减少滚动量，避免抬起太高
if (isIOS) {
  const reductionFactor = this.options.iosScrollReduction || 0.2; // 默认减少80%
  scrollAmount = scrollAmount * reductionFactor;
}
```

### 3. **CSS调整优化**

```css
/* iOS设备更保守的调整 */
@supports (-webkit-touch-callout: none) {
  .text-input-wrapper {
    transform: translateY(calc(-1 * var(--keyboard-height, 0px) / 6));
  }
  
  .messages-container {
    padding-bottom: calc(var(--keyboard-height, 0px) * 0.3 + 20px);
  }
}
```

### 4. **延迟时间调整**

```typescript
// iOS设备延迟更长时间，确保键盘完全弹起
const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
const delay = isIOS ? 500 : 300;

setTimeout(() => {
  this.handleKeyboardShow();
}, delay);
```

## 🎯 配置参数

### MobileInputFix 新增配置

```typescript
interface MobileInputFixOptions {
  // iOS设备滚动减少系数 (0-1之间，越小调整越保守)
  iosScrollReduction?: number; // 默认 0.2
}
```

### InlineChat 组件配置

```typescript
const mobileInputFix = initMobileInputFix({
  inputContainerSelector: '.text-input-wrapper',
  enableAutoScroll: true,
  scrollOffset: 30,
  iosScrollReduction: 0.2, // iOS设备只滚动20%的计算量
  debug: true
});
```

## 📱 iOS特殊处理策略

### 1. **键盘高度估算**
- 使用视口高度的35%作为标准键盘高度
- 当检测到的高度异常时，使用估算值
- 避免因检测不准确导致的过度调整

### 2. **滚动量控制**
- 默认减少80%的滚动量（`iosScrollReduction: 0.2`）
- 可根据实际效果调整减少系数
- 确保输入框可见但不会抬起过高

### 3. **CSS变换减少**
- `transform` 调整从 `/2` 改为 `/6`
- `padding-bottom` 从 `1倍` 改为 `0.3倍`
- 使用 `@supports (-webkit-touch-callout: none)` 精确识别iOS

### 4. **延迟优化**
- 焦点事件延迟从300ms增加到500ms
- 给iOS键盘动画更充分的时间
- 避免在动画过程中进行调整

## 🧪 测试方法

### 1. **使用测试页面**
打开 `ios-keyboard-test.html` 进行测试：
- 观察键盘弹起时的调整幅度
- 检查输入框是否可见且位置合理
- 验证不同iOS设备的兼容性

### 2. **实际应用测试**
在对话模式中测试：
- 点击输入框观察调整效果
- 输入长文本测试自适应高度
- 切换不同输入框测试连续操作

### 3. **调试信息**
开启 `debug: true` 查看日志：
```
iOS设备，减少滚动量 (系数: 0.2): 50px
iOS设备键盘高度过大，使用估算值: 280px
```

## 📊 效果对比

| 项目 | 修复前 | 修复后 |
|------|--------|--------|
| 滚动量 | 100% | 20% |
| CSS调整 | `/2` | `/6` |
| 键盘高度 | 检测值 | 估算值(35%) |
| 延迟时间 | 300ms | 500ms |

## 🔧 微调建议

如果仍然觉得抬起过高，可以进一步调整：

```typescript
// 更保守的设置
iosScrollReduction: 0.1, // 只滚动10%

// CSS中进一步减少
transform: translateY(calc(-1 * var(--keyboard-height, 0px) / 10));
```

如果觉得抬起不够，可以适当增加：

```typescript
// 稍微增加
iosScrollReduction: 0.3, // 滚动30%
```

## 🎯 最佳实践

1. **渐进式调整**：从最保守的设置开始，逐步调整到合适的效果
2. **多设备测试**：在不同尺寸的iPhone和iPad上测试
3. **用户反馈**：收集实际用户的使用反馈进行优化
4. **版本兼容**：考虑不同iOS版本的差异

现在iOS设备上的软键盘抬起过高问题应该得到了有效解决！
