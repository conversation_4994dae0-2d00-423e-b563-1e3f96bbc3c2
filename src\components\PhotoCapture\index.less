// PhotoCapture 组件样式

// .photo-capture-overlay {
//   position: fixed;
//   top: 0;
//   left: 0;
//   right: 0;
//   bottom: 0;
//   background: rgba(0, 0, 0, 0.8);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   z-index: 1000;
//   padding: 20px;
// }

// .photo-capture-container {
//   background: white;
//   border-radius: 12px;
//   width: 100%;
//   max-width: 400px;
//   max-height: 90vh;
//   box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
//   overflow: hidden;
//   display: flex;
//   flex-direction: column;
// }

.photo-capture-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;

  .close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      color: #333;
    }
  }

  .title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  .placeholder {
    width: 30px;
  }
}

.photo-capture-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

// 选择步骤
.select-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;

  .camera-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
    border: 2px dashed #ddd;
    border-radius: 8px;
    width: 100%;
    background: #fafafa;

    .camera-icon {
      font-size: 48px;
      margin-bottom: 10px;
    }

    p {
      margin: 0;
      color: #666;
      font-size: 16px;
    }
  }
}

// 预览步骤
.preview-step {
  display: flex;
  flex-direction: column;
  gap: 20px;

  .preview-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    border-radius: 8px;
    overflow: hidden;
    background: #f9f9f9;

    .preview-image {
      max-width: 100%;
      max-height: 300px;
      object-fit: contain;
      border-radius: 8px;
    }
  }
}

// 操作按钮
.action-buttons {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: auto;

  .photo-btn {
    width: 100px;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;

    &.primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover:not(:disabled) {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }

      &:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
      }
    }

    &.secondary {
      background: #f5f5f5;
      color: #666;
      border: 1px solid #ddd;

      &:hover {
        background: #e9e9e9;
        border-color: #bbb;
      }
    }
  }
}

// 响应式设计
@media (max-width: 480px) {
  .photo-capture-overlay {
    padding: 10px;
  }

  .photo-capture-container {
    max-width: none;
    width: 100%;
    max-height: 95vh;
  }

  .photo-capture-content {
    padding: 15px;
  }

  .select-step .camera-placeholder {
    min-height: 150px;

    .camera-icon {
      font-size: 36px;
    }

    p {
      font-size: 14px;
    }
  }

  .action-buttons .photo-btn {
    padding: 10px 12px;
    font-size: 14px;
  }
}

// Cropper.js 样式覆盖
.cropper-container {
  .cropper-view-box,
  .cropper-face {
    border-radius: 50%;
  }

  .cropper-line,
  .cropper-point {
    background-color: #4873FF;
  }

  .cropper-bg {
    background-image: none;
  }

  .cropper-modal {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

// 加载状态
.photo-btn:disabled {
  position: relative;

  &::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 