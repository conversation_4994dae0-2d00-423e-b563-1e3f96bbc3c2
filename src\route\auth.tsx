/*
 * @Author: 杨越 <EMAIL>
 * @Date: 2024-12-04 15:20:08
 * @LastEditors: 杨越 <EMAIL>
 * @LastEditTime: 2025-02-11 16:48:20
 * @FilePath: \react-h5-template\src\route\auth.tsx
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import React, {useEffect} from 'react';
import {useNavigate} from 'react-router-dom';

interface AuthRouteProps {
    children: React.ReactNode;
    auth?: boolean;
}

/**
 * 认证路由
 * @param children  子组件
 * @param auth  是否需要认证
 * @constructor 认证路由组件
 */
const AuthRoute: React.FC<AuthRouteProps> = ({children, auth}) => {
    const navigate = useNavigate();
    const token = sessionStorage.getItem('token'); // 或者其他认证令牌的获取方式
    const isAuthenticated = Boolean(token); // 认证逻辑

    useEffect(() => {
        
        if (auth && !isAuthenticated) {
            navigate('/login',{ replace: true }); // 如果未认证且路由需要认证，则重定向到登录
        }
    }, [auth, isAuthenticated, navigate]);

    return <>{children}</>;
};

export default AuthRoute;
