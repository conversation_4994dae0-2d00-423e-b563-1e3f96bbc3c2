/*
 * @Description: 信息来源
 * @Author: wangliang
 * @Date: 2024-08-08 15:45:48
 * @LastEditTime: 2025-06-26 13:46:49
 * @LastEditors: wl
 */
import { Popup } from 'antd-mobile';
import { useState, useContext } from "react"
import { CaretUpOutlined, CaretDownOutlined, UpCircleOutlined, DownCircleOutlined, DownloadOutlined } from "@ant-design/icons"
import { openFilePreview } from "@/util/util"
import MarkdownContent from "@/components/markdown-content";
import { projectPath } from '@/api/api';
// import { Context } from '@/redux/context';
import wx from 'weixin-js-sdk';
import style from "./InfomationSource.module.scss"
const InfomationSource = ({sourceDocuments}) => {
    // const { state } = useContext(Context);
    const [showList, setShowList] = useState(false)
    const [showContentList, setShowContentList] = useState([])
    const [preViewVisible, setPreViewVisible] = useState(false);
    const [url, setUrl] = useState('');

    const filePrevire = (item) => {

        const origin = window.location.origin
        const isWeChat = localStorage.getItem('isWeChat') === 'true';
        if(isWeChat) {
            const _url = `${origin}${projectPath}/upload/downLoadByUrl?url=${item.fileUrl}`;
            wx.miniProgram.navigateTo({
                url: `/pages/download/index?url=${encodeURIComponent(_url)}&fileName=${item.fileName}`
            });
        }else { 
            setPreViewVisible(true);
            setUrl(`${origin}/file-preview?file_url=${item.fileUrl}&file_name=${item.fileName}`)
            // if(state.isMobile) {
                
            // }else {
            //     openFilePreview(`/file-preview?file_url=${item.fileUrl}&file_name=${item.fileName}`)
            // }
        }
        
        
    }

    const getShowContentList = (show, i) => {
        let list = [...showContentList]
        if(show) {
            list = list.filter(index => index !== i)
        }else {
            list.push(i)
        }
        setShowContentList(list)
    }
    
    return (
        <div className={style['recomment-list']}>
            <div className={style['recomment-list-title']}>
                找到了{sourceDocuments.length}个信息来源
                <div className={style['list-title-icon']} onClick={()=>setShowList(!showList)}>{showList ? <CaretUpOutlined /> : <CaretDownOutlined />}</div>
            </div>
            {
                showList && <div className={style['list-container']}>
                    {
                        sourceDocuments.map((l, i) => {
                            const showContent = showContentList.findIndex(index => index === i) > -1
                            return <div key={l.id} className={style['recomment-item']} >
                                <div className={style['recomment-item-header']}>
                                    {/* <div className={style['recomment-item-title']}>数据来源{i + 1}:</div> */}
                                    <div className={style['recomment-item-file']} title={l.fileName} onClick={()=>filePrevire(l)}>{l.fileName}</div>
                                    {/* <div className={style['recomment-item-icon']} title="下载文件" onClick={()=>downLoadFile(l)}>
                                        <DownloadOutlined />
                                    </div> */}
                                    <div className={style['recomment-item-icon']} title="展开" onClick={()=>getShowContentList(showContent, i)}>
                                        {
                                            showContent ? <UpCircleOutlined style={{color: '#4873FF'}}/> : <DownCircleOutlined style={{color: '#4873FF'}}/>
                                        }
                                    </div>
                                </div>
                                {
                                    showContent && <div className={style['recomment-item-content']}>
                                        <MarkdownContent content={l.content || ''} parent={null} loading={false} sceneData={null} />
                                    </div>
                                }
                            </div>
                        })
                    }
                </div>
            }
            <Popup
                visible={preViewVisible}
                onMaskClick={() => {
                    setPreViewVisible(false);
                    setUrl('');
                }}
                position="bottom"
                bodyStyle={{ height: '88vh', width: '100vw' }}
            >
                <iframe
                    style={{
                        width: '200%',
                        height: '200%',
                        transformOrigin: 'left top',
                        transform: 'scale(0.5, 0.5)'
                    }}
                    src={url}
                ></iframe>
            </Popup>
        </div>
    )
}

export default InfomationSource