import React from 'react'
import <PERSON>actD<PERSON> from 'react-dom/client'
import {<PERSON><PERSON><PERSON><PERSON>out<PERSON>} from 'react-router-dom';
import App from '@/view/app/App.tsx'
import './index.css'
import './rem.js'

ReactDOM.createRoot(document.getElementById('root')!).render(
    // <React.StrictMode>
    //     <BrowserRouter>
    //             <App/>
    //     </BrowserRouter>
    // </React.StrictMode>
    <BrowserRouter>
        <App/>
    </BrowserRouter>,
)
