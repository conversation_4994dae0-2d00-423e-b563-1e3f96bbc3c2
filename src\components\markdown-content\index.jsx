/*
 * @Description: markdown
 * @Author: wangliang
 * @Date: 2025-02-11 17:30:46
 * @LastEditTime: 2025-06-25 09:52:04
 * @LastEditors: wl
 */
import React, { useState, useMemo } from 'react';
import { Image } from 'antd';
import { LoadingOutlined } from '@ant-design/icons';
// import ReactMarkdown from 'react-markdown'
import Markdown from 'markdown-to-jsx';
import { getHtml } from '@/util/util';
import videoStudySvg from "@/assets/images/chat/icon_video_study.svg"
import style from "./MarkdownContent.module.scss"

const MarkdownContent = ({ content, parent, loading, sceneData }) => {
    const [imgView, setImgView] = useState('')

    

    const customImage = ({ src, alt, ...props }) => {
        return <img src={src} style={{ width: '40%' }} onClick={() => setImgView(src)} />
    }

    const customTable = ({children}) => {
        return <div className={style['markdown-table']}>
            <table>
                {children}
            </table>
        </div>
    }

    const customThink = ({children}) => {
        return <div className={style['markdown-think']}>
            {/* <ThinkContent children={children} sceneData={sceneData}/> */}
        </div>
    }


    const customLink = ({ href, children }) => {
        const videoRegex = /\.(mp4|mov|ogg|webm)$/i;

        if (href && videoRegex.test(href)) {
            let type;
            if (href.endsWith('.mp4')) {
                type = 'video/mp4';
            } else if (href.endsWith('.mov')) {
                type = 'video/quicktime';
            } else if (href.endsWith('.ogg')) {
                type = 'video/ogg';
            } else if (href.endsWith('.webm')) {
                type = 'video/webm';
            } else {
                type = 'video/mp4'; // 默认值
            }

            

            return loading ? <img src={videoStudySvg} alt="" /> : <video key={href} controls>
                    <source src={href} type={type} />
                    Your browser does not support the video tag.
                </video>
            
        }

        return <a href={href} target="_blank" rel="noopener noreferrer">{children}</a>;
    }

    // 自定义 <sup> 标签组件
    const customSup = ({ node, ...props }) => {
        return (
        <sup
            {...props}
            style={{ cursor: 'help' }}
            title={props.title || ''}
        >
            {props.children}
        </sup>
        );
    };

    const customWait = (e) => {
        return <div className={style['wait-content']}><i style={{marginRight: '0.5rem'}}/>{e.children}</div>
    }

    const markdownComponents = useMemo(() => ({
        sup: customSup,
        blockquote: customThink,
        img: customImage,
        table: customTable,
        a: customLink,
        
    }), [customThink, customImage, customTable, customLink]);

    return <div className={style['markdown-content']}>
        {/* <ReactMarkdown children={getHtml(content, parent)} components={markdownComponents} rehypePlugins={[rehypeRaw]} remarkPlugins={[[gfm]]} /> */}
        <Markdown
            options={{
                overrides: {
                    sup: customSup,
                    // blockquote: customThink,
                    think: customThink,
                    img: customImage,
                    table: customTable,
                    a: customLink,
                    wait: customWait
                },
            }}
        >
            {getHtml(content, parent)}
        </Markdown>
        {
            !!imgView && <Image
            preview={{
                visible: !!imgView,
                src: imgView,
                onVisibleChange: (value) => {
                    setImgView('');
                },
            }}
        />
        }
        
    </div>
}

export default MarkdownContent